# yaml-language-server: $schema=https://aka.ms/teams-toolkit/v1.3/yaml.schema.json
# Visit https://aka.ms/teamsfx-v5.0-guide for details on this file
# Visit https://aka.ms/teamsfx-actions for details on actions
version: v1.3

provision:
  # Creates a Teams app
  - uses: teamsApp/create
    with:
      # Teams app name
      name: ProjectManager-${{APP_NAME_SUFFIX}}
    # Write the information of created resources into environment file for
    # the specified environment variable(s).
    writeToEnvironmentFile: 
      teamsAppId: TEAMS_APP_ID

  # Create or reuse an existing Azure Active Directory application for bot.
  - uses: botAadApp/create
    with:
      # The Azure Active Directory application's display name
      name: AuthenticationTest${{APP_NAME_SUFFIX}}
    writeToEnvironmentFile:
      # The Azure Active Directory application's client id created for bot.
      botId: BOT_ID
      # The Azure Active Directory application's client secret created for bot.
      botPassword: SECRET_BOT_PASSWORD 

  # Create or update the bot registration on dev.botframework.com
  - uses: botFramework/create
    with:
      botId: ${{BOT_ID}}
      name: AuthenticationTest
      # messagingEndpoint: ${{BOT_ENDPOINT}}/api/messages
      messagingEndpoint: https://better-louse-centrally.ngrok-free.app/api/messages
      description: ""
      channels:
        - name: msteams

  # Validate using manifest schema
  - uses: teamsApp/validateManifest
    with:
      # Path to manifest template
      manifestPath: ./appPackage/manifest.json

  # Build Teams app package with latest env value
  - uses: teamsApp/zipAppPackage
    with:
      # Path to manifest template
      manifestPath: ./appPackage/manifest.json
      outputZipPath: ./appPackage/build/appPackage.${{TEAMSFX_ENV}}.zip
      outputJsonPath: ./appPackage/build/manifest.${{TEAMSFX_ENV}}.json
  # Validate app package using validation rules
  - uses: teamsApp/validateAppPackage
    with:
      # Relative path to this file. This is the path for built zip file.
      appPackagePath: ./appPackage/build/appPackage.${{TEAMSFX_ENV}}.zip

  # Apply the Teams app manifest to an existing Teams app in
  # Teams Developer Portal.
  # Will use the app id in manifest file to determine which Teams app to update.
  - uses: teamsApp/update
    with:
      # Relative path to this file. This is the path for built zip file.
      appPackagePath: ./appPackage/build/appPackage.${{TEAMSFX_ENV}}.zip

deploy:
  # Defines what the `deploy` lifecycle step does with Teams Toolkit.
  # Runs after `provision` during Start Debugging (F5) or run manually using `teamsfx deploy --env local`.
  # Provides the Teams Toolkit .env file values to the apps runtime so they can be accessed with `process.env`.
  - uses: file/createOrUpdateEnvironmentFile
    with:
      target: ./.env
      envs:
        BOT_ID: ${{BOT_ID}}
        BOT_PASSWORD: ${{SECRET_BOT_PASSWORD}}
        BOT_ENDPOINT: ${{BOT_ENDPOINT}}
        JIRA_CLIENT_ID: ${{JIRA_CLIENT_ID}}
        SECRET_JIRA: ${{SECRET_JIRA}}

