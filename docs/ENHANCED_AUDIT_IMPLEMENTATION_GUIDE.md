# Enhanced Audit System Implementation Guide

## Overview

This guide provides comprehensive documentation for the enhanced audit system that has been implemented to improve logging and auditing infrastructure with advanced privacy controls, text sanitization, and GDPR compliance features.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Text Sanitization System](#text-sanitization-system)
3. [Enhanced Audit Service](#enhanced-audit-service)
4. [Privacy Controls and Data Retention](#privacy-controls-and-data-retention)
5. [Enhanced Decorators](#enhanced-decorators)
6. [Enhanced Middleware](#enhanced-middleware)
7. [Configuration](#configuration)
8. [Usage Examples](#usage-examples)
9. [Testing](#testing)
10. [Migration Guide](#migration-guide)
11. [Performance Considerations](#performance-considerations)
12. [Security and Compliance](#security-and-compliance)

## Architecture Overview

The enhanced audit system consists of several key components:

```
┌─────────────────────────────────────────────────────────────┐
│                    Enhanced Audit System                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Text Sanitizer  │  │ Privacy Config  │  │ Data Classifier │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │Enhanced Audit   │  │ Performance     │  │ Retention       │ │
│  │Service          │  │ Monitor         │  │ Manager         │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐                    │
│  │Enhanced         │  │Enhanced         │                    │
│  │Decorators       │  │Middleware       │                    │
│  └─────────────────┘  └─────────────────┘                    │
└─────────────────────────────────────────────────────────────┘
```

## Text Sanitization System

### Overview

The text sanitization system automatically detects and sanitizes personally identifiable information (PII) and sensitive data in user inputs and system logs.

### Supported PII Patterns

- **Email addresses**: `<EMAIL>` → `j***@e******.com`
- **Phone numbers**: `(*************` → `(***) ***-4567`
- **Credit card numbers**: `4532 1234 5678 9012` → `[REDACTED:CREDIT_CARD]`
- **Social Security Numbers**: `***********` → `[REDACTED:SSN]`
- **API keys**: `sk_test_1234...` → `[HASH:a1b2c3d4]` or `[REDACTED:API_KEY]`
- **Passwords**: `password: secret123` → `[REDACTED:PASSWORD]`
- **IP addresses**: `***********` → Detected and classified
- **URLs**: `https://example.com` → Detected and classified

### Sensitivity Levels

```python
from src.utils.text_sanitizer import SensitivityLevel

# Available sensitivity levels
SensitivityLevel.PUBLIC       # No sensitive data detected
SensitivityLevel.INTERNAL     # Some sensitive keywords or patterns
SensitivityLevel.CONFIDENTIAL # PII like emails, phones detected
SensitivityLevel.RESTRICTED   # High-risk data like credit cards, passwords
```

### Sanitization Strategies

```python
from src.utils.text_sanitizer import SanitizationStrategy

SanitizationStrategy.MASK      # Replace with asterisks, preserve structure
SanitizationStrategy.HASH      # Replace with SHA-256 hash
SanitizationStrategy.REDACT    # Complete removal with placeholder
SanitizationStrategy.TOKENIZE  # Replace with reversible token
SanitizationStrategy.PRESERVE  # Keep original content
```

### Basic Usage

```python
from src.utils.text_sanitizer import sanitize_text, ContentType, SensitivityLevel

# Sanitize user message
content = "My <NAME_EMAIL> and phone is (*************"
sanitized = sanitize_text(content, ContentType.USER_MESSAGE)

print(f"Original: {content}")
print(f"Sanitized: {sanitized.sanitized_content}")
print(f"Sensitivity: {sanitized.sensitivity_level}")
print(f"Patterns detected: {sanitized.detected_patterns}")
print(f"Sanitization applied: {sanitized.sanitization_applied}")
```

### Custom Pattern Addition

```python
from src.utils.text_sanitizer import get_default_sanitizer

sanitizer = get_default_sanitizer()

# Add custom employee ID pattern
sanitizer.add_custom_pattern('employee_id', r'EMP\d{6}')

# Add custom account number pattern
sanitizer.add_custom_pattern('account_number', r'ACC-\d{8}')
```

### Dictionary Sanitization

```python
from src.utils.text_sanitizer import sanitize_parameters

parameters = {
    'username': '<EMAIL>',
    'password': 'secretpassword',
    'description': 'Create new ticket',
    'phone': '************'
}

sanitized_params, sanitization_log = sanitize_parameters(parameters)
print(f"Sanitized keys: {sanitization_log}")
```

## Enhanced Audit Service

### Overview

The Enhanced Audit Service extends the base audit service with privacy controls, automatic sanitization, performance monitoring, and GDPR compliance features.

### Key Features

- **Automatic Sanitization**: All logged content is automatically sanitized based on sensitivity level
- **Performance Monitoring**: Track operation performance and generate alerts
- **Data Retention**: Configurable retention policies based on data sensitivity
- **GDPR Compliance**: User data export and deletion capabilities
- **Metadata Tracking**: Store sanitization metadata for audit trails

### Basic Usage

```python
from src.services.enhanced_audit_service import (
    get_enhanced_audit_service,
    PrivacyConfig,
    SensitivityLevel
)

# Get enhanced audit service with default privacy config
audit_service = get_enhanced_audit_service()

# Log user message with automatic sanitization
await audit_service.log_user_message_enhanced(
    user_id='user123',
    user_name='John Doe',
    message_content='My <NAME_EMAIL>',
    sensitivity_level=SensitivityLevel.CONFIDENTIAL
)

# Log action execution with parameter sanitization
await audit_service.log_action_execution_enhanced(
    action_name='create_ticket',
    user_id='user123',
    user_name='John Doe',
    parameters={
        'summary': 'New ticket',
        'email': '<EMAIL>',
        'password': 'secret123'
    }
)
```

### Performance Monitoring

```python
# Get performance metrics
metrics = audit_service.get_performance_metrics()
print(f"Total operations: {metrics['total_operations']}")
print(f"Average duration: {metrics['average_duration_ms']:.2f}ms")
print(f"Error rate: {metrics['error_rate']:.2%}")
print(f"Sanitization operations: {metrics['sanitization_operations']}")
```

### GDPR Compliance

```python
# Export user data (GDPR Article 20 - Right to data portability)
user_data = await audit_service.export_user_data('user123')
print(f"Exported {user_data['total_audit_logs']} audit logs")
print(f"Exported {user_data['total_sessions']} sessions")

# Delete user data (GDPR Article 17 - Right to be forgotten)
deletion_stats = await audit_service.delete_user_data('user123')
print(f"Deleted {deletion_stats['audit_logs_deleted']} audit logs")
print(f"Deleted {deletion_stats['sessions_deleted']} sessions")

# Clean up expired data based on retention policies
cleanup_stats = await audit_service.cleanup_expired_data()
print(f"Total deleted: {cleanup_stats['total_deleted']}")
```

## Privacy Controls and Data Retention

### Privacy Configuration

```python
from src.services.enhanced_audit_service import PrivacyConfig, RetentionPolicy

# Create custom privacy configuration
privacy_config = PrivacyConfig()

# Enable/disable sanitization
privacy_config.enable_sanitization = True
privacy_config.sanitize_user_messages = True
privacy_config.sanitize_bot_responses = True
privacy_config.sanitize_action_parameters = True

# Configure GDPR compliance
privacy_config.enable_gdpr_compliance = True
privacy_config.mask_pii_in_logs = True

# Performance settings
privacy_config.enable_performance_monitoring = True
privacy_config.performance_alert_threshold_ms = 5000

# Use custom privacy config
audit_service = get_enhanced_audit_service(privacy_config)
```

### Retention Policies

```python
from src.utils.sql.models import AuditEventType

# Default retention policies by event type and sensitivity
retention_policies = {
    (AuditEventType.USER_MESSAGE, SensitivityLevel.PUBLIC): RetentionPolicy.LONG_TERM,      # 1 year
    (AuditEventType.USER_MESSAGE, SensitivityLevel.INTERNAL): RetentionPolicy.MEDIUM_TERM,  # 90 days
    (AuditEventType.USER_MESSAGE, SensitivityLevel.CONFIDENTIAL): RetentionPolicy.SHORT_TERM, # 30 days
    (AuditEventType.USER_MESSAGE, SensitivityLevel.RESTRICTED): RetentionPolicy.IMMEDIATE,   # Delete immediately
}

# Custom retention periods
retention_periods = {
    RetentionPolicy.IMMEDIATE: 0,      # Delete immediately
    RetentionPolicy.SHORT_TERM: 30,    # 30 days
    RetentionPolicy.MEDIUM_TERM: 90,   # 90 days
    RetentionPolicy.LONG_TERM: 365,    # 1 year
    RetentionPolicy.PERMANENT: -1,     # Never delete
}
```

## Enhanced Decorators

### Enhanced Action Decorator

```python
from src.utils.enhanced_audit_decorators import enhanced_audit_action
from src.utils.text_sanitizer import SensitivityLevel

@enhanced_audit_action(
    action_name='create_ticket',
    sensitivity_level=SensitivityLevel.INTERNAL
)
async def create_ticket(context, state):
    """Create a new ticket with enhanced audit logging."""
    # Extract parameters
    summary = context.data.get('summary')
    description = context.data.get('description')

    # Your action logic here
    ticket_id = await create_jira_ticket(summary, description)

    return f'Ticket {ticket_id} created successfully'
```

### Enhanced Message Handler Decorator

```python
from src.utils.enhanced_audit_decorators import enhanced_audit_message_handler

@enhanced_audit_message_handler(
    handler_name='user_message_handler',
    sensitivity_level=SensitivityLevel.CONFIDENTIAL
)
async def on_message_activity(context, state):
    """Handle user messages with enhanced audit logging."""
    user_message = context.activity.text

    # Your message handling logic here
    response = await process_user_message(user_message)

    # Send response (automatically logged by EnhancedAuditTurnContext)
    await context.send_activity(response)

    return state
```

### Enhanced Turn Context

```python
from src.utils.enhanced_audit_decorators import create_enhanced_audit_context

async def message_handler(context, state):
    # Create enhanced audit context wrapper
    enhanced_context = create_enhanced_audit_context(
        context,
        session_id='session123',
        sensitivity_level=SensitivityLevel.INTERNAL
    )

    # Use enhanced context for automatic bot response logging
    await enhanced_context.send_activity('This response will be automatically logged')

    return state
```

## Enhanced Middleware

### API Request/Response Logging

```python
from src.utils.enhanced_audit_middleware import create_enhanced_audit_middleware
from aiohttp import web

# Create enhanced audit middleware
privacy_config = PrivacyConfig()
enhanced_middleware = create_enhanced_audit_middleware(privacy_config)

# Add to aiohttp application
app = web.Application(middlewares=[enhanced_middleware])
```

### Bot Message Middleware

```python
from src.utils.enhanced_audit_middleware import enhanced_messages_handler

async def setup_bot_middleware(app):
    """Setup enhanced audit middleware for bot messages."""

    async def bot_message_wrapper(request):
        return await enhanced_messages_handler(
            request,
            original_handler,
            privacy_config
        )

    app.router.add_post('/api/messages', bot_message_wrapper)
```

### Enhanced Logging Setup

```python
from src.utils.enhanced_audit_middleware import setup_enhanced_audit_logging

# Setup enhanced audit logging with privacy controls
setup_enhanced_audit_logging(privacy_config)
```

## Configuration

### Environment Variables

```bash
# Environment configuration
ENVIRONMENT=production
DATABASE_URL=your_database_url

# Audit configuration
AUDIT_ENABLE_SANITIZATION=true
AUDIT_ENABLE_GDPR_COMPLIANCE=true
AUDIT_PERFORMANCE_THRESHOLD_MS=5000
AUDIT_LOG_RETENTION_DAYS=90

# Privacy configuration
PRIVACY_MASK_PII=true
PRIVACY_HASH_SENSITIVE_DATA=true
PRIVACY_DEFAULT_SENSITIVITY=internal
```

### Application Configuration

```python
import os
from src.services.enhanced_audit_service import PrivacyConfig, get_enhanced_audit_service

def create_audit_config():
    """Create audit configuration from environment variables."""
    privacy_config = PrivacyConfig()

    # Load from environment
    privacy_config.enable_sanitization = os.getenv('AUDIT_ENABLE_SANITIZATION', 'true').lower() == 'true'
    privacy_config.enable_gdpr_compliance = os.getenv('AUDIT_ENABLE_GDPR_COMPLIANCE', 'true').lower() == 'true'
    privacy_config.performance_alert_threshold_ms = int(os.getenv('AUDIT_PERFORMANCE_THRESHOLD_MS', '5000'))

    return privacy_config

# Initialize enhanced audit service
privacy_config = create_audit_config()
audit_service = get_enhanced_audit_service(privacy_config)
```

## Usage Examples

### Example 1: User Message with PII

```python
# User sends: "My <NAME_EMAIL> and I need help"
await audit_service.log_user_message_enhanced(
    user_id='user123',
    user_name='John Doe',
    message_content='My <NAME_EMAIL> and I need help',
    session_id='session456'
)

# Logged as: "My email is j***@e******.com and I need help"
# Sensitivity: CONFIDENTIAL
# Patterns detected: ['email']
```

### Example 2: Action with Sensitive Parameters

```python
@enhanced_audit_action('reset_password')
async def reset_password(context, state):
    # Parameters contain sensitive data
    email = context.data.get('email')
    new_password = context.data.get('new_password')

    # Automatically sanitized in audit logs:
    # email: "j***@e******.com"
    # new_password: "[REDACTED:PASSWORD]"

    await perform_password_reset(email, new_password)
    return 'Password reset successfully'
```

### Example 3: Bot Response Logging

```python
@enhanced_audit_message_handler()
async def handle_user_query(context, state):
    user_query = context.activity.text

    # Process query
    if 'account balance' in user_query.lower():
        response = f"Your account balance is $1,234.56"
        # This response will be automatically sanitized if it contains PII
    else:
        response = "I can help you with account information"

    # Send response - automatically logged with sanitization
    await context.send_activity(response)

    return state
```

### Example 4: Performance Monitoring

```python
# Monitor slow operations
metrics = audit_service.get_performance_metrics()

if metrics['average_duration_ms'] > 1000:
    logger.warning(f"Audit operations are slow: {metrics['average_duration_ms']:.2f}ms average")

if metrics['error_rate'] > 0.05:  # 5% error rate
    logger.error(f"High audit error rate: {metrics['error_rate']:.2%}")

# Check recent operations
for operation in metrics['recent_operations']:
    if not operation['success']:
        logger.error(f"Failed operation: {operation}")
```

## Testing

### Running Tests

```bash
# Run all enhanced audit tests
pytest tests/utils/test_enhanced_audit_system.py -v

# Run specific test categories
pytest tests/utils/test_enhanced_audit_system.py::TestTextContentSanitizer -v
pytest tests/utils/test_enhanced_audit_system.py::TestEnhancedAuditService -v
pytest tests/utils/test_enhanced_audit_system.py::TestPrivacyConfig -v

# Run with coverage
pytest tests/utils/test_enhanced_audit_system.py --cov=src.utils.text_sanitizer --cov=src.services.enhanced_audit_service
```

### Test Categories

1. **Text Sanitization Tests**: PII detection, masking, redaction, custom patterns
2. **Enhanced Audit Service Tests**: Logging, performance monitoring, GDPR compliance
3. **Privacy Configuration Tests**: Retention policies, sanitization settings
4. **Decorator Tests**: Action and message handler decorators
5. **Integration Tests**: End-to-end audit flows

### Example Test

```python
import pytest
from src.utils.text_sanitizer import sanitize_text, ContentType

def test_email_sanitization():
    content = "Contact <NAME_EMAIL>"
    sanitized = sanitize_text(content, ContentType.USER_MESSAGE)

    assert sanitized.sanitization_applied
    assert 'email' in sanitized.detected_patterns
    assert 'j***@e******.com' in sanitized.sanitized_content
```

## Migration Guide

### From Basic Audit to Enhanced Audit

1. **Update Imports**:
```python
# Old
from src.services.audit_service import AuditService
from src.utils.audit_decorators import audit_action

# New
from src.services.enhanced_audit_service import get_enhanced_audit_service
from src.utils.enhanced_audit_decorators import enhanced_audit_action
```

2. **Update Service Initialization**:
```python
# Old
audit_service = AuditService()

# New
privacy_config = PrivacyConfig()
audit_service = get_enhanced_audit_service(privacy_config)
```

3. **Update Decorators**:
```python
# Old
@audit_action("create_ticket")
async def create_ticket(context, state):
    pass

# New
@enhanced_audit_action("create_ticket", sensitivity_level=SensitivityLevel.INTERNAL)
async def create_ticket(context, state):
    pass
```

4. **Update Middleware**:
```python
# Old
from src.utils.audit_middleware import create_audit_middleware
middleware = create_audit_middleware()

# New
from src.utils.enhanced_audit_middleware import create_enhanced_audit_middleware
middleware = create_enhanced_audit_middleware(privacy_config)
```

### Backward Compatibility

The enhanced audit system maintains backward compatibility with the existing audit system:

- Existing audit decorators continue to work
- Database schema is compatible
- Existing audit logs are preserved
- API interfaces are maintained

## Performance Considerations

### Sanitization Performance

- **Caching**: Content classification results are cached for 1 hour
- **Batch Processing**: Multiple patterns processed efficiently
- **Memory Usage**: Sanitization metadata stored in memory with cleanup
- **Regex Optimization**: Compiled patterns for better performance

### Database Performance

- **Connection Pooling**: Reuse database connections
- **Batch Operations**: Group multiple audit logs when possible
- **Index Optimization**: Proper indexes for common queries
- **Retention Cleanup**: Automated cleanup of expired data

### Monitoring and Alerts

```python
# Performance monitoring configuration
privacy_config.enable_performance_monitoring = True
privacy_config.performance_alert_threshold_ms = 5000  # Alert if > 5 seconds

# Check performance metrics
metrics = audit_service.get_performance_metrics()
if metrics['average_duration_ms'] > 1000:
    # Take action for slow operations
    pass
```

### Optimization Tips

1. **Adjust Sensitivity Levels**: Higher sensitivity = more processing
2. **Configure Retention**: Shorter retention = better performance
3. **Batch Cleanup**: Run cleanup during off-peak hours
4. **Monitor Metrics**: Regular performance monitoring
5. **Cache Tuning**: Adjust cache TTL based on usage patterns

## Security and Compliance

### Data Protection

- **Encryption at Rest**: Database encryption for sensitive audit data
- **Encryption in Transit**: TLS for all database connections
- **Access Controls**: Role-based access to audit logs
- **Audit Trail**: Complete audit trail for all data access

### Privacy Compliance

- **GDPR Article 17**: Right to be forgotten (data deletion)
- **GDPR Article 20**: Right to data portability (data export)
- **Data Minimization**: Collect only necessary information
- **Purpose Limitation**: Use data only for stated purposes
- **Consent Management**: Track and respect user consent

### Security Measures

- **Input Validation**: Comprehensive input validation and sanitization
- **SQL Injection Prevention**: Parameterized queries and ORM usage
- **Log Injection Prevention**: Sanitize log content to prevent injection
- **Access Logging**: Log all access to sensitive audit data

### Compliance Features

```python
# GDPR compliance example
async def handle_gdpr_request(user_id: str, request_type: str):
    if request_type == 'export':
        # Article 20 - Right to data portability
        user_data = await audit_service.export_user_data(user_id)
        return user_data

    elif request_type == 'delete':
        # Article 17 - Right to be forgotten
        deletion_stats = await audit_service.delete_user_data(user_id)
        return deletion_stats

    elif request_type == 'cleanup':
        # Automated data retention compliance
        cleanup_stats = await audit_service.cleanup_expired_data()
        return cleanup_stats
```

## Troubleshooting

### Common Issues

1. **Sanitization Not Working**:
   - Check `privacy_config.enable_sanitization = True`
   - Verify patterns are correctly configured
   - Check sensitivity level settings

2. **Performance Issues**:
   - Monitor performance metrics
   - Adjust cache TTL settings
   - Review retention policies
   - Check database indexes

3. **GDPR Compliance Issues**:
   - Verify `privacy_config.enable_gdpr_compliance = True`
   - Check retention policies configuration
   - Ensure cleanup jobs are running

4. **Missing Audit Logs**:
   - Check decorator configuration
   - Verify middleware setup
   - Review error logs for failures

### Debug Mode

```python
# Enable debug logging for troubleshooting
import logging
logging.getLogger('src.utils.text_sanitizer').setLevel(logging.DEBUG)
logging.getLogger('src.services.enhanced_audit_service').setLevel(logging.DEBUG)

# Check sanitization metadata
event_id = 'your_event_id'
metadata = audit_service.get_sanitization_metadata(event_id)
print(f"Sanitization metadata: {metadata}")
```

## Conclusion

The enhanced audit system provides enterprise-grade logging and auditing capabilities with strong privacy controls, comprehensive monitoring, and flexible configuration options. It maintains backward compatibility while adding powerful new features for data protection and compliance.

For additional support or questions, please refer to the test suite and example implementations provided in the codebase.