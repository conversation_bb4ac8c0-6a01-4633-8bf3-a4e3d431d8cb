# Comprehensive Audit Mechanism

This document describes the comprehensive audit mechanism implemented for the bot framework, providing detailed logging and tracking of all user interactions, bot actions, system events, and errors.

## Overview

The audit mechanism consists of several components working together to provide complete visibility into system operations:

1. **Database Schema** - Structured storage for audit events and user sessions
2. **Audit Services** - Core services for logging different types of events
3. **Audit Decorators** - Automatic audit logging for action functions
4. **Audit Middleware** - API request/response logging
5. **Enhanced Logging** - Structured logging with Australia/Sydney timezone

## Database Schema

### AuditLog Table

Stores comprehensive audit information for all events:

```sql
CREATE TABLE [model].[AuditLog] (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    event_id VARCHAR(36) UNIQUE NOT NULL,  -- UUID
    event_type VARCHAR(50) NOT NULL,       -- user_message, bot_response, action_execution, etc.
    event_status VARCHAR(20) NOT NULL,     -- success, failure, warning, in_progress
    
    -- User and session information
    user_id VARCHAR(255),
    user_name VARCHAR(255),
    session_id VARCHAR(255),
    
    -- Request/Response information
    request_id VARCHAR(36),
    activity_id VARCHAR(255),
    channel_id VARCHAR(255),
    
    -- Event details
    action_name VARCHAR(255),
    endpoint VARCHAR(255),
    method VARCHAR(10),
    
    -- Content and metadata
    message_content TEXT,
    parameters TEXT,        -- JSON string
    response_data TEXT,     -- JSON string
    error_message TEXT,
    stack_trace TEXT,
    
    -- Performance metrics
    duration_ms FLOAT,
    
    -- Timestamps (Australia/Sydney timezone)
    created_at DATETIME NOT NULL
);
```

### UserSession Table

Tracks user sessions and interaction patterns:

```sql
CREATE TABLE [model].[UserSession] (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id VARCHAR(36) UNIQUE NOT NULL,  -- UUID
    user_id VARCHAR(255) NOT NULL,
    user_name VARCHAR(255),
    
    -- Session metadata
    channel_id VARCHAR(255),
    conversation_id VARCHAR(255),
    
    -- Session metrics
    message_count INTEGER DEFAULT 0,
    action_count INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    
    -- Session timing
    first_activity_at DATETIME NOT NULL,
    last_activity_at DATETIME NOT NULL,
    session_duration_minutes FLOAT,
    
    -- Timestamps
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

### Indexes

Performance-optimized indexes for common queries:

```sql
-- AuditLog indexes
CREATE INDEX idx_audit_log_user_id ON AuditLog(user_id);
CREATE INDEX idx_audit_log_event_type ON AuditLog(event_type);
CREATE INDEX idx_audit_log_created_at ON AuditLog(created_at);
CREATE INDEX idx_audit_log_user_event_type ON AuditLog(user_id, event_type);
CREATE INDEX idx_audit_log_user_created_at ON AuditLog(user_id, created_at);

-- UserSession indexes
CREATE INDEX idx_user_session_user_id ON UserSession(user_id);
CREATE INDEX idx_user_session_last_activity ON UserSession(last_activity_at);
```

## Audit Services

### AuditService

Core service for logging audit events:

```python
from services.audit_service import AuditService
from utils.sql.models import AuditEventType, AuditStatus

audit_service = AuditService()

# Log user message
await audit_service.log_user_message(
    user_id="user123",
    user_name="John Doe",
    message_content="Create a ticket for bug fix",
    session_id="session456"
)

# Log action execution
await audit_service.log_action_execution(
    action_name="create_ticket",
    user_id="user123",
    user_name="John Doe",
    parameters={"summary": "Bug fix", "priority": "High"},
    response_data={"ticket_key": "BUG-123"},
    event_status=AuditStatus.SUCCESS,
    duration_ms=250.5
)

# Log error event
await audit_service.log_error_event(
    error_message="Database connection failed",
    stack_trace=traceback.format_exc(),
    user_id="user123",
    action_name="create_ticket"
)
```

### SessionService

Service for managing user sessions:

```python
from services.audit_service import SessionService

session_service = SessionService()

# Get or create session
session_id = await session_service.get_or_create_session(
    user_id="user123",
    user_name="John Doe",
    channel_id="teams_channel"
)

# Update session metrics
await session_service.update_session_metrics(
    session_id=session_id,
    message_increment=1,
    action_increment=1
)
```

## Audit Decorators

### @audit_action Decorator

Automatically logs action function executions:

```python
from utils.audit_decorators import audit_action

@audit_action("create_ticket")
@with_jira_client
async def create_ticket(context, state, jira=None):
    """
    The decorator automatically handles:
    - Action start/completion logging
    - Performance timing
    - Parameter and response capture
    - Error logging with stack traces
    - Session tracking and metrics
    """
    # Your business logic here
    return "Ticket created successfully"
```

### @audit_message_handler Decorator

Automatically logs message handler executions:

```python
from utils.audit_decorators import audit_message_handler

@audit_message_handler("help_command")
async def display_help(context, state):
    """
    The decorator automatically handles:
    - User message logging
    - Handler execution tracking
    - Session management
    - Error handling
    """
    # Your message handling logic here
    return True
```

## Audit Middleware

### API Request/Response Logging

Automatically logs all API requests and responses:

```python
from utils.audit_middleware import create_audit_middleware

# Add to aiohttp application
audit_middleware = create_audit_middleware()
app = web.Application(middlewares=[aiohttp_error_middleware, audit_middleware])
```

The middleware automatically logs:
- Request method, endpoint, parameters
- Response status, content type, duration
- User information (from headers)
- Error details for failed requests

## Event Types

The system tracks the following event types:

- **USER_MESSAGE** - Messages sent by users
- **BOT_RESPONSE** - Responses sent by the bot
- **ACTION_EXECUTION** - AI action function executions
- **AUTHENTICATION** - User authentication events
- **ERROR** - System errors and exceptions
- **SYSTEM_EVENT** - System startup, shutdown, configuration changes
- **DATABASE_OPERATION** - Database operations and migrations
- **API_CALL** - External API calls and responses

## Event Statuses

Each event has one of the following statuses:

- **SUCCESS** - Operation completed successfully
- **FAILURE** - Operation failed with error
- **WARNING** - Operation completed with warnings
- **IN_PROGRESS** - Operation is currently running

## Usage Examples

### Basic Audit Logging

```python
# Manual audit logging
from services.audit_service import AuditService
from utils.sql.models import AuditEventType, AuditStatus

audit_service = AuditService()

await audit_service.log_event(
    event_type=AuditEventType.SYSTEM_EVENT,
    event_status=AuditStatus.SUCCESS,
    message_content="Application started successfully",
    parameters={"startup_time": time.time()}
)
```

### Decorator Usage

```python
# Automatic audit logging with decorators
@audit_action("custom_action")
async def my_custom_action(context, state):
    # Business logic only - audit is automatic
    return "Action completed"
```

### Session Tracking

```python
# Track user sessions
session_service = SessionService()

# Get session info
session_info = await session_service.get_session_info("session_id")
print(f"User: {session_info['user_name']}")
print(f"Messages: {session_info['message_count']}")
print(f"Actions: {session_info['action_count']}")
print(f"Duration: {session_info['session_duration_minutes']} minutes")
```

## Configuration

### Logging Setup

The audit mechanism uses Loguru with Australia/Sydney timezone:

```python
from utils.audit_middleware import setup_audit_logging

# Setup audit-specific logging
setup_audit_logging()
```

### Database Configuration

Audit tables are automatically created during application startup:

```python
from utils.sql.init_db import init_database

# Initialize database tables
success = init_database()
```

## Performance Considerations

1. **Asynchronous Operations** - All audit operations are asynchronous to avoid blocking
2. **Database Indexing** - Optimized indexes for common query patterns
3. **Connection Pooling** - Efficient database connection management
4. **Batch Operations** - Session metrics are updated in batches
5. **Error Isolation** - Audit failures don't affect core functionality

## Security and Privacy

1. **Data Sanitization** - Sensitive data is filtered before logging
2. **Access Control** - Audit logs require appropriate database permissions
3. **Retention Policies** - Configurable log retention periods
4. **Encryption** - Database connections use encryption in production

## Monitoring and Alerting

The audit system provides data for:

1. **User Engagement Metrics** - Session duration, message frequency
2. **Performance Monitoring** - Action execution times, error rates
3. **System Health** - Error patterns, failure rates
4. **Usage Analytics** - Popular actions, user behavior patterns

## Testing

Comprehensive test coverage includes:

- Unit tests for all audit services
- Integration tests for end-to-end flows
- Performance tests for high-volume scenarios
- Error handling tests for failure scenarios

See the `tests/` directory for complete test examples.

## Migration and Deployment

1. **Database Migration** - Tables are created automatically on startup
2. **Backward Compatibility** - New audit features don't break existing functionality
3. **Gradual Rollout** - Decorators can be applied incrementally
4. **Monitoring** - Built-in health checks for audit system components
