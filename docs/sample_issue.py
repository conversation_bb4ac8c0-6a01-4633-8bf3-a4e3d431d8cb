{
    'expand': 'customfield_10346.properties,customfield_10063.properties,operations,versionedRepresentations,editmeta,changelog,customfield_10010.requestTypePractice,renderedFields,customfield_10062.properties',
    'id': '190520',
    'self': 'https: //api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/issue/190520',
    'key': 'BIRI-10428',
    'fields': {
        'statusCategory': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/statuscategory/2',
            'id': 2,
            'key': 'new',
            'colorName': 'blue-gray',
            'name': 'To Do',
        },
        'resolution': None,
        'customfield_10510': None,
        'customfield_10500': None,
        'customfield_10501': None,
        'customfield_10506': None,
        'customfield_10507': None,
        'customfield_10508': None,
        'customfield_10509': None,
        'lastViewed': None,
        'aggregatetimeoriginalestimate': None,
        'issuelinks': [],
        'assignee': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/user?accountId=61f86093f5f5b80070c6a411',
            'accountId': '61f86093f5f5b80070c6a411',
            'emailAddress': '<EMAIL>',
            'avatarUrls': {
                '48x48': 'https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/61f86093f5f5b80070c6a411/8dced1f0-33e1-49e4-aec0-45765511d6a1/48',
                '24x24': 'https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/61f86093f5f5b80070c6a411/8dced1f0-33e1-49e4-aec0-45765511d6a1/24',
                '16x16': 'https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/61f86093f5f5b80070c6a411/8dced1f0-33e1-49e4-aec0-45765511d6a1/16',
                '32x32': 'https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/61f86093f5f5b80070c6a411/8dced1f0-33e1-49e4-aec0-45765511d6a1/32',
            },
            'displayName': 'Phillip Hu',
            'active': True,
            'timeZone': 'Australia/Sydney',
            'accountType': 'atlassian',
        },
        'subtasks': [],
        'votes': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/issue/BIRI-10428/votes',
            'votes': 0,
            'hasVoted': False,
        },
        'worklog': {
            'startAt': 0,
            'maxResults': 20,
            'total': 1,
            'worklogs': [
                {
                    'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/issue/190520/worklog/419805',
                    'author': {
                        'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/user?accountId=61f86093f5f5b80070c6a411',
                        'accountId': '61f86093f5f5b80070c6a411',
                        'emailAddress': '<EMAIL>',
                        'avatarUrls': {
                            '48x48': 'https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/61f86093f5f5b80070c6a411/8dced1f0-33e1-49e4-aec0-45765511d6a1/48',
                            '24x24': 'https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/61f86093f5f5b80070c6a411/8dced1f0-33e1-49e4-aec0-45765511d6a1/24',
                            '16x16': 'https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/61f86093f5f5b80070c6a411/8dced1f0-33e1-49e4-aec0-45765511d6a1/16',
                            '32x32': 'https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/61f86093f5f5b80070c6a411/8dced1f0-33e1-49e4-aec0-45765511d6a1/32',
                        },
                        'displayName': 'Phillip Hu',
                        'active': True,
                        'timeZone': 'Australia/Sydney',
                        'accountType': 'atlassian',
                    },
                    'updateAuthor': {
                        'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/user?accountId=61f86093f5f5b80070c6a411',
                        'accountId': '61f86093f5f5b80070c6a411',
                        'emailAddress': '<EMAIL>',
                        'avatarUrls': {
                            '48x48': 'https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/61f86093f5f5b80070c6a411/8dced1f0-33e1-49e4-aec0-45765511d6a1/48',
                            '24x24': 'https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/61f86093f5f5b80070c6a411/8dced1f0-33e1-49e4-aec0-45765511d6a1/24',
                            '16x16': 'https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/61f86093f5f5b80070c6a411/8dced1f0-33e1-49e4-aec0-45765511d6a1/16',
                            '32x32': 'https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/61f86093f5f5b80070c6a411/8dced1f0-33e1-49e4-aec0-45765511d6a1/32',
                        },
                        'displayName': 'Phillip Hu',
                        'active': True,
                        'timeZone': 'Australia/Sydney',
                        'accountType': 'atlassian',
                    },
                    'comment': '[RC] Focused on implementing a new feature for the Teams bot. This involved gathering requirements, understanding the desired functionality, and planning the integration process to ensure seamless operation within the existing framework.',
                    'created': '2025-05-20T10:50:57.583+1000',
                    'updated': '2025-05-20T10:50:57.583+1000',
                    'started': '2025-05-19T08:00:00.000+1000',
                    'timeSpent': '2h',
                    'timeSpentSeconds': 7200,
                    'id': '419805',
                    'issueId': '190520',
                }
            ],
        },
        'issuetype': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/issuetype/10007',
            'id': '10007',
            'description': 'A small, distinct piece of work.',
            'iconUrl': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/universal_avatar/view/type/issuetype/avatar/10557?size=medium',
            'name': 'Task',
            'subtask': False,
            'avatarId': 10557,
            'hierarchyLevel': 0,
        },
        'timetracking': {
            'remainingEstimate': '0m',
            'timeSpent': '2h',
            'remainingEstimateSeconds': 0,
            'timeSpentSeconds': 7200,
        },
        'environment': None,
        'duedate': None,
        'customfield_10350': None,
        'customfield_10230': None,
        'customfield_10110': None,
        'customfield_10231': None,
        'customfield_10352': None,
        'customfield_10111': None,
        'customfield_10232': None,
        'customfield_10112': None,
        'customfield_10233': None,
        'customfield_10234': None,
        'customfield_10113': None,
        'customfield_10114': None,
        'customfield_10235': None,
        'customfield_10467': None,
        'customfield_10104': None,
        'customfield_10225': None,
        'customfield_10346': None,
        'customfield_10226': None,
        'customfield_10347': None,
        'customfield_10105': None,
        'customfield_10106': None,
        'customfield_10227': None,
        'customfield_10348': None,
        'customfield_10228': None,
        'customfield_10349': None,
        'customfield_10107': None,
        'customfield_10229': None,
        'customfield_10108': None,
        'customfield_10109': None,
        'customfield_10340': None,
        'customfield_10220': None,
        'customfield_10463': None,
        'customfield_10100': None,
        'customfield_10221': None,
        'customfield_10343': None,
        'customfield_10222': None,
        'customfield_10101': None,
        'customfield_10464': None,
        'customfield_10465': None,
        'customfield_10102': None,
        'customfield_10223': None,
        'customfield_10344': None,
        'customfield_10103': None,
        'customfield_10224': None,
        'customfield_10345': None,
        'customfield_10466': None,
        'customfield_10335': None,
        'customfield_10214': None,
        'customfield_10215': None,
        'customfield_10216': None,
        'customfield_10337': 'Policy Number:\r\nClaim Number:\r\nDelete: Yes/No\r\nMove: Yes/No\r\nIf Move, please provide the new Policy Number:',
        'customfield_10338': None,
        'customfield_10217': None,
        'customfield_10459': None,
        'customfield_10339': None,
        'customfield_10218': None,
        'timeestimate': 0,
        'customfield_10219': None,
        'status': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/status/10022',
            'description': 'The initial "bucket" for new work',
            'iconUrl': 'https://petsure.atlassian.net/images/icons/statuses/generic.png',
            'name': 'Backlog',
            'id': '10022',
            'statusCategory': {
                'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/statuscategory/2',
                'id': 2,
                'key': 'new',
                'colorName': 'blue-gray',
                'name': 'To Do',
            },
        },
        'customfield_10450': None,
        'customfield_10330': None,
        'customfield_10331': None,
        'customfield_10210': None,
        'customfield_10211': None,
        'customfield_10332': None,
        'customfield_10333': None,
        'customfield_10212': None,
        'customfield_10454': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10975',
            'value': 'No',
            'id': '10975',
        },
        'customfield_10334': None,
        'customfield_10213': None,
        'customfield_10445': 0.0,
        'customfield_10324': None,
        'customfield_10203': None,
        'customfield_10325': None,
        'customfield_10204': None,
        'customfield_10446': 0.0,
        'customfield_10447': None,
        'customfield_10326': None,
        'customfield_10205': None,
        'customfield_10327': None,
        'customfield_10206': None,
        'customfield_10448': None,
        'aggregatetimeestimate': 0,
        'customfield_10449': None,
        'customfield_10328': None,
        'customfield_10207': None,
        'customfield_10329': None,
        'customfield_10208': None,
        'customfield_10209': None,
        'creator': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/user?accountId=61f86093f5f5b80070c6a411',
            'accountId': '61f86093f5f5b80070c6a411',
            'emailAddress': '<EMAIL>',
            'avatarUrls': {
                '48x48': 'https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/61f86093f5f5b80070c6a411/8dced1f0-33e1-49e4-aec0-45765511d6a1/48',
                '24x24': 'https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/61f86093f5f5b80070c6a411/8dced1f0-33e1-49e4-aec0-45765511d6a1/24',
                '16x16': 'https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/61f86093f5f5b80070c6a411/8dced1f0-33e1-49e4-aec0-45765511d6a1/16',
                '32x32': 'https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/61f86093f5f5b80070c6a411/8dced1f0-33e1-49e4-aec0-45765511d6a1/32',
            },
            'displayName': 'Phillip Hu',
            'active': True,
            'timeZone': 'Australia/Sydney',
            'accountType': 'atlassian',
        },
        'customfield_10440': None,
        'customfield_10441': None,
        'customfield_10320': [
            {
                'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10741',
                'value': 'Dev',
                'id': '10741',
            }
        ],
        'customfield_10200': None,
        'customfield_10442': None,
        'customfield_10443': None,
        'customfield_10201': None,
        'customfield_10322': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10746',
            'value': 'Australia',
            'id': '10746',
        },
        'customfield_10202': None,
        'customfield_10444': None,
        'customfield_10313': None,
        'customfield_10314': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10739',
            'value': 'No',
            'id': '10739',
        },
        'customfield_10315': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10731',
            'value': '4',
            'id': '10731',
        },
        'customfield_10316': None,
        'customfield_10317': None,
        'customfield_10318': None,
        'customfield_10439': None,
        'customfield_10319': None,
        'timespent': 7200,
        'aggregatetimespent': 7200,
        'workratio': -1,
        'customfield_10540': None,
        'customfield_10415': None,
        'customfield_10416': None,
        'customfield_10418': None,
        'customfield_10411': None,
        'customfield_10401': None,
        'customfield_10402': None,
        'customfield_10404': None,
        'customfield_10405': None,
        'customfield_10406': None,
        'customfield_10407': None,
        'customfield_10400': None,
        'customfield_10511': None,
        'customfield_10512': None,
        'customfield_10513': None,
        'customfield_10514': None,
        'customfield_10515': None,
        'customfield_10516': None,
        'customfield_10190': None,
        'customfield_10070': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10112',
            'value': '3 - Minor',
            'id': '10112',
        },
        'customfield_10191': None,
        'customfield_10071': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10118',
            'value': 'Standard',
            'id': '10118',
        },
        'customfield_10192': None,
        'customfield_10072': '2025-05-20',
        'customfield_10193': None,
        'customfield_10073': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10115',
            'value': 'Low',
            'id': '10115',
        },
        'customfield_10194': None,
        'customfield_10074': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10126',
            'value': 'Yes',
            'id': '10126',
        },
        'customfield_10195': None,
        'customfield_10075': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10142',
            'value': 'Reduced Premium (% or $ Off)',
            'id': '10142',
        },
        'customfield_10196': None,
        'customfield_10076': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10152',
            'value': 'Lockers',
            'id': '10152',
        },
        'customfield_10197': None,
        'customfield_10077': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10120',
            'value': 'Low',
            'id': '10120',
        },
        'customfield_10198': None,
        'customfield_10078': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10134',
            'value': 'New promotion code',
            'id': '10134',
        },
        'customfield_10199': None,
        'customfield_10079': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10136',
            'value': 'Promotion (funded by Partner)',
            'id': '10136',
        },
        'customfield_10180': None,
        'customfield_10060': None,
        'customfield_10181': None,
        'customfield_10182': None,
        'customfield_10061': None,
        'customfield_10062': None,
        'customfield_10183': None,
        'customfield_10063': {'id': 4, 'value': 'OPEX'},
        'customfield_10184': None,
        'customfield_10185': None,
        'customfield_10186': None,
        'customfield_10066': None,
        'customfield_10187': None,
        'customfield_10067': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10148',
            'value': 'Yes',
            'id': '10148',
        },
        'customfield_10188': None,
        'customfield_10068': None,
        'customfield_10189': None,
        'customfield_10069': None,
        'labels': [],
        'components': [],
        'customfield_10170': None,
        'customfield_10050': None,
        'customfield_10171': None,
        'customfield_10172': None,
        'customfield_10051': None,
        'customfield_10052': None,
        'customfield_10173': None,
        'customfield_10174': None,
        'customfield_10053': None,
        'customfield_10054': None,
        'customfield_10176': None,
        'customfield_10177': None,
        'customfield_10178': None,
        'customfield_10057': None,
        'customfield_10058': None,
        'customfield_10179': None,
        'customfield_10059': None,
        'customfield_10049': [],
        'customfield_10160': None,
        'customfield_10040': None,
        'customfield_10161': None,
        'customfield_10162': None,
        'customfield_10041': None,
        'customfield_10042': None,
        'customfield_10163': None,
        'customfield_10284': None,
        'customfield_10164': None,
        'customfield_10043': None,
        'reporter': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/user?accountId=61f86093f5f5b80070c6a411',
            'accountId': '61f86093f5f5b80070c6a411',
            'emailAddress': '<EMAIL>',
            'avatarUrls': {
                '48x48': 'https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/61f86093f5f5b80070c6a411/8dced1f0-33e1-49e4-aec0-45765511d6a1/48',
                '24x24': 'https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/61f86093f5f5b80070c6a411/8dced1f0-33e1-49e4-aec0-45765511d6a1/24',
                '16x16': 'https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/61f86093f5f5b80070c6a411/8dced1f0-33e1-49e4-aec0-45765511d6a1/16',
                '32x32': 'https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/61f86093f5f5b80070c6a411/8dced1f0-33e1-49e4-aec0-45765511d6a1/32',
            },
            'displayName': 'Phillip Hu',
            'active': True,
            'timeZone': 'Australia/Sydney',
            'accountType': 'atlassian',
        },
        'customfield_10044': None,
        'customfield_10165': None,
        'customfield_10286': None,
        'customfield_10166': None,
        'customfield_10287': None,
        'customfield_10045': None,
        'customfield_10046': None,
        'customfield_10167': None,
        'customfield_10288': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10672',
            'value': 'Core Component',
            'id': '10672',
        },
        'customfield_10168': None,
        'customfield_10047': None,
        'customfield_10048': None,
        'customfield_10169': None,
        'customfield_10159': None,
        'customfield_10038': None,
        'customfield_10039': None,
        'progress': {'progress': 7200, 'total': 7200, 'percent': 100},
        'customfield_10270': None,
        'customfield_10271': None,
        'customfield_10150': None,
        'customfield_10030': None,
        'customfield_10272': None,
        'customfield_10151': None,
        'customfield_10031': None,
        'customfield_10152': None,
        'project': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/project/10031',
            'id': '10031',
            'key': 'BIRI',
            'name': 'Business Intelligence Reporting & Insights',
            'projectTypeKey': 'software',
            'simplified': False,
            'avatarUrls': {
                '48x48': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/universal_avatar/view/type/project/avatar/10913',
                '24x24': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/universal_avatar/view/type/project/avatar/10913?size=small',
                '16x16': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/universal_avatar/view/type/project/avatar/10913?size=xsmall',
                '32x32': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/universal_avatar/view/type/project/avatar/10913?size=medium',
            },
        },
        'customfield_10032': None,
        'customfield_10153': None,
        'customfield_10154': None,
        'customfield_10033': None,
        'customfield_10034': None,
        'customfield_10155': None,
        'customfield_10156': None,
        'customfield_10277': None,
        'customfield_10035': None,
        'customfield_10036': None,
        'customfield_10399': None,
        'customfield_10157': None,
        'customfield_10158': None,
        'customfield_10037': None,
        'customfield_10148': None,
        'customfield_10269': None,
        'customfield_10027': [],
        'customfield_10028': None,
        'customfield_10149': None,
        'customfield_10029': None,
        'resolutiondate': None,
        'watches': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/issue/BIRI-10428/watchers',
            'watchCount': 1,
            'isWatching': True,
        },
        'customfield_10260': None,
        'customfield_10140': None,
        'customfield_10020': None,
        'customfield_10262': None,
        'customfield_10141': None,
        'customfield_10142': None,
        'customfield_10263': None,
        'customfield_10021': None,
        'customfield_10022': None,
        'customfield_10264': None,
        'customfield_10143': None,
        'customfield_10144': None,
        'customfield_10023': None,
        'customfield_10024': None,
        'customfield_10266': None,
        'customfield_10145': None,
        'customfield_10146': None,
        'customfield_10267': None,
        'customfield_10025': None,
        'customfield_10026': None,
        'customfield_10268': None,
        'customfield_10016': None,
        'customfield_10258': None,
        'customfield_10137': None,
        'customfield_10138': None,
        'customfield_10259': None,
        'customfield_10017': None,
        'customfield_10139': None,
        'customfield_10018': {
            'hasEpicLinkFieldDependency': False,
            'showField': False,
            'nonEditableReason': {
                'reason': 'PLUGIN_LICENSE_ERROR',
                'message': 'The Parent Link is only available to Jira Premium users.',
            },
        },
        'customfield_10019': '2|i01mnf:',
        'updated': '2025-05-20T10:50:57.614+1000',
        'timeoriginalestimate': None,
        'customfield_10370': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10863',
            'value': 'Production',
            'id': '10863',
        },
        'customfield_10250': None,
        'customfield_10371': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10865',
            'value': 'Standard',
            'id': '10865',
        },
        'customfield_10130': None,
        'customfield_10372': None,
        'description': 'Focused on implementing a new feature for the Teams bot. This involved gathering requirements, understanding the desired functionality, and planning the integration process to ensure seamless operation within the existing framework.',
        'customfield_10010': None,
        'customfield_10131': None,
        'customfield_10253': None,
        'customfield_10132': None,
        'customfield_10133': None,
        'customfield_10254': None,
        'customfield_10134': None,
        'customfield_10255': None,
        'customfield_10014': None,
        'customfield_10135': None,
        'customfield_10256': None,
        'customfield_10136': None,
        'customfield_10257': None,
        'customfield_10015': None,
        'customfield_10368': None,
        'customfield_10005': None,
        'customfield_10126': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10318',
            'value': 'No',
            'id': '10318',
        },
        'customfield_10006': None,
        'customfield_10369': None,
        'customfield_10127': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10321',
            'value': 'No',
            'id': '10321',
        },
        'customfield_10128': None,
        'customfield_10007': None,
        'customfield_10008': None,
        'customfield_10129': None,
        'customfield_10009': None,
        'summary': 'Implementing a new feature for the Teams bot',
        'customfield_10360': None,
        'customfield_10481': None,
        'customfield_10361': None,
        'customfield_10482': None,
        'customfield_10483': None,
        'customfield_10362': None,
        'customfield_10241': None,
        'customfield_10120': None,
        'customfield_10000': '{}',
        'customfield_10121': None,
        'customfield_10484': None,
        'customfield_10364': None,
        'customfield_10122': None,
        'customfield_10001': None,
        'customfield_10123': None,
        'customfield_10365': None,
        'customfield_10002': [],
        'customfield_10366': None,
        'customfield_10124': None,
        'customfield_10003': None,
        'customfield_10004': None,
        'customfield_10367': None,
        'customfield_10125': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10315',
            'value': 'No',
            'id': '10315',
        },
        'customfield_10115': None,
        'customfield_10236': None,
        'customfield_10237': None,
        'customfield_10116': None,
        'customfield_10117': None,
        'customfield_10118': None,
        'customfield_10119': None,
        'comment': {
            'comments': [],
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/issue/190520/comment',
            'maxResults': 0,
            'total': 0,
            'startAt': 0,
        },
        'statuscategorychangedate': '2025-05-20T10:50:07.925+1000',
        'fixVersions': [],
        'priority': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/priority/10000',
            'iconUrl': 'https://petsure.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10695?size=xsmall',
            'name': 'Must Have',
            'id': '10000',
        },
        'versions': [],
        'aggregateprogress': {'progress': 7200, 'total': 7200, 'percent': 100},
        'issuerestriction': {'issuerestrictions': {}, 'shouldDisplay': False},
        'created': '2025-05-20T10:50:07.377+1000',
        'customfield_10090': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10173',
            'value': 'Yes',
            'id': '10173',
        },
        'customfield_10091': [
            {
                'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10181',
                'value': 'All Brands',
                'id': '10181',
            }
        ],
        'customfield_10092': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10175',
            'value': 'Yes',
            'id': '10175',
        },
        'customfield_10093': None,
        'customfield_10094': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10163',
            'value': 'Call centre',
            'id': '10163',
        },
        'customfield_10095': None,
        'customfield_10096': None,
        'customfield_10097': None,
        'customfield_10098': None,
        'customfield_10099': None,
        'security': None,
        'attachment': [],
        'customfield_10080': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10144',
            'value': 'First Year',
            'id': '10144',
        },
        'customfield_10081': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10123',
            'value': 'Low',
            'id': '10123',
        },
        'customfield_10082': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10145',
            'value': 'Yes',
            'id': '10145',
        },
        'customfield_10083': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10149',
            'value': 'Not Working',
            'id': '10149',
        },
        'customfield_10084': '2025-05-20',
        'customfield_10085': None,
        'customfield_10086': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10177',
            'value': 'Large - $50k to 249k',
            'id': '10177',
        },
        'customfield_10087': 'For any Change requests please raise a ticket @ \r\n\r\nhttps://jira.petsure.com.au/projects/PCM',
        'customfield_10088': {
            'self': 'https://api.atlassian.com/ex/jira/************************8cd6b957fa99/rest/api/2/customFieldOption/10213',
            'value': 'Upcoming',
            'id': '10213',
        },
        'customfield_10089': None,
    },
}
