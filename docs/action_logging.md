# Action Function Logging Guide

This document describes the logging implementation for action functions in the bot_framework project.

## Overview

All action functions in the bot_framework have been enhanced with comprehensive logging using the Loguru library. This provides detailed visibility into:

- When each action function is called
- Who called the action (user ID and name)
- What parameters were provided
- The execution time of each action
- Any errors or exceptions that occurred
- The result of the action

## Logging Pattern

Each action function follows a consistent logging pattern:

1. **Action Start**: Log when the action is called with a unique ID
2. **Context Capture**: Capture user information and action parameters
3. **Validation Logging**: Log validation errors at WARNING level
4. **Operation Logging**: Log key operations at DEBUG level
5. **Result Logging**: Log the result with timing information
6. **Error Logging**: Log any errors with detailed context

## Example Action Function Logging

Here's an example of the logging pattern implemented in all action functions:

```python
@with_jira_client
async def create_ticket(context: ActionTurnContext, state: AppTurnState, jira: Jira = None):
    """Creates a new Jira ticket with the provided summary and description."""
    # Generate a unique operation ID for tracking this action
    action_id = str(uuid.uuid4())
    start_time = time.time()
    
    # Get user information if available
    user_id = "unknown"
    user_name = "unknown"
    if context.activity and context.activity.from_property:
        user_id = context.activity.from_property.id
        user_name = context.activity.from_property.name
    
    # Log the action start with context
    logger.info(f"ACTION CALLED: create_ticket [id={action_id}] by user {user_name}")
    
    try:
        # Use LoggingContext to add structured context to all logs within this function
        with LoggingContext(action_id=action_id, function="create_ticket", user_id=user_id, user_name=user_name):
            # Validate and log validation errors
            if not context.data.get('summary'):
                logger.warning(f"Missing required field 'summary' in create_ticket [id={action_id}]")
                return 'Error: Ticket summary is required.'

            # Log operation details
            logger.info(f"Creating Jira ticket with summary: '{summary[:30]}...' [id={action_id}]")
            
            # Perform the operation
            issue = jira.issue_create(...)
            
            # Log success with timing information
            duration = time.time() - start_time
            logger.info(f"Successfully created ticket {issue['key']} [id={action_id}] in {duration:.3f}s")
            
            return f'Ticket {issue["key"]} created.'
    except Exception as e:
        # Log error with timing and context
        duration = time.time() - start_time
        logger.error(f"Error in create_ticket [id={action_id}] after {duration:.3f}s: {e}")
        logger.exception(e)
        return 'An error occurred...'
```

## Key Logging Points

### 1. Action Start

Every action function logs its invocation with a unique ID:

```python
action_id = str(uuid.uuid4())
logger.info(f"ACTION CALLED: {function_name} [id={action_id}] by user {user_name}")
```

### 2. Parameter Validation

Parameter validation is logged at WARNING level:

```python
if not context.data.get('required_field'):
    logger.warning(f"Missing required field 'required_field' in {function_name} [id={action_id}]")
    return 'Error: Required field is required.'
```

### 3. Operation Details

Key operations are logged at DEBUG or INFO level:

```python
logger.debug(f"Operation detail: {some_value} [id={action_id}]")
```

### 4. Success Results

Successful completion is logged with timing information:

```python
duration = time.time() - start_time
logger.info(f"Successfully completed {function_name} [id={action_id}] in {duration:.3f}s")
```

### 5. Error Handling

Errors are logged with context and timing:

```python
duration = time.time() - start_time
logger.error(f"Error in {function_name} [id={action_id}] after {duration:.3f}s: {e}")
logger.exception(e)  # Logs the full stack trace
```

## Logging Context

All logs within an action function include a consistent context using the `LoggingContext` context manager:

```python
with LoggingContext(action_id=action_id, function=function_name, user_id=user_id, user_name=user_name):
    # All logs in this block will include these context values
```

## Log Levels Used

- **DEBUG**: Detailed operation information (parameters, intermediate steps)
- **INFO**: Action start/completion, successful operations
- **WARNING**: Validation issues, potential problems
- **ERROR**: Operation failures, exceptions
- **CRITICAL**: System-level failures

## Benefits

This comprehensive logging approach provides several benefits:

1. **Traceability**: Each action has a unique ID that can be traced through the logs
2. **Performance Monitoring**: Timing information helps identify slow operations
3. **Error Diagnosis**: Detailed context makes it easier to diagnose issues
4. **User Attribution**: All actions are linked to the user who initiated them
5. **Audit Trail**: Complete record of all actions performed by the bot

## Viewing Logs

Logs can be viewed in the console during development and in log files in production. The log format includes:

- Timestamp in Australia/Sydney timezone
- Log level
- File name, function name, and line number
- Action ID and user information
- The log message

Example log output:

```
2023-07-15 14:23:45.123 | INFO    | tools.actions:create_ticket:45 | action_id=abc123 function=create_ticket user_id=user123 user_name=John Doe | ACTION CALLED: create_ticket [id=abc123] by user John Doe
2023-07-15 14:23:45.456 | INFO    | tools.actions:create_ticket:60 | action_id=abc123 function=create_ticket user_id=user123 user_name=John Doe | Successfully created ticket PROJ-123 [id=abc123] in 0.333s
```
