{
    'id': '190520',
    'key': 'BIRI-10428',
    'statusCategory': {
        'name': 'To <PERSON>',
    },
    'issuelinks': [],
    'assignee': {
        'accountId': '61f86093f5f5b80070c6a411',
        'emailAddress': '<EMAIL>',
        'displayName': '<PERSON>',
    },
    'subtasks': [],
    'worklogs': [
        {
            'author': {
                'accountId': '61f86093f5f5b80070c6a411',
                'emailAddress': '<EMAIL>',
                'displayName': '<PERSON>',
            },
            'comment': '[<PERSON>] Focused on implementing a new feature for the Teams bot. This involved gathering requirements, understanding the desired functionality, and planning the integration process to ensure seamless operation within the existing framework.',
            'created': '2025-05-20T10:50:57.583+1000',
            'updated': '2025-05-20T10:50:57.583+1000',
            'started': '2025-05-19T08:00:00.000+1000',
            'timeSpent': '2h',
            'timeSpentSeconds': 7200,
            'id': '419805',
            'issueId': '190520',
        }
    ],
    'issuetype': {
        'name': 'Task',
        'subtask': False,
    },
    'timetracking': {
        'remainingEstimate': '0m',
        'timeSpent': '2h',
        'remainingEstimateSeconds': 0,
        'timeSpentSeconds': 7200,
    },
    'duedate': None,
    'status': {
        'name': 'Backlog',
        'id': '10022',
        'statusCategory': {
            'id': 2,
            'name': 'To Do',
        },
    },
    'creator': {
        'accountId': '61f86093f5f5b80070c6a411',
        'emailAddress': '<EMAIL>',
        'displayName': 'Phillip Hu',
    },
    'customfield_10063': {'id': 4, 'value': 'OPEX'},
    'reporter': {
        'accountId': '61f86093f5f5b80070c6a411',
        'emailAddress': '<EMAIL>',
        'displayName': 'Phillip Hu',
    },
    'project': {
        'id': '10031',
        'key': 'BIRI',
        'name': 'Business Intelligence Reporting & Insights',
    },
    'updated': '2025-05-20T10:50:57.614+1000',
    'timeoriginalestimate': None,
    'description': 'Focused on implementing a new feature for the Teams bot. This involved gathering requirements, understanding the desired functionality, and planning the integration process to ensure seamless operation within the existing framework.',
    'summary': 'Implementing a new feature for the Teams bot',
    'comment': {
        'comments': [],
        'total': 0,
        'startAt': 0,
    },
    'statuscategorychangedate': '2025-05-20T10:50:07.925+1000',
    'priority': {
        'name': 'Must Have',
        'id': '10000',
    },
    'aggregateprogress': {'progress': 7200, 'total': 7200, 'percent': 100},
    'created': '2025-05-20T10:50:07.377+1000',
}
