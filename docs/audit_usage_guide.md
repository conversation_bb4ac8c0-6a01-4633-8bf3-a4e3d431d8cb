# Audit Mechanism Usage Guide

This guide provides practical examples and best practices for using the comprehensive audit mechanism in your bot framework.

## Quick Start

### 1. Database Setup

The audit tables are automatically created when the application starts. If you need to manually run the migration:

```bash
# Run migration
python src/utils/sql/audit_migration.py migrate

# Verify migration
python src/utils/sql/audit_migration.py verify

# Rollback (WARNING: deletes all audit data)
python src/utils/sql/audit_migration.py rollback
```

### 2. Basic Usage

#### Manual Audit Logging

```python
from src.services.audit_service import AuditService
from src.utils.sql.models import AuditEventType, AuditStatus

# Initialize service
audit_service = AuditService()

# Log user message
await audit_service.log_user_message(
    user_id="user123",
    user_name="<PERSON>",
    message_content="Create a ticket for bug fix",
    session_id="session456"
)

# Log action execution
await audit_service.log_action_execution(
    action_name="create_ticket",
    user_id="user123",
    user_name="<PERSON>",
    parameters={"summary": "Bug fix", "priority": "High"},
    response_data={"ticket_key": "BUG-123"},
    event_status=AuditStatus.SUCCESS,
    duration_ms=250.5
)
```

#### Automatic Audit with Decorators

```python
from src.utils.audit_decorators import audit_action

@audit_action("create_ticket")
@with_jira_client
async def create_ticket(context, state, jira=None):
    """
    This function is automatically audited:
    - Action start/completion logged
    - Performance timing captured
    - Parameters and response logged
    - Errors logged with stack traces
    - Session tracking updated
    """
    # Your business logic here
    return "Ticket created successfully"
```

## Advanced Usage

### Session Management

```python
from src.services.audit_service import SessionService

session_service = SessionService()

# Get or create session (30-minute window)
session_id = await session_service.get_or_create_session(
    user_id="user123",
    user_name="John Doe",
    channel_id="teams_channel"
)

# Update session metrics
await session_service.update_session_metrics(
    session_id=session_id,
    message_increment=1,
    action_increment=1
)

# Get session information
session_info = await session_service.get_session_info(session_id)
print(f"User: {session_info['user_name']}")
print(f"Messages: {session_info['message_count']}")
print(f"Duration: {session_info['session_duration_minutes']} minutes")
```

### Custom Event Logging

```python
# Log authentication events
await audit_service.log_authentication_event(
    user_id="user123",
    user_name="John Doe",
    event_status=AuditStatus.SUCCESS,
    parameters={"auth_method": "oauth", "provider": "jira"}
)

# Log API calls
await audit_service.log_api_call(
    endpoint="/rest/api/3/issue",
    method="POST",
    event_status=AuditStatus.SUCCESS,
    user_id="user123",
    duration_ms=150.0,
    parameters={"project": "TEST"},
    response_data={"key": "TEST-123"}
)

# Log system events
await audit_service.log_system_event(
    event_description="Application started",
    event_status=AuditStatus.SUCCESS,
    parameters={"version": "1.0.0", "environment": "production"}
)

# Log errors
await audit_service.log_error_event(
    error_message="Database connection failed",
    stack_trace=traceback.format_exc(),
    user_id="user123",
    action_name="create_ticket"
)
```

## Integration Examples

### Enhancing Existing Actions

**Before (manual logging):**
```python
@with_jira_client
async def create_ticket_old(context, state, jira=None):
    action_id = str(uuid.uuid4())
    start_time = time.time()
    
    user_id = context.activity.from_property.id
    logger.info(f'ACTION: create_ticket [id={action_id}] by {user_id}')
    
    try:
        # Business logic...
        duration = time.time() - start_time
        logger.info(f'SUCCESS: create_ticket [id={action_id}] in {duration:.3f}s')
        return result
    except Exception as e:
        duration = time.time() - start_time
        logger.error(f'ERROR: create_ticket [id={action_id}] after {duration:.3f}s: {e}')
        raise
```

**After (automatic auditing):**
```python
@audit_action("create_ticket")
@with_jira_client
async def create_ticket_new(context, state, jira=None):
    # Business logic only - audit is automatic!
    return result
```

### Message Handler Enhancement

```python
from src.utils.audit_decorators import audit_message_handler

@audit_message_handler("help_command")
async def display_help(context, state):
    """
    Automatically logs:
    - User message content
    - Handler execution time
    - Success/failure status
    - Session updates
    """
    # Your message handling logic
    return True
```

### API Middleware Integration

The audit middleware is automatically integrated in `src/api.py`:

```python
from src.utils.audit_middleware import create_audit_middleware

# Middleware automatically logs all API requests/responses
audit_middleware = create_audit_middleware()
app = web.Application(middlewares=[error_middleware, audit_middleware])
```

## Querying Audit Data

### Common Queries

```sql
-- Get all actions by a user in the last 24 hours
SELECT event_type, action_name, created_at, duration_ms, event_status
FROM model.AuditLog 
WHERE user_id = 'user123' 
  AND event_type = 'action_execution'
  AND created_at >= NOW() - INTERVAL 24 HOUR
ORDER BY created_at DESC;

-- Get user session summary
SELECT user_id, user_name, message_count, action_count, error_count,
       session_duration_minutes, first_activity_at, last_activity_at
FROM model.UserSession 
WHERE user_id = 'user123'
ORDER BY last_activity_at DESC;

-- Get error patterns
SELECT action_name, COUNT(*) as error_count, 
       AVG(duration_ms) as avg_duration
FROM model.AuditLog 
WHERE event_type = 'action_execution' 
  AND event_status = 'failure'
  AND created_at >= NOW() - INTERVAL 7 DAY
GROUP BY action_name
ORDER BY error_count DESC;

-- Get performance metrics
SELECT action_name, 
       COUNT(*) as total_executions,
       AVG(duration_ms) as avg_duration,
       MIN(duration_ms) as min_duration,
       MAX(duration_ms) as max_duration
FROM model.AuditLog 
WHERE event_type = 'action_execution' 
  AND event_status = 'success'
  AND created_at >= NOW() - INTERVAL 7 DAY
GROUP BY action_name
ORDER BY avg_duration DESC;
```

## Best Practices

### 1. Use Decorators for Actions

Always use `@audit_action()` for action functions:

```python
@audit_action("custom_action_name")  # Custom name for clarity
async def my_action(context, state):
    # Business logic
    pass
```

### 2. Handle Sensitive Data

Filter sensitive information before logging:

```python
# Good: Filter sensitive data
safe_params = {k: v for k, v in params.items() if k not in ['password', 'token']}
await audit_service.log_action_execution(
    action_name="login",
    parameters=safe_params,
    # ...
)
```

### 3. Use Appropriate Event Types

Choose the correct event type for your use case:

- `USER_MESSAGE` - User inputs and messages
- `BOT_RESPONSE` - Bot responses and outputs
- `ACTION_EXECUTION` - AI action function calls
- `AUTHENTICATION` - Login/logout events
- `ERROR` - Exceptions and failures
- `SYSTEM_EVENT` - Application lifecycle events
- `API_CALL` - External API interactions

### 4. Include Context Information

Always include relevant context:

```python
await audit_service.log_action_execution(
    action_name="create_ticket",
    user_id=user_id,
    user_name=user_name,
    session_id=session_id,
    activity_id=activity_id,
    channel_id=channel_id,
    parameters=parameters,
    response_data=response_data,
    duration_ms=duration_ms
)
```

### 5. Monitor Performance Impact

The audit system is designed to be non-blocking, but monitor for any performance impact:

```python
# Audit operations are async and won't block your main logic
result = await my_business_logic()
# Audit logging happens in parallel
return result
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure you're using the correct import paths with `src.` prefix
2. **Database Connection**: Verify database connection settings in your configuration
3. **Missing Tables**: Run the migration script if audit tables don't exist
4. **Performance**: Check database indexes if queries are slow

### Debug Mode

Enable debug logging to see audit operations:

```python
import logging
logging.getLogger('src.services.audit_service').setLevel(logging.DEBUG)
```

### Health Checks

Verify audit system health:

```python
from src.utils.sql.audit_migration import verify_audit_migration

if verify_audit_migration():
    print("Audit system is healthy")
else:
    print("Audit system has issues")
```

## Monitoring and Alerting

### Key Metrics to Monitor

1. **Error Rates**: Track action failure rates
2. **Performance**: Monitor action execution times
3. **User Activity**: Track user engagement patterns
4. **System Health**: Monitor audit system itself

### Sample Monitoring Queries

```sql
-- Daily error rate
SELECT DATE(created_at) as date,
       COUNT(CASE WHEN event_status = 'failure' THEN 1 END) * 100.0 / COUNT(*) as error_rate
FROM model.AuditLog 
WHERE event_type = 'action_execution'
  AND created_at >= NOW() - INTERVAL 30 DAY
GROUP BY DATE(created_at)
ORDER BY date;

-- Active users per day
SELECT DATE(created_at) as date, COUNT(DISTINCT user_id) as active_users
FROM model.AuditLog 
WHERE created_at >= NOW() - INTERVAL 30 DAY
GROUP BY DATE(created_at)
ORDER BY date;
```

This comprehensive audit mechanism provides complete visibility into your bot's operations while maintaining high performance and reliability.
