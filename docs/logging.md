# Logging Implementation Guide

This document describes the logging implementation in the bot_framework project using the <PERSON><PERSON>ru library.

## Overview

The bot_framework uses [<PERSON><PERSON><PERSON>](https://github.com/Delgan/loguru) for comprehensive logging throughout the application. Loguru provides a simple yet powerful API for logging with features like:

- Structured logging
- Log rotation
- Colorized output
- Exception tracking
- Context-based logging

## Configuration

The logging configuration is centralized in `src/utils/logging_config.py`. This module provides:

1. A `setup_logging()` function to initialize logging with appropriate settings
2. A `LoggingContext` context manager for adding context to logs
3. Helper functions for working with timezones and formatting

## Log Levels

The application uses the following log levels:

| Level | Usage |
|-------|-------|
| DEBUG | Detailed information for debugging and troubleshooting |
| INFO | General operational information |
| WARNING | Potential issues that don't prevent operation |
| ERROR | Errors that prevent specific operations but not the whole application |
| CRITICAL | Critical errors that may cause the application to fail |

## Key Logging Areas

### Application Startup/Shutdown

Application startup and shutdown events are logged in `src/app.py`:

```python
# Setup logging with appropriate level
log_level = os.environ.get("LOG_LEVEL", "INFO").upper()
setup_logging(log_level)

# Log application startup
logger.info("Starting bot_framework application")
logger.info(f"Server running on localhost:{Config.PORT}")

try:
    web.run_app(api, host='localhost', port=Config.PORT)
except Exception as e:
    logger.critical(f"Application crashed: {e}")
    raise
finally:
    logger.info("Application shutdown")
```

### API Endpoints

API endpoints are logged in `src/api.py` with request/response details:

```python
@routes.post('/api/messages')
async def on_messages(req: web.Request) -> web.Response:
    request_id = f"req_{int(time.time() * 1000)}"
    
    # Log the incoming request
    logger.info(f"Received message request [id={request_id}]")
    logger.debug(f"Request headers: {dict(req.headers)}")
    
    start_time = time.time()
    
    try:
        # Process the request with context
        with LoggingContext(request_id=request_id, endpoint="/api/messages"):
            res = await app.process(req)
            
            # Log the response
            process_time = time.time() - start_time
            logger.info(f"Processed message request [id={request_id}] in {process_time:.3f}s")
            
            if res is not None:
                return res
            
            return web.Response(status=HTTPStatus.OK)
    except Exception as e:
        # Log any exceptions
        process_time = time.time() - start_time
        logger.error(f"Error processing message request [id={request_id}] after {process_time:.3f}s: {str(e)}")
        logger.exception(e)
        # Re-raise to let the middleware handle it
        raise
```

### Database Operations

Database operations are logged in `src/utils/sql/db_connection.py`:

```python
@contextmanager
def session_scope(self) -> Generator[Session, None, None]:
    """Provide a transactional scope around a series of operations."""
    session = self.get_session()
    operation_id = f'db_{int(time.time() * 1000)}'

    try:
        logger.debug(f'Starting database transaction [id={operation_id}]')
        start_time = time.time()

        yield session

        # Commit the transaction
        session.commit()
        duration = time.time() - start_time
        logger.debug(f'Committed database transaction [id={operation_id}] in {duration:.3f}s')

    except Exception as e:
        # Roll back the transaction on error
        session.rollback()
        duration = time.time() - start_time
        logger.error(f'Rolling back transaction [id={operation_id}] after {duration:.3f}s: {str(e)}')
        logger.debug(f'Transaction error traceback [id={operation_id}]:\n{traceback.format_exc()}')
        raise

    finally:
        # Close the session
        session.close()
        logger.debug(f'Closed database session [id={operation_id}]')
```

### Authentication and Token Management

Authentication operations are logged in `src/tools/others.py`:

```python
async def before_turn(context: TurnContext, state=None):
    """Handle authentication before processing a turn."""
    # Generate a unique ID for this turn
    turn_id = str(uuid.uuid4())
    
    # Get user information if available
    user_id = "unknown"
    user_name = "unknown"
    if context.activity and context.activity.from_property:
        user_id = context.activity.from_property.id
        user_name = context.activity.from_property.name
    
    # Log the turn with context
    logger.bind(
        turn_id=turn_id,
        user_id=user_id,
        user_name=user_name,
        activity_type=getattr(context.activity, 'type', 'unknown'),
        channel_id=getattr(context.activity, 'channel_id', 'unknown')
    ).debug(f"Processing turn for user {user_name}")
```

### Error Handling

Error handling is logged in `src/tools/others.py`:

```python
async def on_error(context: TurnContext, error: Exception):
    """Handle unhandled errors during bot turn processing."""
    error_id = str(uuid.uuid4())
    
    # Get user information if available
    user_id = "unknown"
    user_name = "unknown"
    if context.activity and context.activity.from_property:
        user_id = context.activity.from_property.id
        user_name = context.activity.from_property.name
    
    # Log the error with context
    logger.bind(
        error_id=error_id,
        user_id=user_id,
        user_name=user_name,
        activity_id=getattr(context.activity, 'id', 'unknown'),
        channel_id=getattr(context.activity, 'channel_id', 'unknown')
    ).error(f"Unhandled error in bot turn: {error}")
    
    # Log the full traceback at debug level
    logger.debug(f"Error traceback [id={error_id}]:\n{traceback.format_exc()}")
```

## Using the Logging System

### Basic Logging

```python
from loguru import logger

# Simple logging
logger.debug("Detailed information for debugging")
logger.info("General operational information")
logger.warning("Warning about potential issues")
logger.error("Error that prevents an operation")
logger.critical("Critical error that may crash the application")
```

### Logging with Context

```python
from utils.logging_config import LoggingContext

# Add context to logs
with LoggingContext(user_id="123", operation="create_ticket"):
    logger.info("Creating ticket")
    # All logs in this block will include user_id and operation context
```

### Logging Exceptions

```python
try:
    # Some operation that might fail
    result = risky_operation()
except Exception as e:
    # Log the exception with traceback
    logger.exception(f"Operation failed: {e}")
    # or
    logger.error(f"Operation failed: {e}")
    logger.debug(f"Error traceback:\n{traceback.format_exc()}")
```

## Environment Variables

- `LOG_LEVEL`: Sets the minimum log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `ENVIRONMENT`: When set to "production", enables file-based logging with rotation

## Best Practices

1. Use appropriate log levels based on the information's importance
2. Include relevant context in logs (IDs, user information, operation details)
3. Log both the start and end of important operations
4. Use structured logging with the LoggingContext for consistent context
5. Include timing information for performance-sensitive operations
6. Log exceptions with enough context to understand the error
7. Keep sensitive information out of logs (passwords, tokens, etc.)
