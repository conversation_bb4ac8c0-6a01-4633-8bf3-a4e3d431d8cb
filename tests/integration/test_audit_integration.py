"""
Integration tests for the complete audit mechanism.

These tests verify that the audit system works end-to-end,
including database operations, service interactions, and middleware.
"""

import asyncio
import json
from unittest.mock import MagicMock, patch

import pytest
from aiohttp import web
from aiohttp.test_utils import make_mocked_request

from src.services.audit_service import AuditService, SessionService
from src.utils.audit_decorators import audit_action
from src.utils.audit_middleware import audit_middleware
from src.utils.sql.models import AuditEventType, AuditLog, AuditStatus, UserSession


class TestAuditIntegration:
    """Integration tests for the audit mechanism."""

    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session for testing."""
        with patch('services.audit_service.DatabaseConnectionManager') as mock_manager:
            mock_instance = MagicMock()
            mock_manager.return_value = mock_instance

            # Mock session scope
            mock_session = MagicMock()
            mock_instance.session_scope.return_value.__enter__ = MagicMock(return_value=mock_session)
            mock_instance.session_scope.return_value.__exit__ = MagicMock(return_value=None)

            yield mock_session

    @pytest.mark.asyncio
    async def test_complete_user_interaction_flow(self, mock_db_session):
        """Test a complete user interaction flow with audit logging."""
        # Initialize services
        audit_service = AuditService()
        session_service = SessionService()

        # Mock existing session query to return None (new session)
        mock_db_session.query.return_value.filter.return_value.order_by.return_value.first.return_value = None

        # Step 1: User sends a message
        user_id = 'test_user_123'
        user_name = 'Test User'
        message_content = 'Create a ticket for bug fix'

        # Get or create session
        session_id = await session_service.get_or_create_session(
            user_id=user_id,
            user_name=user_name,
            channel_id='teams_channel_456',
        )

        # Log user message
        message_event_id = await audit_service.log_user_message(
            user_id=user_id,
            user_name=user_name,
            message_content=message_content,
            session_id=session_id,
            activity_id='activity_789',
            channel_id='teams_channel_456',
        )

        # Step 2: Bot processes the message and executes an action
        action_parameters = {
            'summary': 'Fix critical bug',
            'description': 'User reported critical bug that needs immediate attention',
            'priority': 'High',
        }

        action_response = {'ticket_key': 'BUG-123', 'ticket_id': '12345', 'status': 'created'}

        action_event_id = await audit_service.log_action_execution(
            action_name='create_ticket',
            user_id=user_id,
            user_name=user_name,
            parameters=action_parameters,
            response_data=action_response,
            event_status=AuditStatus.SUCCESS,
            session_id=session_id,
            activity_id='activity_789',
            channel_id='teams_channel_456',
            duration_ms=250.5,
        )

        # Step 3: Bot sends response to user
        bot_response = "I've created ticket BUG-123 for you. The ticket has been assigned high priority."

        response_event_id = await audit_service.log_bot_response(
            user_id=user_id,
            user_name=user_name,
            response_content=bot_response,
            session_id=session_id,
            activity_id='activity_790',
            channel_id='teams_channel_456',
            duration_ms=50.2,
        )

        # Step 4: Update session metrics
        await session_service.update_session_metrics(
            session_id=session_id,
            message_increment=1,
            action_increment=1,
        )

        # Verify all events were logged
        assert message_event_id is not None
        assert action_event_id is not None
        assert response_event_id is not None

        # Verify database operations were called
        assert mock_db_session.add.call_count == 4  # 1 session + 3 audit logs

        # Verify the audit logs were created with correct data
        added_objects = [call[0][0] for call in mock_db_session.add.call_args_list]

        # Find the session object
        session_obj = next(obj for obj in added_objects if isinstance(obj, UserSession))
        assert session_obj.user_id == user_id
        assert session_obj.user_name == user_name
        assert session_obj.session_id == session_id

        # Find the audit log objects
        audit_logs = [obj for obj in added_objects if isinstance(obj, AuditLog)]
        assert len(audit_logs) == 3

        # Verify user message log
        message_log = next(log for log in audit_logs if log.event_type == AuditEventType.USER_MESSAGE.value)
        assert message_log.user_id == user_id
        assert message_log.message_content == message_content
        assert message_log.session_id == session_id

        # Verify action execution log
        action_log = next(log for log in audit_logs if log.event_type == AuditEventType.ACTION_EXECUTION.value)
        assert action_log.action_name == 'create_ticket'
        assert action_log.user_id == user_id
        assert action_log.duration_ms == 250.5
        assert json.loads(action_log.parameters) == action_parameters
        assert json.loads(action_log.response_data) == action_response

        # Verify bot response log
        response_log = next(log for log in audit_logs if log.event_type == AuditEventType.BOT_RESPONSE.value)
        assert response_log.user_id == user_id
        assert response_log.message_content == bot_response
        assert response_log.duration_ms == 50.2

    @pytest.mark.asyncio
    async def test_decorator_integration(self, mock_db_session):
        """Test audit decorators integration with services."""
        # Mock session query to return None (new session)
        mock_db_session.query.return_value.filter.return_value.order_by.return_value.first.return_value = None

        # Create a test action function with audit decorator
        @audit_action('test_integration_action')
        async def test_action(context):
            # Simulate some work
            await asyncio.sleep(0.01)
            return {'result': 'success', 'data': 'test_data'}

        # Create mock context
        context = MagicMock()
        context.activity = MagicMock()
        context.activity.from_property = MagicMock()
        context.activity.from_property.id = 'integration_user'
        context.activity.from_property.name = 'Integration User'
        context.activity.id = 'activity_integration'
        context.activity.channel_id = 'channel_integration'
        context.data = {'input_param': 'test_value'}

        # Execute the decorated function
        result = await test_action(context)

        # Verify the result
        assert result == {'result': 'success', 'data': 'test_data'}

        # Verify database operations were called
        # Should have: 1 session creation + 1 action execution log + 1 session metrics update
        assert mock_db_session.add.call_count >= 2

        # Verify audit log was created
        added_objects = [call[0][0] for call in mock_db_session.add.call_args_list]
        audit_logs = [obj for obj in added_objects if isinstance(obj, AuditLog)]

        assert len(audit_logs) >= 1
        action_log = next(log for log in audit_logs if log.event_type == AuditEventType.ACTION_EXECUTION.value)
        assert action_log.action_name == 'test_integration_action'
        assert action_log.user_id == 'integration_user'
        assert action_log.event_status == AuditStatus.SUCCESS.value

    @pytest.mark.asyncio
    async def test_middleware_integration(self, mock_db_session):
        """Test audit middleware integration with services."""
        # Use audit middleware function
        middleware = audit_middleware

        # Create a test handler
        async def test_handler(request):
            return web.json_response({'message': 'success'})

        # Create mock request
        request = make_mocked_request('POST', '/api/test')
        request.headers = {'X-User-ID': 'middleware_user', 'X-User-Name': 'Middleware User'}
        request.query = {'test_param': 'test_value'}

        # Mock request.json() method
        async def mock_json():
            return {'body_data': 'test_body'}

        request.json = mock_json
        request.content_type = 'application/json'

        # Execute middleware
        response = await middleware(request, test_handler)

        # Verify response
        assert response.status == 200

        # Verify audit log was created
        assert mock_db_session.add.call_count >= 1

        added_objects = [call[0][0] for call in mock_db_session.add.call_args_list]
        audit_logs = [obj for obj in added_objects if isinstance(obj, AuditLog)]

        assert len(audit_logs) >= 1
        api_log = next(log for log in audit_logs if log.event_type == AuditEventType.API_CALL.value)
        assert api_log.endpoint == '/api/test'
        assert api_log.method == 'POST'
        assert api_log.user_id == 'middleware_user'
        assert api_log.event_status == AuditStatus.SUCCESS.value

    @pytest.mark.asyncio
    async def test_error_handling_integration(self, mock_db_session):
        """Test error handling across the audit system."""
        # Initialize services
        audit_service = AuditService()

        # Create a test action that fails
        @audit_action('failing_action')
        async def failing_action(context):
            raise ValueError('Intentional test error')

        # Create mock context
        context = MagicMock()
        context.activity = MagicMock()
        context.activity.from_property = MagicMock()
        context.activity.from_property.id = 'error_user'
        context.activity.from_property.name = 'Error User'
        context.data = {'test': 'data'}

        # Execute the failing action and expect exception
        with pytest.raises(ValueError, match='Intentional test error'):
            await failing_action(context)

        # Verify error was logged
        added_objects = [call[0][0] for call in mock_db_session.add.call_args_list]
        audit_logs = [obj for obj in added_objects if isinstance(obj, AuditLog)]

        # Should have action execution log with failure status
        action_log = next(log for log in audit_logs if log.event_type == AuditEventType.ACTION_EXECUTION.value)
        assert action_log.event_status == AuditStatus.FAILURE.value
        assert action_log.error_message == 'Intentional test error'
        assert action_log.stack_trace is not None

    @pytest.mark.asyncio
    async def test_session_continuity(self, mock_db_session):
        """Test session continuity across multiple interactions."""
        session_service = SessionService()

        # Mock first call to return None (new session)
        # Mock subsequent calls to return existing session
        existing_session = MagicMock()
        existing_session.session_id = 'continuous_session_123'
        existing_session.first_activity_at = session_service._get_sydney_now()

        mock_db_session.query.return_value.filter.return_value.order_by.return_value.first.side_effect = [
            None,  # First call - no existing session
            existing_session,  # Second call - existing session found
            existing_session,  # Third call - existing session found
        ]

        user_id = 'continuous_user'
        user_name = 'Continuous User'

        # First interaction - should create new session
        session_id_1 = await session_service.get_or_create_session(
            user_id=user_id,
            user_name=user_name,
        )

        # Second interaction - should reuse existing session
        session_id_2 = await session_service.get_or_create_session(
            user_id=user_id,
            user_name=user_name,
        )

        # Third interaction - should reuse existing session
        session_id_3 = await session_service.get_or_create_session(
            user_id=user_id,
            user_name=user_name,
        )

        # Verify session continuity
        assert session_id_1 is not None
        assert session_id_2 == 'continuous_session_123'
        assert session_id_3 == 'continuous_session_123'

        # Verify only one new session was created
        added_objects = [call[0][0] for call in mock_db_session.add.call_args_list]
        sessions = [obj for obj in added_objects if isinstance(obj, UserSession)]
        assert len(sessions) == 1  # Only one new session created

    @pytest.mark.asyncio
    async def test_performance_metrics_tracking(self, mock_db_session):
        """Test that performance metrics are properly tracked."""
        audit_service = AuditService()

        # Log an action with specific duration
        event_id = await audit_service.log_action_execution(
            action_name='performance_test',
            user_id='perf_user',
            user_name='Performance User',
            duration_ms=1234.56,
            event_status=AuditStatus.SUCCESS,
        )

        # Verify the audit log includes performance metrics
        added_objects = [call[0][0] for call in mock_db_session.add.call_args_list]
        audit_logs = [obj for obj in added_objects if isinstance(obj, AuditLog)]

        action_log = next(log for log in audit_logs if log.action_name == 'performance_test')
        assert action_log.duration_ms == 1234.56
        assert action_log.event_id == event_id
