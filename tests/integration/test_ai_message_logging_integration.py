"""
Integration tests for complete AI message logging functionality.

This module tests the end-to-end AI message logging flow including
AI response generation, streaming, and audit database integration.
"""

import asyncio
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from botbuilder.core import TurnContext
from teams.ai.planners import ActionPlanner
from teams.state import TurnState
from teams.streaming import StreamingResponse

from src.services.audit_service import AuditService
from src.stream.stream_helper import end_stream_handler
from src.utils.ai_planner_wrapper import AuditActionPlanner
from src.utils.ai_response_logger import AIResponseLogger


class TestAIMessageLoggingIntegration:
    """Integration tests for AI message logging functionality."""

    @pytest.fixture
    def mock_turn_context(self):
        """Create a comprehensive mock TurnContext for testing."""
        context = MagicMock(spec=TurnContext)
        activity = MagicMock()
        activity.from_property = MagicMock()
        activity.from_property.id = 'integration_user_123'
        activity.from_property.name = 'Integration Test User'
        activity.id = 'activity_integration_456'
        activity.channel_id = 'channel_integration_789'
        activity.text = 'Tell me about the weather today'
        context.activity = activity
        return context

    @pytest.fixture
    def mock_turn_state(self):
        """Create a comprehensive mock TurnState for testing."""
        state = MagicMock(spec=TurnState)

        # Mock conversation with session tracking
        conversation = MagicMock()
        conversation.get = MagicMock(return_value='integration_session_123')
        state.conversation = conversation

        # Mock user information
        user = MagicMock()
        user.get = MagicMock(return_value='integration_user_123')
        state.user = user

        return state

    @pytest.fixture
    def mock_audit_service(self):
        """Create a mock audit service for integration testing."""
        with patch('src.services.audit_service.DatabaseConnectionManager'):
            service = AuditService()
            service.log_bot_response = AsyncMock(return_value='bot_response_event_123')
            service.log_error_event = AsyncMock(return_value='error_event_456')
            return service

    @pytest.mark.asyncio
    async def test_complete_ai_response_logging_flow(self, mock_turn_context, mock_turn_state, mock_audit_service):
        """Test the complete AI response logging flow from planner to audit database."""
        # Create original planner mock
        original_planner = MagicMock(spec=ActionPlanner)
        original_planner.options = MagicMock()

        # Create a plan with AI response
        ai_response_content = 'Based on current weather data, today will be sunny with a high of 75°F and low of 55°F. Perfect weather for outdoor activities!'
        mock_plan = MagicMock()
        mock_plan.response = ai_response_content
        original_planner.begin_task = AsyncMock(return_value=mock_plan)

        # Create audit planner wrapper
        audit_planner = AuditActionPlanner(original_planner)

        # Mock the AI response logger
        with patch('src.utils.ai_planner_wrapper.ai_response_logger') as mock_ai_logger:
            mock_ai_logger.log_ai_response = AsyncMock(return_value='ai_response_event_789')

            # Execute the AI planning task
            result_plan = await audit_planner.begin_task(mock_turn_context, mock_turn_state)

            # Verify the plan was returned correctly
            assert result_plan == mock_plan

            # Verify original planner was called
            original_planner.begin_task.assert_called_once_with(mock_turn_context, mock_turn_state)

            # Verify AI response was logged
            mock_ai_logger.log_ai_response.assert_called_once()
            call_args = mock_ai_logger.log_ai_response.call_args[1]

            assert call_args['context'] == mock_turn_context
            assert call_args['response_content'] == ai_response_content
            assert call_args['response_type'] == 'ai_plan_response'
            assert call_args['session_id'] == 'integration_session_123'
            assert 'duration_ms' in call_args
            assert 'metadata' in call_args

    @pytest.mark.asyncio
    async def test_streaming_ai_response_logging_flow(self, mock_turn_context, mock_turn_state):
        """Test the streaming AI response logging flow."""
        # Create streaming response mock
        streaming_content = 'The weather forecast shows... [streaming chunk 1] sunny skies ahead... [streaming chunk 2] with temperatures reaching 75°F... [streaming chunk 3] Perfect for outdoor activities!'

        streamer = MagicMock(spec=StreamingResponse)
        streamer.message = streaming_content

        # Create memory state with timing information
        memory_state = MagicMock()
        conversation = MagicMock()
        conversation.get = MagicMock(return_value='streaming_session_456')
        memory_state.conversation = conversation

        # Mock temp data for duration calculation
        temp = MagicMock()
        temp.get = MagicMock(return_value=1234567890.0)  # Start time
        memory_state.temp = temp

        # Mock the AI response logger
        with patch('src.stream.stream_helper.ai_response_logger') as mock_ai_logger:
            mock_ai_logger.log_streaming_ai_response = AsyncMock(return_value='streaming_event_123')

            # Mock time.time to simulate duration
            with patch('src.stream.stream_helper.time.time', return_value=1234567892.5):  # 2.5 seconds later
                # Mock asyncio.create_task to capture the coroutine
                with patch('src.stream.stream_helper.asyncio.create_task') as mock_create_task:
                    # Call the stream handler
                    end_stream_handler(mock_turn_context, memory_state, None, streamer)

                    # Verify task was created
                    mock_create_task.assert_called_once()

                    # Execute the task manually to test the logging
                    task_coro = mock_create_task.call_args[0][0]
                    await task_coro

                    # Verify streaming response was logged
                    mock_ai_logger.log_streaming_ai_response.assert_called_once()
                    call_args = mock_ai_logger.log_streaming_ai_response.call_args[1]

                    assert call_args['context'] == mock_turn_context
                    assert call_args['final_content'] == streaming_content
                    assert call_args['session_id'] == 'streaming_session_456'
                    assert call_args['duration_ms'] == 2500.0  # 2.5 seconds

    @pytest.mark.asyncio
    async def test_ai_error_logging_integration(self, mock_turn_context, mock_turn_state):
        """Test AI error logging integration."""
        # Create original planner that raises an error
        original_planner = MagicMock(spec=ActionPlanner)
        original_planner.options = MagicMock()
        error_message = 'OpenAI API timeout after 30 seconds'
        original_planner.begin_task = AsyncMock(side_effect=Exception(error_message))

        # Create audit planner wrapper
        audit_planner = AuditActionPlanner(original_planner)

        # Mock the AI response logger
        with patch('src.utils.ai_planner_wrapper.ai_response_logger') as mock_ai_logger:
            mock_ai_logger.log_ai_error = AsyncMock(return_value='error_event_789')

            # Execute the AI planning task and expect exception
            with pytest.raises(Exception, match=error_message):
                await audit_planner.begin_task(mock_turn_context, mock_turn_state)

            # Verify error was logged
            mock_ai_logger.log_ai_error.assert_called_once()
            call_args = mock_ai_logger.log_ai_error.call_args[1]

            assert call_args['context'] == mock_turn_context
            assert call_args['error_message'] == error_message
            assert call_args['error_type'] == 'ai_planning_error'
            assert call_args['session_id'] == 'integration_session_123'
            assert 'duration_ms' in call_args

    @pytest.mark.asyncio
    async def test_ai_response_logger_audit_service_integration(self, mock_turn_context):
        """Test AI response logger integration with audit service."""
        # Create AI response logger with mocked audit service
        ai_logger = AIResponseLogger()

        with patch.object(ai_logger, 'audit_service') as mock_audit_service:
            mock_audit_service.log_bot_response = AsyncMock(return_value='audit_event_123')

            response_content = 'This is an AI response that should be logged to the audit database.'
            session_id = 'audit_integration_session'
            duration_ms = 1750.5

            # Log the AI response
            event_id = await ai_logger.log_ai_response(
                context=mock_turn_context,
                response_content=response_content,
                response_type='ai_completion',
                session_id=session_id,
                duration_ms=duration_ms,
                metadata={'model': 'gpt-4o', 'tokens': 125},
            )

            # Verify event ID was returned
            assert event_id is not None

            # Verify audit service was called correctly
            mock_audit_service.log_bot_response.assert_called_once()
            call_args = mock_audit_service.log_bot_response.call_args[1]

            assert call_args['user_id'] == 'integration_user_123'
            assert call_args['user_name'] == 'Integration Test User'
            assert call_args['response_content'] == response_content
            assert call_args['session_id'] == session_id
            assert call_args['activity_id'] == 'activity_integration_456'
            assert call_args['channel_id'] == 'channel_integration_789'
            assert call_args['duration_ms'] == duration_ms

    @pytest.mark.asyncio
    async def test_multiple_ai_responses_session_tracking(self, mock_turn_context, mock_turn_state):
        """Test session tracking across multiple AI responses."""
        session_id = 'multi_response_session_789'

        # Update state to return consistent session ID
        mock_turn_state.conversation.get.return_value = session_id

        # Create AI response logger
        ai_logger = AIResponseLogger()

        with patch.object(ai_logger, 'audit_service') as mock_audit_service:
            mock_audit_service.log_bot_response = AsyncMock(side_effect=['event_1', 'event_2', 'event_3'])

            # Log multiple AI responses in the same session
            responses = [
                'First AI response in the conversation.',
                'Second AI response building on the previous context.',
                'Third AI response completing the conversation.',
            ]

            event_ids = []
            for i, response in enumerate(responses):
                event_id = await ai_logger.log_ai_response(
                    context=mock_turn_context,
                    response_content=response,
                    response_type=f'ai_response_{i + 1}',
                    session_id=session_id,
                    duration_ms=1000.0 + (i * 500),  # Increasing duration
                )
                event_ids.append(event_id)

            # Verify all responses were logged
            assert len(event_ids) == 3
            assert all(event_id is not None for event_id in event_ids)

            # Verify audit service was called for each response
            assert mock_audit_service.log_bot_response.call_count == 3

            # Verify all calls used the same session ID
            for call in mock_audit_service.log_bot_response.call_args_list:
                assert call[1]['session_id'] == session_id

    @pytest.mark.asyncio
    async def test_ai_logging_performance_metrics(self, mock_turn_context, mock_turn_state):
        """Test that performance metrics are properly captured in AI logging."""
        # Create original planner with simulated processing time
        original_planner = MagicMock(spec=ActionPlanner)
        original_planner.options = MagicMock()

        mock_plan = MagicMock()
        mock_plan.response = 'AI response with performance tracking'

        # Simulate processing delay
        async def delayed_begin_task(*args, **kwargs):
            await asyncio.sleep(0.1)  # 100ms delay
            return mock_plan

        original_planner.begin_task = delayed_begin_task

        # Create audit planner wrapper
        audit_planner = AuditActionPlanner(original_planner)

        # Mock the AI response logger to capture timing
        with patch('src.utils.ai_planner_wrapper.ai_response_logger') as mock_ai_logger:
            mock_ai_logger.log_ai_response = AsyncMock(return_value='perf_event_123')

            # Execute the AI planning task
            start_time = asyncio.get_event_loop().time()
            result_plan = await audit_planner.begin_task(mock_turn_context, mock_turn_state)
            end_time = asyncio.get_event_loop().time()

            # Verify timing was captured
            mock_ai_logger.log_ai_response.assert_called_once()
            call_args = mock_ai_logger.log_ai_response.call_args[1]

            # Duration should be approximately 100ms (allowing for some variance)
            duration_ms = call_args['duration_ms']
            assert 90 <= duration_ms <= 200  # Allow for timing variance

            # Verify metadata includes performance information
            metadata = call_args['metadata']
            assert 'operation_id' in metadata
            assert 'plan_type' in metadata
