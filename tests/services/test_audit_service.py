"""
Tests for the audit service.
"""

import json
from datetime import datetime
from unittest.mock import MagicMock, patch

import pytest
import pytz

from src.services.audit_service import AuditService, SessionService
from src.utils.sql.models import AuditEventType, AuditLog, AuditStatus, UserSession


class TestAuditService:
    """Test cases for the AuditService class."""

    @pytest.fixture
    def audit_service(self):
        """Create an audit service instance for testing."""
        return AuditService()

    @pytest.fixture
    def mock_db_manager(self):
        """Create a mock database manager."""
        with patch('services.audit_service.DatabaseConnectionManager') as mock:
            mock_instance = MagicMock()
            mock.return_value = mock_instance

            # Mock session scope
            mock_session = MagicMock()
            mock_instance.session_scope.return_value.__enter__ = MagicMock(return_value=mock_session)
            mock_instance.session_scope.return_value.__exit__ = MagicMock(return_value=None)

            yield mock_instance, mock_session

    def test_serialize_data(self, audit_service):
        """Test data serialization."""
        # Test normal data
        data = {'key': 'value', 'number': 123}
        result = audit_service._serialize_data(data)
        assert result == json.dumps(data, default=str, ensure_ascii=False)

        # Test None
        assert audit_service._serialize_data(None) is None

        # Test complex object that can't be serialized normally
        class CustomObject:
            def __str__(self):
                return 'custom_object'

        obj = CustomObject()
        result = audit_service._serialize_data(obj)
        assert result == 'custom_object'

    @pytest.mark.asyncio
    async def test_log_event_success(self, audit_service, mock_db_manager):
        """Test successful event logging."""
        mock_manager, mock_session = mock_db_manager

        event_id = await audit_service.log_event(
            event_type=AuditEventType.USER_MESSAGE,
            event_status=AuditStatus.SUCCESS,
            user_id='test_user',
            user_name='Test User',
            message_content='Hello, bot!',
        )

        # Verify event ID is returned
        assert event_id is not None
        assert isinstance(event_id, str)

        # Verify session.add was called
        mock_session.add.assert_called_once()

        # Verify the audit log object was created correctly
        added_log = mock_session.add.call_args[0][0]
        assert isinstance(added_log, AuditLog)
        assert added_log.event_type == AuditEventType.USER_MESSAGE.value
        assert added_log.event_status == AuditStatus.SUCCESS.value
        assert added_log.user_id == 'test_user'
        assert added_log.user_name == 'Test User'
        assert added_log.message_content == 'Hello, bot!'

    @pytest.mark.asyncio
    async def test_log_user_message(self, audit_service, mock_db_manager):
        """Test user message logging."""
        mock_manager, mock_session = mock_db_manager

        event_id = await audit_service.log_user_message(
            user_id='test_user',
            user_name='Test User',
            message_content='Hello, bot!',
            session_id='session_123',
        )

        assert event_id is not None
        mock_session.add.assert_called_once()

        added_log = mock_session.add.call_args[0][0]
        assert added_log.event_type == AuditEventType.USER_MESSAGE.value
        assert added_log.event_status == AuditStatus.SUCCESS.value
        assert added_log.session_id == 'session_123'

    @pytest.mark.asyncio
    async def test_log_bot_response(self, audit_service, mock_db_manager):
        """Test bot response logging."""
        mock_manager, mock_session = mock_db_manager

        event_id = await audit_service.log_bot_response(
            user_id='test_user',
            user_name='Test User',
            response_content='Hello, human!',
            duration_ms=150.5,
        )

        assert event_id is not None
        mock_session.add.assert_called_once()

        added_log = mock_session.add.call_args[0][0]
        assert added_log.event_type == AuditEventType.BOT_RESPONSE.value
        assert added_log.duration_ms == 150.5

    @pytest.mark.asyncio
    async def test_log_action_execution(self, audit_service, mock_db_manager):
        """Test action execution logging."""
        mock_manager, mock_session = mock_db_manager

        parameters = {'ticket_key': 'TEST-123', 'comment': 'Test comment'}
        response_data = {'result': 'success', 'ticket_id': '12345'}

        event_id = await audit_service.log_action_execution(
            action_name='create_ticket',
            user_id='test_user',
            user_name='Test User',
            parameters=parameters,
            response_data=response_data,
            event_status=AuditStatus.SUCCESS,
            duration_ms=250.0,
        )

        assert event_id is not None
        mock_session.add.assert_called_once()

        added_log = mock_session.add.call_args[0][0]
        assert added_log.event_type == AuditEventType.ACTION_EXECUTION.value
        assert added_log.action_name == 'create_ticket'
        assert json.loads(added_log.parameters) == parameters
        assert json.loads(added_log.response_data) == response_data

    @pytest.mark.asyncio
    async def test_log_error_event(self, audit_service, mock_db_manager):
        """Test error event logging."""
        mock_manager, mock_session = mock_db_manager

        error_message = 'Something went wrong'
        stack_trace = 'Traceback (most recent call last):\n  File...'

        event_id = await audit_service.log_error_event(
            error_message=error_message,
            stack_trace=stack_trace,
            user_id='test_user',
            action_name='create_ticket',
        )

        assert event_id is not None
        mock_session.add.assert_called_once()

        added_log = mock_session.add.call_args[0][0]
        assert added_log.event_type == AuditEventType.ERROR.value
        assert added_log.event_status == AuditStatus.FAILURE.value
        assert added_log.error_message == error_message
        assert added_log.stack_trace == stack_trace

    @pytest.mark.asyncio
    async def test_log_api_call(self, audit_service, mock_db_manager):
        """Test API call logging."""
        mock_manager, mock_session = mock_db_manager

        event_id = await audit_service.log_api_call(
            endpoint='/api/messages',
            method='POST',
            event_status=AuditStatus.SUCCESS,
            user_id='test_user',
            duration_ms=100.0,
        )

        assert event_id is not None
        mock_session.add.assert_called_once()

        added_log = mock_session.add.call_args[0][0]
        assert added_log.event_type == AuditEventType.API_CALL.value
        assert added_log.endpoint == '/api/messages'
        assert added_log.method == 'POST'

    @pytest.mark.asyncio
    async def test_log_system_event(self, audit_service, mock_db_manager):
        """Test system event logging."""
        mock_manager, mock_session = mock_db_manager

        event_id = await audit_service.log_system_event(
            event_description='Application started',
            event_status=AuditStatus.SUCCESS,
            parameters={'startup_time': 1234567890},
        )

        assert event_id is not None
        mock_session.add.assert_called_once()

        added_log = mock_session.add.call_args[0][0]
        assert added_log.event_type == AuditEventType.SYSTEM_EVENT.value
        assert added_log.message_content == 'Application started'


class TestSessionService:
    """Test cases for the SessionService class."""

    @pytest.fixture
    def session_service(self):
        """Create a session service instance for testing."""
        return SessionService()

    @pytest.fixture
    def mock_db_manager(self):
        """Create a mock database manager."""
        with patch('services.audit_service.DatabaseConnectionManager') as mock:
            mock_instance = MagicMock()
            mock.return_value = mock_instance

            # Mock session scope
            mock_session = MagicMock()
            mock_instance.session_scope.return_value.__enter__ = MagicMock(return_value=mock_session)
            mock_instance.session_scope.return_value.__exit__ = MagicMock(return_value=None)

            yield mock_instance, mock_session

    @pytest.mark.asyncio
    async def test_get_or_create_session_new(self, session_service, mock_db_manager):
        """Test creating a new session when none exists."""
        mock_manager, mock_session = mock_db_manager

        # Mock query to return no existing session
        mock_session.query.return_value.filter.return_value.order_by.return_value.first.return_value = None

        session_id = await session_service.get_or_create_session(
            user_id='test_user',
            user_name='Test User',
            channel_id='test_channel',
        )

        assert session_id is not None
        assert isinstance(session_id, str)

        # Verify a new session was added
        mock_session.add.assert_called_once()
        added_session = mock_session.add.call_args[0][0]
        assert isinstance(added_session, UserSession)
        assert added_session.user_id == 'test_user'
        assert added_session.user_name == 'Test User'
        assert added_session.channel_id == 'test_channel'

    @pytest.mark.asyncio
    async def test_get_or_create_session_existing(self, session_service, mock_db_manager):
        """Test updating an existing session."""
        mock_manager, mock_session = mock_db_manager

        # Mock existing session
        existing_session = MagicMock()
        existing_session.session_id = 'existing_session_123'
        existing_session.first_activity_at = datetime.now(pytz.timezone('Australia/Sydney'))
        mock_session.query.return_value.filter.return_value.order_by.return_value.first.return_value = existing_session

        session_id = await session_service.get_or_create_session(
            user_id='test_user',
            user_name='Test User',
        )

        assert session_id == 'existing_session_123'

        # Verify no new session was added
        mock_session.add.assert_not_called()

        # Verify existing session was updated
        assert existing_session.last_activity_at is not None
        assert existing_session.session_duration_minutes is not None

    @pytest.mark.asyncio
    async def test_update_session_metrics(self, session_service, mock_db_manager):
        """Test updating session metrics."""
        mock_manager, mock_session = mock_db_manager

        # Mock existing session
        existing_session = MagicMock()
        existing_session.message_count = 5
        existing_session.action_count = 2
        existing_session.error_count = 0
        existing_session.first_activity_at = datetime.now(pytz.timezone('Australia/Sydney'))
        mock_session.query.return_value.filter.return_value.first.return_value = existing_session

        await session_service.update_session_metrics(
            session_id='test_session',
            message_increment=1,
            action_increment=1,
            error_increment=0,
        )

        # Verify metrics were updated
        assert existing_session.message_count == 6
        assert existing_session.action_count == 3
        assert existing_session.error_count == 0
        assert existing_session.last_activity_at is not None
        assert existing_session.session_duration_minutes is not None

    @pytest.mark.asyncio
    async def test_get_session_info(self, session_service, mock_db_manager):
        """Test getting session information."""
        mock_manager, mock_session = mock_db_manager

        # Mock existing session
        existing_session = MagicMock()
        existing_session.session_id = 'test_session'
        existing_session.user_id = 'test_user'
        existing_session.user_name = 'Test User'
        existing_session.message_count = 10
        existing_session.action_count = 5
        existing_session.error_count = 1
        mock_session.query.return_value.filter.return_value.first.return_value = existing_session

        session_info = await session_service.get_session_info('test_session')

        assert session_info is not None
        assert session_info['session_id'] == 'test_session'
        assert session_info['user_id'] == 'test_user'
        assert session_info['user_name'] == 'Test User'
        assert session_info['message_count'] == 10
        assert session_info['action_count'] == 5
        assert session_info['error_count'] == 1

    @pytest.mark.asyncio
    async def test_get_session_info_not_found(self, session_service, mock_db_manager):
        """Test getting session information when session doesn't exist."""
        mock_manager, mock_session = mock_db_manager

        # Mock no session found
        mock_session.query.return_value.filter.return_value.first.return_value = None

        session_info = await session_service.get_session_info('nonexistent_session')

        assert session_info is None
