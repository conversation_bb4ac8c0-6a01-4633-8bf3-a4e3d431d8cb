"""Tests for the Jira action functions."""

from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from teams.ai.actions import ActionTurnContext

from src.state import AppTurnState
from src.tools.actions import get_tickets, retrieve_jira_tickets

# Get the original function implementation without the decorator
original_retrieve_jira_tickets = retrieve_jira_tickets.__wrapped__
original_get_tickets = get_tickets.__wrapped__


@pytest.fixture
def mock_jira():
    """Create a mock Jira client."""
    mock = MagicMock()
    mock.myself.return_value = {'accountId': 'test-account-id'}
    mock.jql.return_value = {
        'issues': [
            {
                'key': 'TEST-1',
                'fields': {
                    'summary': 'Test ticket 1',
                    'description': 'Test description 1',
                    'status': {'name': 'To Do'},
                },
            },
            {
                'key': 'TEST-2',
                'fields': {
                    'summary': 'Test ticket 2',
                    'description': 'Test description 2',
                    'status': {'name': 'In Progress'},
                },
            },
        ]
    }
    return mock


@pytest.fixture
def mock_context():
    """Create a mock ActionTurnContext."""
    context = MagicMock(spec=ActionTurnContext)
    context.data = {}
    context.send_activity = AsyncMock()
    return context


@pytest.fixture
def mock_state():
    """Create a mock AppTurnState."""
    return MagicMock(spec=AppTurnState)


@pytest.mark.asyncio
async def test_retrieve_jira_tickets(mock_context, mock_state, mock_jira):
    """Test that retrieve_jira_tickets returns the expected data."""
    # Test with default parameters using the unwrapped function
    issues = await original_retrieve_jira_tickets(mock_context, mock_state, mock_jira)

    # Verify the Jira client was called correctly
    mock_jira.myself.assert_called_once()
    mock_jira.jql.assert_called_once()

    # Verify the correct query was constructed
    call_args = mock_jira.jql.call_args[0][0]
    assert "assignee = 'test-account-id'" in call_args

    # Verify the correct data was returned
    assert len(issues) == 2
    assert issues[0]['key'] == 'TEST-1'
    assert issues[1]['key'] == 'TEST-2'


@pytest.mark.asyncio
async def test_retrieve_jira_tickets_with_query(mock_context, mock_state, mock_jira):
    """Test that retrieve_jira_tickets handles custom queries correctly."""
    # Set a custom query
    mock_context.data = {'query': 'project = TEST'}

    issues = await original_retrieve_jira_tickets(mock_context, mock_state, mock_jira)

    # Verify the correct query was constructed
    call_args = mock_jira.jql.call_args[0][0]
    assert "assignee = 'test-account-id' AND project = TEST" in call_args

    # Verify the correct data was returned
    assert len(issues) == 2


@pytest.mark.asyncio
async def test_get_tickets(mock_context, mock_state, mock_jira):
    """Test that get_tickets calls retrieve_jira_tickets and formats the results."""
    with patch(
        'src.tools.actions.retrieve_jira_tickets', return_value=mock_jira.jql.return_value['issues']
    ) as mock_retrieve:
        with patch('src.tools.actions.create_ticket_list_card') as mock_create_card:
            # Call the function
            result = await original_get_tickets(mock_context, mock_state, mock_jira)

            # Verify retrieve_jira_tickets was called
            mock_retrieve.assert_called_once_with(mock_context, mock_state, mock_jira)

            # Verify the card was created with the correct data
            mock_create_card.assert_called_once_with(mock_jira.jql.return_value['issues'])

            # Verify the activity was sent
            mock_context.send_activity.assert_called_once()

            # Verify the correct result was returned
            assert 'Already returned the results' in result
