"""
Tests for enhanced stream helper functionality.

This module tests the enhanced end_stream_handler that captures
AI streaming responses for logging.
"""

from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from botbuilder.core import TurnContext
from teams.ai.models import PromptResponse
from teams.state import MemoryBase
from teams.streaming import StreamingResponse

from src.stream.stream_helper import _log_streaming_response, end_stream_handler


class TestEnhancedStreamHelper:
    """Test cases for enhanced stream helper functionality."""

    @pytest.fixture
    def mock_turn_context(self):
        """Create a mock TurnContext for testing."""
        context = MagicMock(spec=TurnContext)
        activity = MagicMock()
        activity.from_property = MagicMock()
        activity.from_property.id = 'test_user_123'
        activity.from_property.name = 'Test User'
        activity.id = 'activity_456'
        activity.channel_id = 'channel_789'
        context.activity = activity
        return context

    @pytest.fixture
    def mock_memory_state(self):
        """Create a mock MemoryBase state for testing."""
        state = MagicMock(spec=MemoryBase)

        # Mock conversation attribute
        conversation = MagicMock()
        conversation.get = MagicMock(return_value='session_123')
        state.conversation = conversation

        # Mock temp attribute for timing
        temp = MagicMock()
        temp.get = MagicMock(return_value=1234567890.0)  # Mock start time
        state.temp = temp

        return state

    @pytest.fixture
    def mock_streaming_response(self):
        """Create a mock StreamingResponse for testing."""
        streamer = MagicMock(spec=StreamingResponse)
        streamer.message = 'This is the complete AI streaming response that was built up over multiple chunks.'
        return streamer

    @pytest.fixture
    def mock_prompt_response(self):
        """Create a mock PromptResponse for testing."""
        response = MagicMock(spec=PromptResponse)
        return response

    @pytest.fixture
    def mock_ai_response_logger(self):
        """Create a mock AI response logger."""
        with patch('src.stream.stream_helper.ai_response_logger') as mock_logger:
            mock_logger.log_streaming_ai_response = AsyncMock(return_value='event_123')
            yield mock_logger

    def test_end_stream_handler_no_streamer(self, mock_turn_context, mock_memory_state, mock_prompt_response):
        """Test end_stream_handler with no streamer."""
        # Call with None streamer
        result = end_stream_handler(mock_turn_context, mock_memory_state, mock_prompt_response, None)

        # Should return early without error
        assert result is None

    @patch('src.stream.stream_helper.asyncio.create_task')
    def test_end_stream_handler_with_streamer(
        self, mock_create_task, mock_turn_context, mock_memory_state, mock_prompt_response, mock_streaming_response
    ):
        """Test end_stream_handler with valid streamer."""
        # Call the handler
        end_stream_handler(mock_turn_context, mock_memory_state, mock_prompt_response, mock_streaming_response)

        # Verify asyncio.create_task was called to log the response
        mock_create_task.assert_called_once()

        # Verify the task was created with the correct function
        call_args = mock_create_task.call_args[0][0]
        assert hasattr(call_args, '__name__') or hasattr(call_args, 'cr_code')

    @patch('src.stream.stream_helper.time.time', return_value=1234567895.0)  # 5 seconds later
    def test_end_stream_handler_duration_calculation(
        self, mock_time, mock_turn_context, mock_memory_state, mock_prompt_response, mock_streaming_response
    ):
        """Test duration calculation in end_stream_handler."""
        with patch('src.stream.stream_helper.asyncio.create_task') as mock_create_task:
            # Call the handler
            end_stream_handler(mock_turn_context, mock_memory_state, mock_prompt_response, mock_streaming_response)

            # Verify task was created
            mock_create_task.assert_called_once()

    def test_end_stream_handler_no_message(self, mock_turn_context, mock_memory_state, mock_prompt_response):
        """Test end_stream_handler with streamer that has no message."""
        streamer = MagicMock(spec=StreamingResponse)
        streamer.message = None

        with patch('src.stream.stream_helper.asyncio.create_task') as mock_create_task:
            # Call the handler
            end_stream_handler(mock_turn_context, mock_memory_state, mock_prompt_response, streamer)

            # Should not create task since there's no message
            mock_create_task.assert_not_called()

    def test_end_stream_handler_empty_message(self, mock_turn_context, mock_memory_state, mock_prompt_response):
        """Test end_stream_handler with streamer that has empty message."""
        streamer = MagicMock(spec=StreamingResponse)
        streamer.message = ''

        with patch('src.stream.stream_helper.asyncio.create_task') as mock_create_task:
            # Call the handler
            end_stream_handler(mock_turn_context, mock_memory_state, mock_prompt_response, streamer)

            # Should not create task since message is empty
            mock_create_task.assert_not_called()

    def test_end_stream_handler_state_without_conversation(
        self, mock_turn_context, mock_prompt_response, mock_streaming_response
    ):
        """Test end_stream_handler with state that has no conversation attribute."""
        state = MagicMock(spec=MemoryBase)
        # No conversation attribute

        with patch('src.stream.stream_helper.asyncio.create_task') as mock_create_task:
            # Call the handler - should not raise exception
            end_stream_handler(mock_turn_context, state, mock_prompt_response, mock_streaming_response)

            # Should still create task even without session info
            mock_create_task.assert_called_once()

    @pytest.mark.asyncio
    async def test_log_streaming_response_success(self, mock_turn_context, mock_ai_response_logger):
        """Test successful streaming response logging."""
        final_content = 'This is the complete streamed AI response.'
        session_id = 'session_456'
        duration_ms = 2500.0

        # Call the logging function
        await _log_streaming_response(
            context=mock_turn_context,
            final_content=final_content,
            session_id=session_id,
            duration_ms=duration_ms,
        )

        # Verify AI response logger was called
        mock_ai_response_logger.log_streaming_ai_response.assert_called_once()
        call_args = mock_ai_response_logger.log_streaming_ai_response.call_args[1]

        assert call_args['context'] == mock_turn_context
        assert call_args['final_content'] == final_content
        assert call_args['session_id'] == session_id
        assert call_args['duration_ms'] == duration_ms

    @pytest.mark.asyncio
    async def test_log_streaming_response_no_session(self, mock_turn_context, mock_ai_response_logger):
        """Test streaming response logging without session ID."""
        final_content = 'AI response without session'

        # Call the logging function
        await _log_streaming_response(
            context=mock_turn_context,
            final_content=final_content,
            session_id=None,
            duration_ms=None,
        )

        # Verify AI response logger was called with None values
        mock_ai_response_logger.log_streaming_ai_response.assert_called_once()
        call_args = mock_ai_response_logger.log_streaming_ai_response.call_args[1]

        assert call_args['session_id'] is None
        assert call_args['duration_ms'] is None

    @pytest.mark.asyncio
    async def test_log_streaming_response_error_handling(self, mock_turn_context, mock_ai_response_logger):
        """Test error handling in streaming response logging."""
        # Make AI response logger fail
        mock_ai_response_logger.log_streaming_ai_response.side_effect = Exception('Logging error')

        final_content = 'AI response with logging error'

        # Call the logging function - should not raise exception
        await _log_streaming_response(
            context=mock_turn_context,
            final_content=final_content,
        )

        # Verify logger was called but error was handled gracefully
        mock_ai_response_logger.log_streaming_ai_response.assert_called_once()

    @pytest.mark.asyncio
    async def test_log_streaming_response_long_content(self, mock_turn_context, mock_ai_response_logger):
        """Test streaming response logging with very long content."""
        # Create very long content
        long_content = 'This is a very long streaming response. ' * 200  # 8000+ characters

        # Call the logging function
        await _log_streaming_response(
            context=mock_turn_context,
            final_content=long_content,
            session_id='session_789',
            duration_ms=5000.0,
        )

        # Verify AI response logger was called with full content
        mock_ai_response_logger.log_streaming_ai_response.assert_called_once()
        call_args = mock_ai_response_logger.log_streaming_ai_response.call_args[1]

        assert call_args['final_content'] == long_content
        assert len(call_args['final_content']) >= 8000

    def test_end_stream_handler_state_attribute_errors(
        self, mock_turn_context, mock_prompt_response, mock_streaming_response
    ):
        """Test end_stream_handler with state that raises AttributeError."""
        state = MagicMock(spec=MemoryBase)

        # Make getattr raise AttributeError
        def side_effect(obj, attr, default=None):
            if attr == 'conversation':
                raise AttributeError('No conversation attribute')
            return default

        with patch('src.stream.stream_helper.getattr', side_effect=side_effect):
            with patch('src.stream.stream_helper.asyncio.create_task') as mock_create_task:
                # Call the handler - should not raise exception
                end_stream_handler(mock_turn_context, state, mock_prompt_response, mock_streaming_response)

                # Should still create task despite attribute errors
                mock_create_task.assert_called_once()

    @patch('src.stream.stream_helper.logger')
    @pytest.mark.asyncio
    async def test_log_streaming_response_logging_calls(self, mock_logger, mock_turn_context, mock_ai_response_logger):
        """Test that proper logging calls are made during streaming response logging."""
        final_content = 'Test streaming response'
        session_id = 'session_test'
        duration_ms = 1500.0

        # Call the logging function
        await _log_streaming_response(
            context=mock_turn_context,
            final_content=final_content,
            session_id=session_id,
            duration_ms=duration_ms,
        )

        # Verify structured logging was called
        mock_logger.info.assert_called_once()

        # Verify the log message contains expected information
        log_call_args = mock_logger.info.call_args[0][0]
        assert 'AI streaming completed' in log_call_args
        assert str(len(final_content)) in log_call_args
        assert '1500.000ms' in log_call_args
