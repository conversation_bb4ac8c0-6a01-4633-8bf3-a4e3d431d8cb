"""
Tests for the SQL token storage.
"""

from unittest.mock import MagicMock, patch

import pendulum
import pytest
from sqlalchemy.exc import SQLAlchemyError

from src.auth.token_manager import SQLTokenStorage


class TestSQLTokenStorage:
    """Test cases for the SQLTokenStorage class."""

    @pytest.fixture(autouse=True)
    def setup_method(self):
        """Set up test fixtures."""
        # Mock the database connection manager
        with patch('src.auth.token_manager.DatabaseConnectionManager') as mock_db_manager_class:
            mock_db_manager = MagicMock()
            mock_db_manager_class.return_value = mock_db_manager

            # Mock the create_tables function
            with patch('src.auth.token_manager.create_tables') as mock_create_tables:
                # Create a storage instance
                storage = SQLTokenStorage()
                # Mock the auth_states dictionary
                storage.auth_states = {}

                # Set up common test data
                user_id = 'test_user'
                user_name = 'Test User'
                user_suffix = user_id[-5:]
                access_token = 'test_access_token'
                refresh_token = 'test_refresh_token'
                expires_at = pendulum.now().add(hours=1).isoformat()
                token_data = {
                    'access_token': access_token,
                    'refresh_token': refresh_token,
                    'expires_at': expires_at,
                    'user_name': user_name,
                    'cloud_id': 'test_cloud_id',
                }

                # Make these available to the test methods
                self.mock_db_manager_class = mock_db_manager_class
                self.mock_db_manager = mock_db_manager
                self.mock_create_tables = mock_create_tables
                self.storage = storage
                self.user_id = user_id
                self.user_name = user_name
                self.user_suffix = user_suffix
                self.access_token = access_token
                self.refresh_token = refresh_token
                self.expires_at = expires_at
                self.token_data = token_data

                yield

    @pytest.mark.asyncio
    async def test_save_tokens(self):
        """Test saving tokens."""
        # Set up the mock session
        mock_session = MagicMock()
        mock_query = MagicMock()
        mock_query.filter.return_value.first.return_value = None
        mock_session.query.return_value = mock_query

        # Set up the mock session context manager
        self.mock_db_manager.get_session.return_value.__enter__.return_value = mock_session

        # Call the method
        await self.storage.save_tokens(self.user_id, self.user_name, self.token_data)

        # Assertions
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_save_tokens_failure(self):
        """Test handling failure when saving tokens."""
        # Set up the mock session to raise an exception
        mock_session = MagicMock()
        mock_session.query.side_effect = SQLAlchemyError('Test error')

        # Set up the mock session context manager
        self.mock_db_manager.get_session.return_value.__enter__.return_value = mock_session

        # Call the method
        await self.storage.save_tokens(self.user_id, self.user_name, self.token_data)

        # No assertions needed for this test case as we're just testing error handling

    def test_get_tokens_from_db(self):
        """Test getting tokens from the database."""
        # Create a mock token with attributes
        mock_token = MagicMock()
        mock_token.user_id = self.user_id
        mock_token.user_name = 'Test User'
        mock_token.auth_token = self.access_token
        mock_token.refresh_token = self.refresh_token
        mock_token.expire_at = pendulum.parse(self.expires_at)
        mock_token.cloud_id = 'test_cloud_id'

        # Mock session and query
        mock_session = MagicMock()
        mock_query = MagicMock()
        mock_query.filter.return_value.first.return_value = mock_token
        mock_session.query.return_value = mock_query

        # Set up the mock session context manager
        self.mock_db_manager.get_session.return_value.__enter__.return_value = mock_session

        # Call the method
        result = self.storage.get_tokens(self.user_id)

        # Assertions
        assert result['access_token'] == self.access_token
        assert result['refresh_token'] == self.refresh_token
        assert pendulum.parse(result['expires_at']) == mock_token.expire_at
        assert result['user_name'] == mock_token.user_name
        assert result['cloud_id'] == mock_token.cloud_id

    # Removed test_get_tokens_from_state as we no longer use state for token storage

    def test_get_tokens_not_found(self):
        """Test getting tokens when not found anywhere."""
        # Mock session and query to return None
        mock_session = MagicMock()
        mock_query = MagicMock()
        mock_query.filter.return_value.first.return_value = None
        mock_session.query.return_value = mock_query

        # Set up the mock session context manager
        self.mock_db_manager.get_session.return_value.__enter__.return_value = mock_session

        # No need to set up state mock as we no longer use state for token storage

        # Call the method
        result = self.storage.get_tokens(self.user_id)

        # Assertions
        assert result['access_token'] is None
        assert result['refresh_token'] is None
        assert result['expires_at'] is None
        assert result['user_name'] is None
        assert result['cloud_id'] is None

    def test_save_auth_state(self):
        """Test saving auth state (deprecated but kept for compatibility)."""
        # Set up test data
        state = 'test_state'

        # Mock the logger
        with patch('src.auth.token_manager.logger') as mock_logger:
            # Call the method
            self.storage.save_auth_state(self.user_id, state)

            # Assertions
            assert self.storage.auth_states[self.user_id] == state
            # Check that we log a debug message about this being deprecated
            mock_logger.debug.assert_called_with(f'Auth state saved for user {self.user_id} (deprecated)')

    def test_get_auth_state_from_memory(self):
        """Test getting auth state from memory (deprecated but kept for compatibility)."""
        # Set up test data
        state = 'test_state'
        self.storage.auth_states[self.user_id] = state

        # Call the method
        result = self.storage.get_auth_state(self.user_id)

        # Assertions
        assert result == state
