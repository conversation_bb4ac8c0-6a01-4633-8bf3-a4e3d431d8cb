"""
Tests for the SQL token storage migration.
"""

from unittest.mock import Magic<PERSON>ock, patch

import pendulum
import pytest

from src.auth.token_manager import SQLTokenStorage


class TestSQLTokenMigration:
    """Test cases for the SQL token storage migration."""

    @pytest.fixture(autouse=True)
    def setup_method(self):
        """Set up test fixtures."""
        # Mock the database connection manager
        with patch('src.auth.token_manager.DatabaseConnectionManager') as mock_db_manager_class:
            mock_db_manager = MagicMock()
            mock_db_manager_class.return_value = mock_db_manager

            # Mock the create_tables function
            with patch('src.auth.token_manager.create_tables') as mock_create_tables:
                # Create a storage instance
                storage = SQLTokenStorage()

                # Test data
                user_id = 'test_user_123'
                user_name = 'Phillip Hu'
                user_suffix = user_id[-5:]
                access_token = 'test_access_token'
                refresh_token = 'test_refresh_token'
                expires_at = pendulum.now().add(hours=1).isoformat()
                token_data = {
                    'access_token': access_token,
                    'refresh_token': refresh_token,
                    'expires_at': expires_at,
                    'username': 'Test User',  # Add username field
                    'cloud_id': 'test_cloud_id',  # Add cloud_id field
                }

                # Make these available to the test methods
                self.mock_db_manager_class = mock_db_manager_class
                self.mock_db_manager = mock_db_manager
                self.mock_create_tables = mock_create_tables
                self.storage = storage
                self.user_id = user_id
                self.user_name = user_name
                self.user_suffix = user_suffix
                self.access_token = access_token
                self.refresh_token = refresh_token
                self.expires_at = expires_at
                self.token_data = token_data

                yield

    @pytest.mark.asyncio
    async def test_save_tokens_to_sql(self):
        """Test saving tokens to SQL database."""
        # Mock session and query
        mock_session = MagicMock()
        mock_query = MagicMock()
        mock_query.filter.return_value.first.return_value = None
        mock_session.query.return_value = mock_query

        # Set up the mock session context manager
        self.mock_db_manager.get_session.return_value.__enter__.return_value = mock_session

        # Call the method
        await self.storage.save_tokens(self.user_id, self.user_name, self.token_data)

        # Assertions
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()

    def test_get_tokens_from_sql(self):
        """Test getting tokens from SQL database."""
        # Create a mock token with attributes
        mock_token = MagicMock()
        mock_token.user_id = self.user_id
        mock_token.user_name = 'Test User'
        mock_token.auth_token = self.access_token
        mock_token.refresh_token = self.refresh_token
        mock_token.expire_at = pendulum.parse(self.expires_at)
        mock_token.cloud_id = 'test_cloud_id'

        # Mock session and query
        mock_session = MagicMock()
        mock_query = MagicMock()
        mock_query.filter.return_value.first.return_value = mock_token
        mock_session.query.return_value = mock_query

        # Set up the mock session context manager
        self.mock_db_manager.get_session.return_value.__enter__.return_value = mock_session

        # Call the method
        result = self.storage.get_tokens(self.user_id)

        # Assertions
        assert result['access_token'] == self.access_token
        assert result['refresh_token'] == self.refresh_token
        assert 'expires_at' in result
        assert result['user_name'] == 'Test User'
        assert result['cloud_id'] == 'test_cloud_id'

    # Removed test_backward_compatibility as we no longer support state-based storage
