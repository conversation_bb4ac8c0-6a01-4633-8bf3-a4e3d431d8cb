"""
Tests for AI response logging functionality.

This module tests the AIResponseLogger class and its integration
with the existing Loguru logging and audit system.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from botbuilder.core import TurnContext
from botbuilder.schema import Activity, ActivityTypes

from src.utils.ai_response_logger import AIResponseLogger, ai_response_logger
from src.utils.sql.models import AuditEventType, AuditStatus


class TestAIResponseLogger:
    """Test cases for AI response logging functionality."""

    @pytest.fixture
    def mock_turn_context(self):
        """Create a mock TurnContext for testing."""
        context = MagicMock(spec=TurnContext)
        activity = MagicMock()
        activity.from_property = MagicMock()
        activity.from_property.id = 'test_user_123'
        activity.from_property.name = 'Test User'
        activity.id = 'activity_456'
        activity.channel_id = 'channel_789'
        context.activity = activity
        return context

    @pytest.fixture
    def mock_audit_service(self):
        """Create a mock AuditService for testing."""
        with patch('src.utils.ai_response_logger.AuditService') as mock_service:
            service_instance = AsyncMock()
            mock_service.return_value = service_instance
            yield service_instance

    @pytest.mark.asyncio
    async def test_ai_response_logger_initialization(self):
        """Test AI response logger initialization."""
        logger = AIResponseLogger()
        assert logger.audit_service is not None

    @pytest.mark.asyncio
    async def test_log_ai_response_success(self, mock_turn_context, mock_audit_service):
        """Test successful AI response logging."""
        logger = AIResponseLogger()
        logger.audit_service = mock_audit_service
        
        response_content = "This is an AI-generated response to help you with your query."
        duration_ms = 1250.5
        metadata = {"model": "gpt-4o", "tokens": 150}
        
        # Log the AI response
        event_id = await logger.log_ai_response(
            context=mock_turn_context,
            response_content=response_content,
            response_type="ai_completion",
            session_id="session_123",
            duration_ms=duration_ms,
            metadata=metadata,
        )
        
        # Verify event ID is returned
        assert event_id is not None
        assert isinstance(event_id, str)
        
        # Verify audit service was called
        mock_audit_service.log_bot_response.assert_called_once()
        call_args = mock_audit_service.log_bot_response.call_args[1]
        
        assert call_args['user_id'] == 'test_user_123'
        assert call_args['user_name'] == 'Test User'
        assert call_args['response_content'] == response_content
        assert call_args['session_id'] == 'session_123'
        assert call_args['duration_ms'] == duration_ms

    @pytest.mark.asyncio
    async def test_log_streaming_ai_response(self, mock_turn_context, mock_audit_service):
        """Test streaming AI response logging."""
        logger = AIResponseLogger()
        logger.audit_service = mock_audit_service
        
        final_content = "This is the complete streamed AI response."
        duration_ms = 2500.0
        chunk_count = 15
        
        # Log the streaming response
        event_id = await logger.log_streaming_ai_response(
            context=mock_turn_context,
            final_content=final_content,
            session_id="session_456",
            duration_ms=duration_ms,
            chunk_count=chunk_count,
        )
        
        # Verify event ID is returned
        assert event_id is not None
        
        # Verify audit service was called with streaming metadata
        mock_audit_service.log_bot_response.assert_called_once()
        call_args = mock_audit_service.log_bot_response.call_args[1]
        
        assert call_args['response_content'] == final_content
        assert call_args['duration_ms'] == duration_ms

    @pytest.mark.asyncio
    async def test_log_ai_error(self, mock_turn_context, mock_audit_service):
        """Test AI error logging."""
        logger = AIResponseLogger()
        logger.audit_service = mock_audit_service
        
        error_message = "AI model timeout error"
        error_type = "ai_timeout"
        stack_trace = "Traceback (most recent call last):\n  File..."
        
        # Log the AI error
        event_id = await logger.log_ai_error(
            context=mock_turn_context,
            error_message=error_message,
            error_type=error_type,
            session_id="session_789",
            duration_ms=5000.0,
            stack_trace=stack_trace,
        )
        
        # Verify event ID is returned
        assert event_id is not None
        
        # Verify audit service was called
        mock_audit_service.log_error_event.assert_called_once()
        call_args = mock_audit_service.log_error_event.call_args[1]
        
        assert call_args['error_message'] == error_message
        assert call_args['stack_trace'] == stack_trace
        assert call_args['action_name'] == error_type

    @pytest.mark.asyncio
    async def test_log_ai_response_no_user_context(self, mock_audit_service):
        """Test AI response logging with no user context."""
        logger = AIResponseLogger()
        logger.audit_service = mock_audit_service
        
        # Create context with no user information
        context = MagicMock(spec=TurnContext)
        context.activity = None
        
        response_content = "AI response without user context"
        
        # Log the AI response
        event_id = await logger.log_ai_response(
            context=context,
            response_content=response_content,
        )
        
        # Verify event ID is still returned
        assert event_id is not None
        
        # Verify audit service was not called (no user ID)
        mock_audit_service.log_bot_response.assert_not_called()

    @pytest.mark.asyncio
    async def test_log_ai_response_audit_service_error(self, mock_turn_context, mock_audit_service):
        """Test AI response logging when audit service fails."""
        logger = AIResponseLogger()
        logger.audit_service = mock_audit_service
        
        # Make audit service fail
        mock_audit_service.log_bot_response.side_effect = Exception("Database connection error")
        
        response_content = "AI response with audit failure"
        
        # Log the AI response - should not raise exception
        event_id = await logger.log_ai_response(
            context=mock_turn_context,
            response_content=response_content,
        )
        
        # Verify event ID is still returned
        assert event_id is not None
        
        # Verify audit service was called but failed gracefully
        mock_audit_service.log_bot_response.assert_called_once()

    @pytest.mark.asyncio
    async def test_global_ai_response_logger_instance(self):
        """Test that the global ai_response_logger instance is available."""
        assert ai_response_logger is not None
        assert isinstance(ai_response_logger, AIResponseLogger)

    @pytest.mark.asyncio
    async def test_log_ai_response_with_long_content(self, mock_turn_context, mock_audit_service):
        """Test AI response logging with very long content."""
        logger = AIResponseLogger()
        logger.audit_service = mock_audit_service
        
        # Create a very long response
        long_response = "This is a very long AI response. " * 100  # 3400+ characters
        
        # Log the AI response
        event_id = await logger.log_ai_response(
            context=mock_turn_context,
            response_content=long_response,
        )
        
        # Verify event ID is returned
        assert event_id is not None
        
        # Verify audit service was called with full content
        mock_audit_service.log_bot_response.assert_called_once()
        call_args = mock_audit_service.log_bot_response.call_args[1]
        assert call_args['response_content'] == long_response

    @pytest.mark.asyncio
    async def test_log_ai_response_with_metadata(self, mock_turn_context, mock_audit_service):
        """Test AI response logging with complex metadata."""
        logger = AIResponseLogger()
        logger.audit_service = mock_audit_service
        
        complex_metadata = {
            "model": "gpt-4o",
            "temperature": 0.7,
            "max_tokens": 1000,
            "prompt_tokens": 150,
            "completion_tokens": 200,
            "total_tokens": 350,
            "finish_reason": "stop",
            "nested_data": {
                "performance": {"latency_ms": 1200},
                "quality": {"confidence": 0.95}
            }
        }
        
        # Log the AI response with complex metadata
        event_id = await logger.log_ai_response(
            context=mock_turn_context,
            response_content="AI response with complex metadata",
            metadata=complex_metadata,
        )
        
        # Verify event ID is returned
        assert event_id is not None
        
        # Verify audit service was called
        mock_audit_service.log_bot_response.assert_called_once()
