"""
Comprehensive test suite for the enhanced audit system.

This module tests the text sanitization, enhanced audit service,
privacy controls, and integration components.
"""

from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from src.services.enhanced_audit_service import (
    DataClassifier,
    EnhancedAuditService,
    PerformanceMonitor,
    PrivacyConfig,
    RetentionPolicy,
)
from src.utils.enhanced_audit_decorators import (
    EnhancedAuditTurnContext,
    enhanced_audit_action,
    enhanced_audit_message_handler,
)
from src.utils.sql.models import AuditEventType
from src.utils.text_sanitizer import ContentType, SensitivityLevel, TextContentSanitizer


class TestTextContentSanitizer:
    """Test cases for the TextContentSanitizer class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.sanitizer = TextContentSanitizer()

    def test_email_detection_and_masking(self):
        """Test email detection and masking functionality."""
        content = '<NAME_EMAIL> for more information.'

        sanitized = self.sanitizer.sanitize_content(content, ContentType.USER_MESSAGE)

        assert sanitized.sanitization_applied
        assert 'email' in sanitized.detected_patterns
        assert 'j***@e******.com' in sanitized.sanitized_content
        assert sanitized.sensitivity_level == SensitivityLevel.CONFIDENTIAL

    def test_phone_number_detection_and_masking(self):
        """Test phone number detection and masking functionality."""
        content = 'Call me at (************* tomorrow.'

        sanitized = self.sanitizer.sanitize_content(content, ContentType.USER_MESSAGE)

        assert sanitized.sanitization_applied
        assert 'phone' in sanitized.detected_patterns
        assert '(***) ***-4567' in sanitized.sanitized_content
        assert sanitized.sensitivity_level == SensitivityLevel.CONFIDENTIAL

    def test_credit_card_detection_and_redaction(self):
        """Test credit card detection and redaction functionality."""
        content = 'My card number is 4532 1234 5678 9012.'

        sanitized = self.sanitizer.sanitize_content(content, ContentType.USER_MESSAGE)

        assert sanitized.sanitization_applied
        assert 'credit_card' in sanitized.detected_patterns
        assert '[REDACTED:CREDIT_CARD]' in sanitized.sanitized_content
        assert sanitized.sensitivity_level == SensitivityLevel.RESTRICTED

    def test_password_detection_and_redaction(self):
        """Test password detection and redaction functionality."""
        content = 'My password is: secretpassword123'

        sanitized = self.sanitizer.sanitize_content(content, ContentType.USER_MESSAGE)

        assert sanitized.sanitization_applied
        assert 'password' in sanitized.detected_patterns
        assert '[REDACTED:PASSWORD]' in sanitized.sanitized_content
        assert sanitized.sensitivity_level == SensitivityLevel.RESTRICTED

    def test_api_key_detection_and_hashing(self):
        """Test API key detection and hashing functionality."""
        content = 'Use this API key: sk_test_1234567890abcdef1234567890abcdef'

        # Set to INTERNAL sensitivity to trigger hashing instead of redaction
        sanitized = self.sanitizer.sanitize_content(content, ContentType.USER_MESSAGE, SensitivityLevel.INTERNAL)

        assert sanitized.sanitization_applied
        assert 'api_key' in sanitized.detected_patterns
        assert '[HASH:' in sanitized.sanitized_content
        assert sanitized.sensitivity_level == SensitivityLevel.INTERNAL

    def test_sensitive_keywords_detection(self):
        """Test sensitive keyword detection functionality."""
        content = 'This is confidential financial information about the account balance.'

        sanitized = self.sanitizer.sanitize_content(content, ContentType.USER_MESSAGE)

        assert 'keyword_financial' in sanitized.detected_patterns
        assert 'keyword_business' in sanitized.detected_patterns
        assert sanitized.sensitivity_level == SensitivityLevel.INTERNAL

    def test_custom_pattern_addition(self):
        """Test adding custom PII patterns."""
        self.sanitizer.add_custom_pattern('employee_id', r'EMP\d{6}')

        content = 'Employee ID EMP123456 needs access.'
        sanitized = self.sanitizer.sanitize_content(content, ContentType.USER_MESSAGE)

        assert 'custom_employee_id' in sanitized.detected_patterns

    def test_dictionary_sanitization(self):
        """Test sanitization of dictionary parameters."""
        parameters = {
            'username': '<EMAIL>',
            'password': 'secretpassword',
            'phone': '************',
            'description': 'Normal text content',
        }

        sanitized_params, sanitization_log = self.sanitizer.sanitize_dict(parameters, ContentType.ACTION_PARAMETERS)

        assert len(sanitization_log) >= 2  # At least username and password should be sanitized
        assert 'key:password' in sanitization_log
        assert sanitized_params['password'] != parameters['password']
        assert sanitized_params['description'] == parameters['description']  # Normal text unchanged

    def test_empty_content_handling(self):
        """Test handling of empty or None content."""
        sanitized = self.sanitizer.sanitize_content('', ContentType.USER_MESSAGE)

        assert not sanitized.sanitization_applied
        assert sanitized.sensitivity_level == SensitivityLevel.PUBLIC
        assert sanitized.sanitized_content == ''

    def test_sensitivity_level_override(self):
        """Test sensitivity level override functionality."""
        content = 'This is normal text.'

        sanitized = self.sanitizer.sanitize_content(content, ContentType.USER_MESSAGE, SensitivityLevel.RESTRICTED)

        assert sanitized.sensitivity_level == SensitivityLevel.RESTRICTED

    def test_multiple_patterns_in_content(self):
        """Test content with multiple PII patterns."""
        content = 'Contact <EMAIL> or call (*************. Password: secret123'

        sanitized = self.sanitizer.sanitize_content(content, ContentType.USER_MESSAGE)

        assert sanitized.sanitization_applied
        assert len(sanitized.detected_patterns) >= 2
        assert 'email' in sanitized.detected_patterns
        assert 'phone' in sanitized.detected_patterns or 'password' in sanitized.detected_patterns


class TestEnhancedAuditService:
    """Test cases for the EnhancedAuditService class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.privacy_config = PrivacyConfig()
        self.privacy_config.enable_sanitization = True

        # Mock the database connection
        with patch('src.services.enhanced_audit_service.DatabaseConnectionManager'):
            self.enhanced_audit_service = EnhancedAuditService(self.privacy_config)
            self.enhanced_audit_service.db_manager = MagicMock()

    @pytest.mark.asyncio
    async def test_log_user_message_enhanced(self):
        """Test enhanced user message logging with sanitization."""
        # Mock the parent log_event method
        with patch.object(self.enhanced_audit_service, 'log_event', new_callable=AsyncMock) as mock_log_event:
            mock_log_event.return_value = 'test_event_id'

            message_content = 'My <NAME_EMAIL>'

            event_id = await self.enhanced_audit_service.log_user_message_enhanced(
                user_id='test_user',
                user_name='Test User',
                message_content=message_content,
                sensitivity_level=SensitivityLevel.INTERNAL,
            )

            # Verify the parent method was called
            mock_log_event.assert_called_once()
            call_args = mock_log_event.call_args[1]

            # Check that sanitization was applied
            assert call_args['message_content'] != message_content  # Should be sanitized
            assert 'j***@e******.com' in call_args['message_content']
            assert call_args['event_type'] == AuditEventType.USER_MESSAGE

    @pytest.mark.asyncio
    async def test_log_action_execution_enhanced(self):
        """Test enhanced action execution logging with parameter sanitization."""
        with patch.object(self.enhanced_audit_service, 'log_event', new_callable=AsyncMock) as mock_log_event:
            mock_log_event.return_value = 'test_event_id'

            parameters = {
                'username': '<EMAIL>',
                'password': 'secretpassword',
                'description': 'Create new ticket',
            }

            event_id = await self.enhanced_audit_service.log_action_execution_enhanced(
                action_name='create_ticket', user_id='test_user', user_name='Test User', parameters=parameters
            )

            # Verify the parent method was called
            mock_log_event.assert_called_once()
            call_args = mock_log_event.call_args[1]

            # Check that parameter sanitization was applied
            assert call_args['parameters'] != parameters  # Should be sanitized
            assert call_args['event_type'] == AuditEventType.ACTION_EXECUTION

    @pytest.mark.asyncio
    async def test_performance_monitoring(self):
        """Test performance monitoring functionality."""
        # Test that performance metrics are tracked
        initial_metrics = self.enhanced_audit_service.get_performance_metrics()

        with patch.object(self.enhanced_audit_service, 'log_event', new_callable=AsyncMock) as mock_log_event:
            mock_log_event.return_value = 'test_event_id'

            await self.enhanced_audit_service.log_user_message_enhanced(
                user_id='test_user', user_name='Test User', message_content='Test message'
            )

            updated_metrics = self.enhanced_audit_service.get_performance_metrics()

            # Verify metrics were updated
            assert updated_metrics['total_operations'] > initial_metrics['total_operations']

    @pytest.mark.asyncio
    async def test_cleanup_expired_data(self):
        """Test data cleanup functionality."""
        # Mock the database session
        mock_session = MagicMock()
        mock_query = MagicMock()
        mock_query.filter.return_value.delete.return_value = 5  # Mock 5 deleted records
        mock_session.query.return_value = mock_query

        with patch.object(self.enhanced_audit_service.db_manager, 'session_scope') as mock_session_scope:
            mock_session_scope.return_value.__enter__.return_value = mock_session
            mock_session_scope.return_value.__exit__.return_value = None

            # Mock the Sydney timezone method
            with patch.object(self.enhanced_audit_service, '_get_sydney_now') as mock_get_sydney_now:
                mock_get_sydney_now.return_value = datetime.now()

                cleanup_stats = await self.enhanced_audit_service.cleanup_expired_data()

                assert 'total_deleted' in cleanup_stats
                assert cleanup_stats['errors'] == 0

    @pytest.mark.asyncio
    async def test_export_user_data(self):
        """Test GDPR user data export functionality."""
        # Mock the database session and query results
        mock_session = MagicMock()
        mock_audit_logs = [MagicMock(event_id='log1', event_type='user_message', created_at=datetime.now())]
        mock_user_sessions = [MagicMock(session_id='session1', message_count=5)]

        mock_session.query.return_value.filter.return_value.all.side_effect = [mock_audit_logs, mock_user_sessions]

        with patch.object(self.enhanced_audit_service.db_manager, 'session_scope') as mock_session_scope:
            mock_session_scope.return_value.__enter__.return_value = mock_session
            mock_session_scope.return_value.__exit__.return_value = None

            with patch.object(self.enhanced_audit_service, '_get_sydney_now') as mock_get_sydney_now:
                mock_get_sydney_now.return_value = datetime.now()

                export_data = await self.enhanced_audit_service.export_user_data('test_user')

                assert export_data['user_id'] == 'test_user'
                assert 'audit_logs' in export_data
                assert 'user_sessions' in export_data
                assert export_data['total_audit_logs'] == 1
                assert export_data['total_sessions'] == 1

    def test_sanitization_metadata_storage(self):
        """Test sanitization metadata storage and retrieval."""
        event_id = 'test_event_id'
        metadata = {'test': 'metadata'}

        self.enhanced_audit_service.sanitization_metadata[event_id] = metadata

        retrieved_metadata = self.enhanced_audit_service.get_sanitization_metadata(event_id)
        assert retrieved_metadata == metadata


class TestPrivacyConfig:
    """Test cases for the PrivacyConfig class."""

    def test_default_configuration(self):
        """Test default privacy configuration values."""
        config = PrivacyConfig()

        assert config.enable_sanitization is True
        assert config.default_sensitivity_level == SensitivityLevel.INTERNAL
        assert config.sanitize_user_messages is True
        assert config.enable_gdpr_compliance is True

    def test_retention_policies(self):
        """Test retention policy configuration."""
        config = PrivacyConfig()

        # Test that retention policies are properly configured
        user_message_public = config.retention_policies.get((AuditEventType.USER_MESSAGE, SensitivityLevel.PUBLIC))
        assert user_message_public == RetentionPolicy.LONG_TERM

        user_message_restricted = config.retention_policies.get((
            AuditEventType.USER_MESSAGE,
            SensitivityLevel.RESTRICTED,
        ))
        assert user_message_restricted == RetentionPolicy.IMMEDIATE

    def test_retention_periods(self):
        """Test retention period configuration."""
        config = PrivacyConfig()

        assert config.retention_periods[RetentionPolicy.SHORT_TERM] == 30
        assert config.retention_periods[RetentionPolicy.MEDIUM_TERM] == 90
        assert config.retention_periods[RetentionPolicy.LONG_TERM] == 365
        assert config.retention_periods[RetentionPolicy.PERMANENT] == -1


class TestDataClassifier:
    """Test cases for the DataClassifier class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.classifier = DataClassifier()

    def test_content_classification_caching(self):
        """Test content classification with caching."""
        content = 'This is test content.'

        # First classification
        sensitivity1 = self.classifier.classify_content(content, ContentType.USER_MESSAGE)

        # Second classification (should use cache)
        sensitivity2 = self.classifier.classify_content(content, ContentType.USER_MESSAGE)

        assert sensitivity1 == sensitivity2
        assert len(self.classifier.classification_cache) > 0

    def test_context_based_classification(self):
        """Test context-based sensitivity adjustment."""
        content = 'Normal message content.'

        # Classification without context
        sensitivity_normal = self.classifier.classify_content(content, ContentType.USER_MESSAGE)

        # Classification with admin context
        admin_context = {'user_role': 'admin'}
        sensitivity_admin = self.classifier.classify_content(content, ContentType.USER_MESSAGE, admin_context)

        # Admin context should increase sensitivity
        assert sensitivity_admin.value != sensitivity_normal.value or sensitivity_admin == SensitivityLevel.INTERNAL


class TestPerformanceMonitor:
    """Test cases for the PerformanceMonitor class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.privacy_config = PrivacyConfig()
        self.monitor = PerformanceMonitor(self.privacy_config)

    def test_operation_tracking(self):
        """Test operation performance tracking."""
        initial_metrics = self.monitor.get_metrics()

        # Track a successful operation
        self.monitor.track_operation('test_operation', 100.0, True, {'test': 'metadata'})

        updated_metrics = self.monitor.get_metrics()

        assert updated_metrics['total_operations'] == initial_metrics['total_operations'] + 1
        assert updated_metrics['total_duration_ms'] == initial_metrics['total_duration_ms'] + 100.0

    def test_error_tracking(self):
        """Test error tracking functionality."""
        initial_metrics = self.monitor.get_metrics()

        # Track a failed operation
        self.monitor.track_operation('test_operation', 50.0, False, {'error': 'test error'})

        updated_metrics = self.monitor.get_metrics()

        assert updated_metrics['errors'] == initial_metrics['errors'] + 1

    def test_performance_alert_generation(self):
        """Test performance alert generation for slow operations."""
        # Set a low threshold for testing
        self.privacy_config.performance_alert_threshold_ms = 10

        with patch('src.services.enhanced_audit_service.logger') as mock_logger:
            # Track a slow operation
            self.monitor.track_operation('slow_operation', 50.0, True)

            # Verify alert was generated
            mock_logger.warning.assert_called_once()
            assert 'PERFORMANCE ALERT' in mock_logger.warning.call_args[0][0]

    def test_operation_history_maintenance(self):
        """Test operation history size maintenance."""
        # Track more operations than the max history size
        for i in range(1100):  # More than max_history_size (1000)
            self.monitor.track_operation(f'operation_{i}', 10.0, True)

        # Verify history size is maintained
        assert len(self.monitor.operation_history) == self.monitor.max_history_size


class TestEnhancedAuditDecorators:
    """Test cases for enhanced audit decorators."""

    def setup_method(self):
        """Set up test fixtures."""
        self.mock_context = MagicMock()
        self.mock_context.activity = MagicMock()
        self.mock_context.activity.from_property = MagicMock()
        self.mock_context.activity.from_property.id = 'test_user_id'
        self.mock_context.activity.from_property.name = 'Test User'
        self.mock_context.activity.id = 'test_activity_id'
        self.mock_context.activity.channel_id = 'test_channel'
        self.mock_context.data = {'param1': 'value1', 'param2': 'value2'}

    @pytest.mark.asyncio
    async def test_enhanced_audit_action_decorator(self):
        """Test the enhanced audit action decorator."""

        @enhanced_audit_action('test_action')
        async def test_action(context):
            return 'action_result'

        with patch('src.utils.enhanced_audit_decorators.get_enhanced_audit_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service

            with patch('src.utils.enhanced_audit_decorators.SessionService') as mock_session_service:
                mock_session_instance = AsyncMock()
                mock_session_service.return_value = mock_session_instance
                mock_session_instance.get_or_create_session.return_value = 'test_session_id'

                result = await test_action(self.mock_context)

                assert result == 'action_result'
                mock_service.log_action_execution_enhanced.assert_called_once()

    @pytest.mark.asyncio
    async def test_enhanced_audit_message_handler_decorator(self):
        """Test the enhanced audit message handler decorator."""

        @enhanced_audit_message_handler('test_handler')
        async def test_handler(context, state=None):
            return 'handler_result'

        # Add text to the activity for message content
        self.mock_context.activity.text = 'Test message content'

        with patch('src.utils.enhanced_audit_decorators.get_enhanced_audit_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service

            with patch('src.utils.enhanced_audit_decorators.SessionService') as mock_session_service:
                mock_session_instance = AsyncMock()
                mock_session_service.return_value = mock_session_instance
                mock_session_instance.get_or_create_session.return_value = 'test_session_id'

                result = await test_handler(self.mock_context)

                assert result == 'handler_result'
                mock_service.log_user_message_enhanced.assert_called_once()


class TestEnhancedAuditTurnContext:
    """Test cases for the EnhancedAuditTurnContext wrapper."""

    def setup_method(self):
        """Set up test fixtures."""
        self.mock_original_context = MagicMock()
        self.mock_original_context.activity = MagicMock()
        self.mock_original_context.activity.from_property = MagicMock()
        self.mock_original_context.activity.from_property.id = 'test_user_id'
        self.mock_original_context.activity.from_property.name = 'Test User'

        self.enhanced_context = EnhancedAuditTurnContext(self.mock_original_context, session_id='test_session_id')

    @pytest.mark.asyncio
    async def test_send_activity_text_logging(self):
        """Test send_activity with text content logging."""
        # Mock the original send_activity method
        self.mock_original_context.send_activity = AsyncMock(return_value='response')

        with patch('src.utils.enhanced_audit_decorators.get_enhanced_audit_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service

            result = await self.enhanced_context.send_activity('Test response message')

            assert result == 'response'
            self.mock_original_context.send_activity.assert_called_once_with('Test response message')
            mock_service.log_bot_response_enhanced.assert_called_once()

    @pytest.mark.asyncio
    async def test_send_activity_with_attachments(self):
        """Test send_activity with attachment content logging."""
        # Mock activity with attachments
        mock_activity = MagicMock()
        mock_activity.text = None
        mock_activity.attachments = [MagicMock(content_type='application/vnd.microsoft.card.adaptive')]

        self.mock_original_context.send_activity = AsyncMock(return_value='response')

        with patch('src.utils.enhanced_audit_decorators.get_enhanced_audit_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service

            result = await self.enhanced_context.send_activity(mock_activity)

            assert result == 'response'
            mock_service.log_bot_response_enhanced.assert_called_once()

            # Check that attachment info was logged
            call_args = mock_service.log_bot_response_enhanced.call_args[1]
            assert 'attachment' in call_args['response_content'].lower()

    def test_attribute_delegation(self):
        """Test that attributes are properly delegated to original context."""
        self.mock_original_context.some_attribute = 'test_value'

        assert self.enhanced_context.some_attribute == 'test_value'


class TestIntegration:
    """Integration tests for the enhanced audit system."""

    @pytest.mark.asyncio
    async def test_end_to_end_user_message_flow(self):
        """Test complete user message flow with sanitization and audit logging."""
        # This test would require more complex setup with actual database
        # For now, we'll test the flow with mocked components

        privacy_config = PrivacyConfig()
        privacy_config.enable_sanitization = True

        with patch('src.services.enhanced_audit_service.DatabaseConnectionManager'):
            enhanced_service = EnhancedAuditService(privacy_config)
            enhanced_service.db_manager = MagicMock()

            with patch.object(enhanced_service, 'log_event', new_callable=AsyncMock) as mock_log_event:
                mock_log_event.return_value = 'test_event_id'

                # Test message with PII
                message_content = 'My <NAME_EMAIL> and phone is (*************'

                event_id = await enhanced_service.log_user_message_enhanced(
                    user_id='test_user', user_name='Test User', message_content=message_content
                )

                # Verify sanitization and logging occurred
                mock_log_event.assert_called_once()
                call_args = mock_log_event.call_args[1]

                # Message should be sanitized
                assert call_args['message_content'] != message_content
                assert 'j***@e******.com' in call_args['message_content']
                assert '(***) ***-4567' in call_args['message_content']


if __name__ == '__main__':
    pytest.main([__file__])
