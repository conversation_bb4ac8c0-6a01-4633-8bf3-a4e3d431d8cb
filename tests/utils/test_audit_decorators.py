"""
Tests for the audit decorators.
"""

from unittest.mock import As<PERSON><PERSON><PERSON>, Magic<PERSON>ock, patch

import pytest

from src.utils.audit_decorators import (
    audit_action,
    audit_context,
    audit_message_handler,
    extract_user_info,
)
from src.utils.sql.models import AuditStatus


class TestAuditDecorators:
    """Test cases for audit decorators."""

    def test_extract_user_info_success(self):
        """Test successful user info extraction."""
        # Mock context with activity and user info
        context = MagicMock()
        context.activity = MagicMock()
        context.activity.from_property = MagicMock()
        context.activity.from_property.id = 'test_user_id'
        context.activity.from_property.name = 'Test User'
        context.activity.id = 'activity_123'
        context.activity.channel_id = 'channel_456'

        user_id, user_name, activity_id, channel_id = extract_user_info(context)

        assert user_id == 'test_user_id'
        assert user_name == 'Test User'
        assert activity_id == 'activity_123'
        assert channel_id == 'channel_456'

    def test_extract_user_info_no_activity(self):
        """Test user info extraction when no activity is present."""
        context = MagicMock()
        context.activity = None

        user_id, user_name, activity_id, channel_id = extract_user_info(context)

        assert user_id is None
        assert user_name is None
        assert activity_id is None
        assert channel_id is None

    def test_extract_user_info_no_user(self):
        """Test user info extraction when no user info is present."""
        context = MagicMock()
        context.activity = MagicMock()
        context.activity.from_property = None
        context.activity.id = 'activity_123'

        user_id, user_name, activity_id, channel_id = extract_user_info(context)

        assert user_id is None
        assert user_name is None
        assert activity_id == 'activity_123'

    @pytest.mark.asyncio
    async def test_audit_action_decorator_success(self):
        """Test audit_action decorator with successful function execution."""
        # Mock audit services
        with (
            patch('utils.audit_decorators.AuditService') as mock_audit_service,
            patch('utils.audit_decorators.SessionService') as mock_session_service,
        ):
            mock_audit_instance = AsyncMock()
            mock_session_instance = AsyncMock()
            mock_audit_service.return_value = mock_audit_instance
            mock_session_service.return_value = mock_session_instance

            # Mock session creation
            mock_session_instance.get_or_create_session.return_value = 'session_123'

            # Create a test function
            @audit_action('test_action')
            async def test_function(context):
                return 'success_result'

            # Mock context
            context = MagicMock()
            context.activity = MagicMock()
            context.activity.from_property = MagicMock()
            context.activity.from_property.id = 'test_user'
            context.activity.from_property.name = 'Test User'
            context.activity.id = 'activity_123'
            context.data = {'param1': 'value1'}

            # Execute the decorated function
            result = await test_function(context)

            assert result == 'success_result'

            # Verify audit logging was called
            mock_audit_instance.log_action_execution.assert_called_once()
            call_args = mock_audit_instance.log_action_execution.call_args
            assert call_args[1]['action_name'] == 'test_action'
            assert call_args[1]['user_id'] == 'test_user'
            assert call_args[1]['user_name'] == 'Test User'
            assert call_args[1]['event_status'] == AuditStatus.SUCCESS
            assert call_args[1]['session_id'] == 'session_123'

            # Verify session metrics update
            mock_session_instance.update_session_metrics.assert_called_once_with(
                session_id='session_123',
                action_increment=1,
            )

    @pytest.mark.asyncio
    async def test_audit_action_decorator_failure(self):
        """Test audit_action decorator with function execution failure."""
        # Mock audit services
        with (
            patch('utils.audit_decorators.AuditService') as mock_audit_service,
            patch('utils.audit_decorators.SessionService') as mock_session_service,
        ):
            mock_audit_instance = AsyncMock()
            mock_session_instance = AsyncMock()
            mock_audit_service.return_value = mock_audit_instance
            mock_session_service.return_value = mock_session_instance

            # Mock session creation
            mock_session_instance.get_or_create_session.return_value = 'session_123'

            # Create a test function that raises an exception
            @audit_action('test_action')
            async def test_function(context):
                raise ValueError('Test error')

            # Mock context
            context = MagicMock()
            context.activity = MagicMock()
            context.activity.from_property = MagicMock()
            context.activity.from_property.id = 'test_user'
            context.activity.from_property.name = 'Test User'
            context.data = {'param1': 'value1'}

            # Execute the decorated function and expect exception
            with pytest.raises(ValueError, match='Test error'):
                await test_function(context)

            # Verify audit logging was called with failure status
            mock_audit_instance.log_action_execution.assert_called_once()
            call_args = mock_audit_instance.log_action_execution.call_args
            assert call_args[1]['action_name'] == 'test_action'
            assert call_args[1]['event_status'] == AuditStatus.FAILURE
            assert call_args[1]['error_message'] == 'Test error'
            assert call_args[1]['stack_trace'] is not None

            # Verify session metrics update includes error
            mock_session_instance.update_session_metrics.assert_called_once_with(
                session_id='session_123',
                action_increment=1,
                error_increment=1,
            )

    @pytest.mark.asyncio
    async def test_audit_message_handler_decorator_success(self):
        """Test audit_message_handler decorator with successful execution."""
        # Mock audit services
        with (
            patch('utils.audit_decorators.AuditService') as mock_audit_service,
            patch('utils.audit_decorators.SessionService') as mock_session_service,
        ):
            mock_audit_instance = AsyncMock()
            mock_session_instance = AsyncMock()
            mock_audit_service.return_value = mock_audit_instance
            mock_session_service.return_value = mock_session_instance

            # Mock session creation
            mock_session_instance.get_or_create_session.return_value = 'session_123'

            # Create a test message handler
            @audit_message_handler('test_handler')
            async def test_handler(context, state=None):
                return True

            # Mock context
            context = MagicMock()
            context.activity = MagicMock()
            context.activity.from_property = MagicMock()
            context.activity.from_property.id = 'test_user'
            context.activity.from_property.name = 'Test User'
            context.activity.text = 'Hello, bot!'
            context.activity.id = 'activity_123'

            # Execute the decorated function
            result = await test_handler(context, state=None)

            assert result is True

            # Verify user message logging was called
            mock_audit_instance.log_user_message.assert_called_once()
            call_args = mock_audit_instance.log_user_message.call_args
            assert call_args[1]['user_id'] == 'test_user'
            assert call_args[1]['user_name'] == 'Test User'
            assert call_args[1]['message_content'] == 'Hello, bot!'
            assert call_args[1]['session_id'] == 'session_123'

            # Verify session metrics update
            mock_session_instance.update_session_metrics.assert_called_once_with(
                session_id='session_123',
                message_increment=1,
            )

    @pytest.mark.asyncio
    async def test_audit_message_handler_decorator_failure(self):
        """Test audit_message_handler decorator with execution failure."""
        # Mock audit services
        with (
            patch('utils.audit_decorators.AuditService') as mock_audit_service,
            patch('utils.audit_decorators.SessionService') as mock_session_service,
        ):
            mock_audit_instance = AsyncMock()
            mock_session_instance = AsyncMock()
            mock_audit_service.return_value = mock_audit_instance
            mock_session_service.return_value = mock_session_instance

            # Mock session creation
            mock_session_instance.get_or_create_session.return_value = 'session_123'

            # Create a test message handler that raises an exception
            @audit_message_handler('test_handler')
            async def test_handler(context, state=None):
                raise RuntimeError('Handler error')

            # Mock context
            context = MagicMock()
            context.activity = MagicMock()
            context.activity.from_property = MagicMock()
            context.activity.from_property.id = 'test_user'
            context.activity.from_property.name = 'Test User'
            context.activity.text = 'Hello, bot!'

            # Execute the decorated function and expect exception
            with pytest.raises(RuntimeError, match='Handler error'):
                await test_handler(context, state=None)

            # Verify error logging was called
            mock_audit_instance.log_error_event.assert_called_once()
            call_args = mock_audit_instance.log_error_event.call_args
            assert call_args[1]['error_message'] == 'Handler error'
            assert call_args[1]['user_id'] == 'test_user'
            assert call_args[1]['action_name'] == 'test_handler'

            # Verify session metrics update includes error
            mock_session_instance.update_session_metrics.assert_called_once_with(
                session_id='session_123',
                message_increment=1,
                error_increment=1,
            )

    @pytest.mark.asyncio
    async def test_audit_context_initialization(self):
        """Test audit context initialization."""
        # Reset audit context
        audit_context.audit_service = None
        audit_context.session_service = None

        # Initialize services
        audit_context.initialize_services()

        # Verify services are initialized
        assert audit_context.audit_service is not None
        assert audit_context.session_service is not None

        # Verify they are the correct types
        from services.audit_service import AuditService, SessionService

        assert isinstance(audit_context.audit_service, AuditService)
        assert isinstance(audit_context.session_service, SessionService)

    @pytest.mark.asyncio
    async def test_audit_action_no_user_info(self):
        """Test audit_action decorator when no user info is available."""
        # Mock audit services
        with (
            patch('utils.audit_decorators.AuditService') as mock_audit_service,
            patch('utils.audit_decorators.SessionService') as mock_session_service,
        ):
            mock_audit_instance = AsyncMock()
            mock_session_instance = AsyncMock()
            mock_audit_service.return_value = mock_audit_instance
            mock_session_service.return_value = mock_session_instance

            # Create a test function
            @audit_action('test_action')
            async def test_function(context):
                return 'success_result'

            # Mock context with no user info
            context = MagicMock()
            context.activity = None
            context.data = {}

            # Execute the decorated function
            result = await test_function(context)

            assert result == 'success_result'

            # Verify audit logging was still called but with None user info
            mock_audit_instance.log_action_execution.assert_called_once()
            call_args = mock_audit_instance.log_action_execution.call_args
            assert call_args[1]['user_id'] is None
            assert call_args[1]['user_name'] is None
            assert call_args[1]['session_id'] is None  # No session created without user_id
