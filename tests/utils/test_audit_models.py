"""
Tests for the audit database models.
"""

import datetime
import uuid
from unittest.mock import MagicMock, patch

import pytest
import pytz
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.utils.sql.models import (
    AuditEventType,
    AuditLog,
    AuditStatus,
    Base,
    UserSession,
    create_tables,
    get_sydney_now,
)


class TestAuditModels:
    """Test cases for audit database models."""

    @pytest.fixture
    def in_memory_db(self):
        """Create an in-memory SQLite database for testing."""
        engine = create_engine('sqlite:///:memory:', echo=False)
        Base.metadata.create_all(engine)
        Session = sessionmaker(bind=engine)
        session = Session()
        yield session
        session.close()

    def test_get_sydney_now(self):
        """Test the get_sydney_now function."""
        now = get_sydney_now()

        # Verify it returns a datetime object
        assert isinstance(now, datetime.datetime)

        # Verify it has timezone info
        assert now.tzinfo is not None

        # Verify it's in Sydney timezone
        sydney_tz = pytz.timezone('Australia/Sydney')
        assert now.tzinfo.zone == sydney_tz.zone

    def test_audit_event_type_enum(self):
        """Test AuditEventType enum values."""
        assert AuditEventType.USER_MESSAGE.value == 'user_message'
        assert AuditEventType.BOT_RESPONSE.value == 'bot_response'
        assert AuditEventType.ACTION_EXECUTION.value == 'action_execution'
        assert AuditEventType.AUTHENTICATION.value == 'authentication'
        assert AuditEventType.ERROR.value == 'error'
        assert AuditEventType.SYSTEM_EVENT.value == 'system_event'
        assert AuditEventType.DATABASE_OPERATION.value == 'database_operation'
        assert AuditEventType.API_CALL.value == 'api_call'

    def test_audit_status_enum(self):
        """Test AuditStatus enum values."""
        assert AuditStatus.SUCCESS.value == 'success'
        assert AuditStatus.FAILURE.value == 'failure'
        assert AuditStatus.WARNING.value == 'warning'
        assert AuditStatus.IN_PROGRESS.value == 'in_progress'

    def test_audit_log_model_creation(self, in_memory_db):
        """Test creating an AuditLog model instance."""
        event_id = str(uuid.uuid4())
        now = get_sydney_now()

        audit_log = AuditLog(
            event_id=event_id,
            event_type=AuditEventType.USER_MESSAGE.value,
            event_status=AuditStatus.SUCCESS.value,
            user_id='test_user',
            user_name='Test User',
            session_id='session_123',
            request_id='req_456',
            activity_id='activity_789',
            channel_id='channel_abc',
            action_name='test_action',
            endpoint='/api/test',
            method='POST',
            message_content='Hello, bot!',
            parameters='{"param1": "value1"}',
            response_data='{"result": "success"}',
            error_message=None,
            stack_trace=None,
            duration_ms=150.5,
            created_at=now,
        )

        # Add to database
        in_memory_db.add(audit_log)
        in_memory_db.commit()

        # Verify the record was created
        retrieved = in_memory_db.query(AuditLog).filter(AuditLog.event_id == event_id).first()
        assert retrieved is not None
        assert retrieved.event_id == event_id
        assert retrieved.event_type == AuditEventType.USER_MESSAGE.value
        assert retrieved.event_status == AuditStatus.SUCCESS.value
        assert retrieved.user_id == 'test_user'
        assert retrieved.user_name == 'Test User'
        assert retrieved.session_id == 'session_123'
        assert retrieved.request_id == 'req_456'
        assert retrieved.activity_id == 'activity_789'
        assert retrieved.channel_id == 'channel_abc'
        assert retrieved.action_name == 'test_action'
        assert retrieved.endpoint == '/api/test'
        assert retrieved.method == 'POST'
        assert retrieved.message_content == 'Hello, bot!'
        assert retrieved.parameters == '{"param1": "value1"}'
        assert retrieved.response_data == '{"result": "success"}'
        assert retrieved.duration_ms == 150.5
        assert retrieved.created_at == now

    def test_audit_log_repr(self):
        """Test AuditLog string representation."""
        audit_log = AuditLog(
            id=1,
            event_type=AuditEventType.USER_MESSAGE.value,
            user_id='test_user',
            created_at=get_sydney_now(),
        )

        repr_str = repr(audit_log)
        assert 'AuditLog' in repr_str
        assert 'id=1' in repr_str
        assert "event_type='user_message'" in repr_str
        assert "user_id='test_user'" in repr_str

    def test_user_session_model_creation(self, in_memory_db):
        """Test creating a UserSession model instance."""
        session_id = str(uuid.uuid4())
        now = get_sydney_now()

        user_session = UserSession(
            session_id=session_id,
            user_id='test_user',
            user_name='Test User',
            channel_id='channel_123',
            conversation_id='conv_456',
            message_count=5,
            action_count=3,
            error_count=1,
            first_activity_at=now,
            last_activity_at=now,
            session_duration_minutes=15.5,
            created_at=now,
        )

        # Add to database
        in_memory_db.add(user_session)
        in_memory_db.commit()

        # Verify the record was created
        retrieved = in_memory_db.query(UserSession).filter(UserSession.session_id == session_id).first()
        assert retrieved is not None
        assert retrieved.session_id == session_id
        assert retrieved.user_id == 'test_user'
        assert retrieved.user_name == 'Test User'
        assert retrieved.channel_id == 'channel_123'
        assert retrieved.conversation_id == 'conv_456'
        assert retrieved.message_count == 5
        assert retrieved.action_count == 3
        assert retrieved.error_count == 1
        assert retrieved.first_activity_at == now
        assert retrieved.last_activity_at == now
        assert retrieved.session_duration_minutes == 15.5
        assert retrieved.created_at == now

    def test_user_session_repr(self):
        """Test UserSession string representation."""
        user_session = UserSession(
            id=1,
            session_id='session_123',
            user_id='test_user',
            message_count=10,
        )

        repr_str = repr(user_session)
        assert 'UserSession' in repr_str
        assert 'id=1' in repr_str
        assert "session_id='session_123'" in repr_str
        assert "user_id='test_user'" in repr_str
        assert 'message_count=10' in repr_str

    def test_audit_log_minimal_fields(self, in_memory_db):
        """Test creating AuditLog with only required fields."""
        event_id = str(uuid.uuid4())

        audit_log = AuditLog(
            event_id=event_id,
            event_type=AuditEventType.SYSTEM_EVENT.value,
            event_status=AuditStatus.SUCCESS.value,
            created_at=get_sydney_now(),
        )

        # Add to database
        in_memory_db.add(audit_log)
        in_memory_db.commit()

        # Verify the record was created
        retrieved = in_memory_db.query(AuditLog).filter(AuditLog.event_id == event_id).first()
        assert retrieved is not None
        assert retrieved.event_id == event_id
        assert retrieved.event_type == AuditEventType.SYSTEM_EVENT.value
        assert retrieved.event_status == AuditStatus.SUCCESS.value

        # Verify optional fields are None
        assert retrieved.user_id is None
        assert retrieved.user_name is None
        assert retrieved.session_id is None
        assert retrieved.message_content is None
        assert retrieved.parameters is None
        assert retrieved.response_data is None
        assert retrieved.error_message is None
        assert retrieved.stack_trace is None
        assert retrieved.duration_ms is None

    def test_user_session_minimal_fields(self, in_memory_db):
        """Test creating UserSession with only required fields."""
        session_id = str(uuid.uuid4())

        user_session = UserSession(
            session_id=session_id,
            user_id='test_user',
            message_count=0,
            action_count=0,
            error_count=0,
            first_activity_at=get_sydney_now(),
            last_activity_at=get_sydney_now(),
            created_at=get_sydney_now(),
        )

        # Add to database
        in_memory_db.add(user_session)
        in_memory_db.commit()

        # Verify the record was created
        retrieved = in_memory_db.query(UserSession).filter(UserSession.session_id == session_id).first()
        assert retrieved is not None
        assert retrieved.session_id == session_id
        assert retrieved.user_id == 'test_user'
        assert retrieved.message_count == 0
        assert retrieved.action_count == 0
        assert retrieved.error_count == 0

        # Verify optional fields are None or have defaults
        assert retrieved.user_name is None
        assert retrieved.channel_id is None
        assert retrieved.conversation_id is None
        assert retrieved.session_duration_minutes is None

    def test_create_tables_success(self):
        """Test successful table creation."""
        mock_engine = MagicMock()

        with patch('utils.sql.models.logger') as mock_logger:
            create_tables(mock_engine)

            # Verify Base.metadata.create_all was called
            # Note: We can't directly test this without mocking Base.metadata
            # but we can verify the logger calls
            mock_logger.info.assert_any_call("Creating database tables if they don't exist")
            mock_logger.info.assert_any_call('Database tables created or already exist')

    def test_create_tables_failure(self):
        """Test table creation failure handling."""
        mock_engine = MagicMock()

        with patch('utils.sql.models.Base') as mock_base, patch('utils.sql.models.logger') as mock_logger:
            # Make metadata.create_all raise an exception
            mock_base.metadata.create_all.side_effect = Exception('Database error')

            # Verify exception is raised
            with pytest.raises(Exception, match='Database error'):
                create_tables(mock_engine)

            # Verify error logging
            mock_logger.error.assert_called_once()
            mock_logger.debug.assert_called_once()

    def test_audit_log_unique_event_id(self, in_memory_db):
        """Test that event_id must be unique in AuditLog."""
        event_id = str(uuid.uuid4())

        # Create first audit log
        audit_log1 = AuditLog(
            event_id=event_id,
            event_type=AuditEventType.USER_MESSAGE.value,
            event_status=AuditStatus.SUCCESS.value,
            created_at=get_sydney_now(),
        )
        in_memory_db.add(audit_log1)
        in_memory_db.commit()

        # Try to create second audit log with same event_id
        audit_log2 = AuditLog(
            event_id=event_id,  # Same event_id
            event_type=AuditEventType.BOT_RESPONSE.value,
            event_status=AuditStatus.SUCCESS.value,
            created_at=get_sydney_now(),
        )
        in_memory_db.add(audit_log2)

        # This should raise an integrity error due to unique constraint
        with pytest.raises(Exception):  # SQLAlchemy will raise an IntegrityError
            in_memory_db.commit()

    def test_user_session_unique_session_id(self, in_memory_db):
        """Test that session_id must be unique in UserSession."""
        session_id = str(uuid.uuid4())
        now = get_sydney_now()

        # Create first user session
        session1 = UserSession(
            session_id=session_id,
            user_id='user1',
            message_count=0,
            action_count=0,
            error_count=0,
            first_activity_at=now,
            last_activity_at=now,
            created_at=now,
        )
        in_memory_db.add(session1)
        in_memory_db.commit()

        # Try to create second session with same session_id
        session2 = UserSession(
            session_id=session_id,  # Same session_id
            user_id='user2',
            message_count=0,
            action_count=0,
            error_count=0,
            first_activity_at=now,
            last_activity_at=now,
            created_at=now,
        )
        in_memory_db.add(session2)

        # This should raise an integrity error due to unique constraint
        with pytest.raises(Exception):  # SQLAlchemy will raise an IntegrityError
            in_memory_db.commit()
