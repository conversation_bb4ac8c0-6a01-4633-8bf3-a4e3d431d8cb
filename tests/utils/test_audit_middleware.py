"""
Tests for the audit middleware.
"""

from unittest.mock import AsyncMock, patch

import pytest
from aiohttp import web
from aiohttp.test_utils import make_mocked_request

from src.utils.audit_middleware import (
    _should_skip_audit,
    audit_middleware,
    create_audit_middleware,
    enhanced_messages_handler,
)
from src.utils.sql.models import AuditStatus


class TestAuditMiddleware:
    """Test cases for the audit middleware function."""

    @pytest.fixture
    def mock_audit_service(self):
        """Create a mock audit service."""
        with patch('src.utils.audit_middleware.AuditService') as mock:
            mock_instance = AsyncMock()
            mock.return_value = mock_instance
            yield mock_instance

    def test_should_skip_audit(self):
        """Test the _should_skip_audit function."""
        # Test endpoints that should be skipped
        assert _should_skip_audit('/health') is True
        assert _should_skip_audit('/ping') is True
        assert _should_skip_audit('/favicon.ico') is True
        assert _should_skip_audit('/static/css/style.css') is True
        assert _should_skip_audit('/assets/images/logo.png') is True

        # Test endpoints that should not be skipped
        assert _should_skip_audit('/api/messages') is False
        assert _should_skip_audit('/auth/jira/callback') is False
        assert _should_skip_audit('/custom/endpoint') is False

    @pytest.mark.asyncio
    async def test_middleware_success(self, mock_audit_service):
        """Test middleware with successful request processing."""

        # Mock handler
        async def mock_handler(request):
            return web.Response(text='Success', status=200)

        # Create mock request
        request = make_mocked_request('POST', '/api/messages')
        request.headers = {'X-User-ID': 'test_user', 'X-User-Name': 'Test User'}
        request.query = {'param1': 'value1'}

        # Mock request.json() method
        async def mock_json():
            return {'body_param': 'body_value'}

        request.json = mock_json
        request.content_type = 'application/json'

        # Execute middleware
        response = await audit_middleware(request, mock_handler)

        # Verify response
        assert response.status == 200
        assert response.text == 'Success'

        # Verify audit logging was called
        mock_audit_service.log_api_call.assert_called_once()
        call_args = mock_audit_service.log_api_call.call_args
        assert call_args[1]['endpoint'] == '/api/messages'
        assert call_args[1]['method'] == 'POST'
        assert call_args[1]['event_status'] == AuditStatus.SUCCESS
        assert call_args[1]['user_id'] == 'test_user'
        assert call_args[1]['user_name'] == 'Test User'
        assert 'query' in call_args[1]['parameters']
        assert 'body' in call_args[1]['parameters']

    @pytest.mark.asyncio
    async def test_middleware_failure(self, audit_middleware, mock_audit_service):
        """Test middleware with failed request processing."""

        # Mock handler that raises an exception
        async def mock_handler(request):
            raise ValueError('Handler error')

        # Create mock request
        request = make_mocked_request('GET', '/api/test')
        request.headers = {}
        request.query = {}

        # Execute middleware and expect exception
        with pytest.raises(ValueError, match='Handler error'):
            await audit_middleware(request, mock_handler)

        # Verify audit logging was called for both API call and error
        assert mock_audit_service.log_api_call.call_count == 1
        assert mock_audit_service.log_error_event.call_count == 1

        # Check API call logging
        api_call_args = mock_audit_service.log_api_call.call_args
        assert api_call_args[1]['event_status'] == AuditStatus.FAILURE
        assert api_call_args[1]['error_message'] == 'Handler error'

        # Check error event logging
        error_args = mock_audit_service.log_error_event.call_args
        assert error_args[1]['error_message'] == 'Handler error'
        assert error_args[1]['endpoint'] == '/api/test'

    @pytest.mark.asyncio
    async def test_middleware_skip_audit(self, audit_middleware, mock_audit_service):
        """Test middleware skipping audit for certain endpoints."""

        # Mock handler
        async def mock_handler(request):
            return web.Response(text='Health OK', status=200)

        # Create mock request for health endpoint
        request = make_mocked_request('GET', '/health')

        # Execute middleware
        response = await audit_middleware(request, mock_handler)

        # Verify response
        assert response.status == 200

        # Verify no audit logging was called
        mock_audit_service.log_api_call.assert_not_called()
        mock_audit_service.log_error_event.assert_not_called()

    @pytest.mark.asyncio
    async def test_middleware_warning_status(self, audit_middleware, mock_audit_service):
        """Test middleware with warning status (4xx response)."""

        # Mock handler that returns 400
        async def mock_handler(request):
            return web.Response(text='Bad Request', status=400)

        # Create mock request
        request = make_mocked_request('POST', '/api/test')
        request.headers = {}
        request.query = {}

        # Execute middleware
        response = await audit_middleware(request, mock_handler)

        # Verify response
        assert response.status == 400

        # Verify audit logging was called with warning status
        mock_audit_service.log_api_call.assert_called_once()
        call_args = mock_audit_service.log_api_call.call_args
        assert call_args[1]['event_status'] == AuditStatus.WARNING

    @pytest.mark.asyncio
    async def test_middleware_form_data(self, audit_middleware, mock_audit_service):
        """Test middleware with form data request."""

        # Mock handler
        async def mock_handler(request):
            return web.Response(text='Success', status=200)

        # Create mock request
        request = make_mocked_request('POST', '/api/form')
        request.content_type = 'application/x-www-form-urlencoded'

        # Mock request.post() method
        async def mock_post():
            return {'form_field': 'form_value'}

        request.post = mock_post

        # Execute middleware
        response = await audit_middleware(request, mock_handler)

        # Verify response
        assert response.status == 200

        # Verify audit logging was called
        mock_audit_service.log_api_call.assert_called_once()
        call_args = mock_audit_service.log_api_call.call_args
        assert 'body' in call_args[1]['parameters']

    def test_create_audit_middleware(self):
        """Test the create_audit_middleware function."""
        middleware = create_audit_middleware()
        assert middleware == audit_middleware

    @pytest.mark.asyncio
    async def test_enhanced_messages_handler_success(self):
        """Test enhanced_messages_handler with successful processing."""
        with patch('utils.audit_middleware.AuditService') as mock_audit_service:
            mock_audit_instance = AsyncMock()
            mock_audit_service.return_value = mock_audit_instance

            # Mock handler
            async def mock_handler(request):
                return web.Response(text='Message processed', status=200)

            # Create mock request
            request = make_mocked_request('POST', '/api/messages')

            # Execute enhanced handler
            response = await enhanced_messages_handler(request, mock_handler)

            # Verify response
            assert response.status == 200

            # Verify system event logging was called
            mock_audit_instance.log_system_event.assert_called_once()
            call_args = mock_audit_instance.log_system_event.call_args
            assert 'Bot message processed successfully' in call_args[1]['event_description']
            assert call_args[1]['event_status'] == AuditStatus.SUCCESS

    @pytest.mark.asyncio
    async def test_enhanced_messages_handler_failure(self):
        """Test enhanced_messages_handler with failed processing."""
        with patch('utils.audit_middleware.AuditService') as mock_audit_service:
            mock_audit_instance = AsyncMock()
            mock_audit_service.return_value = mock_audit_instance

            # Mock handler that raises an exception
            async def mock_handler(request):
                raise RuntimeError('Message processing failed')

            # Create mock request
            request = make_mocked_request('POST', '/api/messages')

            # Execute enhanced handler and expect exception
            with pytest.raises(RuntimeError, match='Message processing failed'):
                await enhanced_messages_handler(request, mock_handler)

            # Verify error event logging was called
            mock_audit_instance.log_error_event.assert_called_once()
            call_args = mock_audit_instance.log_error_event.call_args
            assert call_args[1]['error_message'] == 'Message processing failed'
            assert call_args[1]['endpoint'] == '/api/messages'

    @pytest.mark.asyncio
    async def test_middleware_no_user_info(self, audit_middleware, mock_audit_service):
        """Test middleware when no user information is available."""

        # Mock handler
        async def mock_handler(request):
            return web.Response(text='Success', status=200)

        # Create mock request with no user headers
        request = make_mocked_request('GET', '/api/test')
        request.headers = {}
        request.query = {}

        # Execute middleware
        response = await audit_middleware(request, mock_handler)

        # Verify response
        assert response.status == 200

        # Verify audit logging was called with None user info
        mock_audit_service.log_api_call.assert_called_once()
        call_args = mock_audit_service.log_api_call.call_args
        assert call_args[1]['user_id'] is None
        assert call_args[1]['user_name'] is None

    @pytest.mark.asyncio
    async def test_middleware_json_response(self, audit_middleware, mock_audit_service):
        """Test middleware with JSON response."""

        # Mock handler that returns JSON
        async def mock_handler(request):
            response_data = {'result': 'success', 'data': 'test'}
            return web.json_response(response_data)

        # Create mock request
        request = make_mocked_request('GET', '/api/data')

        # Execute middleware
        response = await audit_middleware(request, mock_handler)

        # Verify response
        assert response.status == 200

        # Verify audit logging was called
        mock_audit_service.log_api_call.assert_called_once()
        call_args = mock_audit_service.log_api_call.call_args

        # Check response data includes content type
        response_data = call_args[1]['response_data']
        assert 'content_type' in response_data
        assert 'status_code' in response_data
        assert response_data['status_code'] == 200
