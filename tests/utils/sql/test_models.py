"""
Tests for the SQL models.
"""

import datetime
import unittest
from unittest.mock import MagicMock, patch

from sqlalchemy.engine import Engine

from src.utils.sql.models import Base, PMATokens, create_tables


class TestSQLModels(unittest.TestCase):
    """Test cases for the SQL models."""

    def test_pma_tokens_model(self):
        """Test the PMATokens model."""
        # Create a token
        user_id = 'test_user'
        username = 'Test User'
        auth_token = 'test_auth_token'
        refresh_token = 'test_refresh_token'
        expire_at = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=1)

        cloud_id = 'test_cloud_id'
        token = PMATokens(
            user_id=user_id,
            user_name=username,
            auth_token=auth_token,
            refresh_token=refresh_token,
            expire_at=expire_at,
            cloud_id=cloud_id,
        )

        # Check that the model has the expected attributes
        # We don't directly compare values because SQLAlchemy columns are not directly comparable
        assert hasattr(token, 'user_id')
        assert hasattr(token, 'user_name')
        assert hasattr(token, 'auth_token')
        assert hasattr(token, 'refresh_token')
        assert hasattr(token, 'expire_at')
        assert hasattr(token, 'cloud_id')
        assert hasattr(token, 'created_at')
        assert hasattr(token, 'updated_at')
        # Note: created_at and updated_at are only set when the model is saved to the database

        # Test string representation
        assert str(token) == f"<PMATokens(user_id='{user_id}', user_name='{username}', expire_at='{expire_at}')>"

    def test_create_tables(self):
        """Test creating tables."""
        # Mock the engine
        mock_engine = MagicMock(spec=Engine)

        # Mock the metadata
        with patch.object(Base.metadata, 'create_all') as mock_create_all:
            # Call the function
            create_tables(mock_engine)

            # Assertions
            mock_create_all.assert_called_once_with(mock_engine)
