"""
Tests for the database connection manager.
"""

import unittest
from unittest.mock import MagicMock, patch

import pytest
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Session

from src.utils.sql.db_connection import DatabaseConnectionManager


class TestDatabaseConnectionManager(unittest.TestCase):
    """Test cases for the DatabaseConnectionManager class."""

    def setUp(self):
        """Set up test fixtures."""
        # Mock the create_engine function
        self.create_engine_patcher = patch('src.utils.sql.db_connection.create_engine')
        self.mock_create_engine = self.create_engine_patcher.start()
        self.mock_engine = MagicMock(spec=Engine)
        self.mock_create_engine.return_value = self.mock_engine

        # Mock the sessionmaker function
        self.sessionmaker_patcher = patch('src.utils.sql.db_connection.sessionmaker')
        self.mock_sessionmaker = self.sessionmaker_patcher.start()
        self.mock_session_factory = MagicMock()
        self.mock_sessionmaker.return_value = self.mock_session_factory

        # Reset the singleton instance
        DatabaseConnectionManager._instance = None

    def tearDown(self):
        """Tear down test fixtures."""
        self.create_engine_patcher.stop()
        self.sessionmaker_patcher.stop()

    def test_singleton_pattern(self):
        """Test that the class follows the singleton pattern."""
        # Create two instances
        manager1 = DatabaseConnectionManager()
        manager2 = DatabaseConnectionManager()

        # Assertions
        assert manager1 is manager2

    def test_initialization(self):
        """Test that the manager initializes correctly."""
        # Create an instance
        manager = DatabaseConnectionManager()

        # Assertions
        self.mock_create_engine.assert_called_once()
        self.mock_sessionmaker.assert_called_once()

    def test_engine_property(self):
        """Test the engine property."""
        # Create an instance
        manager = DatabaseConnectionManager()

        # Get the engine
        engine = manager.engine

        # Assertions
        assert engine is self.mock_engine

    def test_get_session(self):
        """Test getting a session."""
        # Create an instance
        manager = DatabaseConnectionManager()

        # Mock the session factory
        mock_session = MagicMock(spec=Session)
        self.mock_session_factory.return_value = mock_session

        # Get a session
        session = manager.get_session()

        # Assertions
        assert session is mock_session
        self.mock_session_factory.assert_called_once()

    def test_dispose(self):
        """Test disposing of the engine."""
        # Create an instance
        manager = DatabaseConnectionManager()

        # Dispose of the engine
        manager.dispose()

        # Assertions
        self.mock_engine.dispose.assert_called_once()

    @patch('src.utils.sql.db_connection.create_engine')
    def test_initialization_error(self, mock_create_engine):
        """Test handling errors during initialization."""
        # Set up the mock to raise an exception
        mock_create_engine.side_effect = Exception('Test error')

        # Reset the singleton instance
        DatabaseConnectionManager._instance = None

        # Try to create an instance
        with pytest.raises(Exception):
            DatabaseConnectionManager()
