"""
Tests for AI planner wrapper functionality.

This module tests the AuditActionPlanner wrapper that captures
AI responses from the Teams AI framework planner.
"""

from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from botbuilder.core import TurnContext
from teams.ai.planners import ActionPlanner
from teams.state import TurnState

from src.utils.ai_planner_wrapper import AuditActionPlanner, create_audit_planner


class TestAuditActionPlanner:
    """Test cases for AI planner wrapper functionality."""

    @pytest.fixture
    def mock_turn_context(self):
        """Create a mock TurnContext for testing."""
        context = MagicMock(spec=TurnContext)
        activity = MagicMock()
        activity.from_property = MagicMock()
        activity.from_property.id = 'test_user_123'
        activity.from_property.name = 'Test User'
        activity.id = 'activity_456'
        activity.channel_id = 'channel_789'
        context.activity = activity
        return context

    @pytest.fixture
    def mock_turn_state(self):
        """Create a mock TurnState for testing."""
        state = MagicMock(spec=TurnState)
        # Mock conversation attribute
        conversation = MagicMock()
        conversation.get = MagicMock(return_value='session_123')
        state.conversation = conversation
        return state

    @pytest.fixture
    def mock_original_planner(self):
        """Create a mock ActionPlanner for testing."""
        planner = MagicMock(spec=ActionPlanner)
        planner.begin_task = AsyncMock()
        planner.options = MagicMock()
        return planner

    @pytest.fixture
    def mock_ai_response_logger(self):
        """Create a mock AI response logger."""
        with patch('src.utils.ai_planner_wrapper.ai_response_logger') as mock_logger:
            mock_logger.log_ai_response = AsyncMock(return_value='event_123')
            mock_logger.log_ai_error = AsyncMock(return_value='error_456')
            yield mock_logger

    def test_audit_planner_initialization(self, mock_original_planner):
        """Test audit planner initialization."""
        audit_planner = AuditActionPlanner(mock_original_planner)

        assert audit_planner.original_planner == mock_original_planner
        assert audit_planner._start_times == {}

    def test_audit_planner_attribute_delegation(self, mock_original_planner):
        """Test that attributes are properly delegated to original planner."""
        mock_original_planner.some_attribute = 'test_value'
        mock_original_planner.some_method = MagicMock(return_value='method_result')

        audit_planner = AuditActionPlanner(mock_original_planner)

        # Test attribute access
        assert audit_planner.some_attribute == 'test_value'

        # Test method call
        result = audit_planner.some_method('arg1', 'arg2')
        assert result == 'method_result'
        mock_original_planner.some_method.assert_called_once_with('arg1', 'arg2')

    def test_audit_planner_options_property(self, mock_original_planner):
        """Test that options property is properly delegated."""
        mock_options = MagicMock()
        mock_original_planner.options = mock_options

        audit_planner = AuditActionPlanner(mock_original_planner)

        assert audit_planner.options == mock_options

    @pytest.mark.asyncio
    async def test_begin_task_success_with_response(
        self, mock_turn_context, mock_turn_state, mock_original_planner, mock_ai_response_logger
    ):
        """Test successful begin_task with AI response content."""
        # Create a mock plan with response content
        mock_plan = MagicMock()
        mock_plan.response = 'This is an AI-generated response from the planner.'
        mock_original_planner.begin_task.return_value = mock_plan

        audit_planner = AuditActionPlanner(mock_original_planner)

        # Execute begin_task
        result = await audit_planner.begin_task(mock_turn_context, mock_turn_state)

        # Verify original planner was called
        mock_original_planner.begin_task.assert_called_once_with(mock_turn_context, mock_turn_state)

        # Verify result is returned
        assert result == mock_plan

        # Verify AI response was logged
        mock_ai_response_logger.log_ai_response.assert_called_once()
        call_args = mock_ai_response_logger.log_ai_response.call_args[1]

        assert call_args['context'] == mock_turn_context
        assert call_args['response_content'] == 'This is an AI-generated response from the planner.'
        assert call_args['response_type'] == 'ai_plan_response'
        assert call_args['session_id'] == 'session_123'
        assert 'duration_ms' in call_args
        assert 'metadata' in call_args

    @pytest.mark.asyncio
    async def test_begin_task_success_no_response(
        self, mock_turn_context, mock_turn_state, mock_original_planner, mock_ai_response_logger
    ):
        """Test successful begin_task without AI response content."""
        # Create a mock plan without response content
        mock_plan = MagicMock()
        mock_plan.response = None
        mock_plan.message = None
        mock_plan.content = None
        mock_plan.commands = None
        mock_original_planner.begin_task.return_value = mock_plan

        audit_planner = AuditActionPlanner(mock_original_planner)

        # Execute begin_task
        result = await audit_planner.begin_task(mock_turn_context, mock_turn_state)

        # Verify original planner was called
        mock_original_planner.begin_task.assert_called_once()

        # Verify result is returned
        assert result == mock_plan

        # Verify AI response was not logged (no content)
        mock_ai_response_logger.log_ai_response.assert_not_called()

    @pytest.mark.asyncio
    async def test_begin_task_with_commands_response(
        self, mock_turn_context, mock_turn_state, mock_original_planner, mock_ai_response_logger
    ):
        """Test begin_task with response content in commands."""
        # Create a mock plan with commands containing responses
        mock_command1 = MagicMock()
        mock_command1.configure_mock(response='Command 1 response', message=None)

        mock_command2 = MagicMock()
        mock_command2.configure_mock(response=None, message='Command 2 message')

        mock_plan = MagicMock()
        mock_plan.response = None
        mock_plan.message = None
        mock_plan.content = None
        mock_plan.commands = [mock_command1, mock_command2]
        mock_original_planner.begin_task.return_value = mock_plan

        audit_planner = AuditActionPlanner(mock_original_planner)

        # Execute begin_task
        result = await audit_planner.begin_task(mock_turn_context, mock_turn_state)

        # Verify AI response was logged with combined command responses
        mock_ai_response_logger.log_ai_response.assert_called_once()
        call_args = mock_ai_response_logger.log_ai_response.call_args[1]

        expected_content = 'Command 1 response\nCommand 2 message'
        assert call_args['response_content'] == expected_content

    @pytest.mark.asyncio
    async def test_begin_task_error_handling(
        self, mock_turn_context, mock_turn_state, mock_original_planner, mock_ai_response_logger
    ):
        """Test begin_task error handling."""
        # Make original planner raise an exception
        error_message = 'AI planner timeout error'
        mock_original_planner.begin_task.side_effect = Exception(error_message)

        audit_planner = AuditActionPlanner(mock_original_planner)

        # Execute begin_task and expect exception
        with pytest.raises(Exception, match=error_message):
            await audit_planner.begin_task(mock_turn_context, mock_turn_state)

        # Verify error was logged
        mock_ai_response_logger.log_ai_error.assert_called_once()
        call_args = mock_ai_response_logger.log_ai_error.call_args[1]

        assert call_args['context'] == mock_turn_context
        assert call_args['error_message'] == error_message
        assert call_args['error_type'] == 'ai_planning_error'
        assert 'duration_ms' in call_args

    @pytest.mark.asyncio
    async def test_begin_task_no_session_id(self, mock_turn_context, mock_original_planner, mock_ai_response_logger):
        """Test begin_task with state that has no session ID."""
        # Create state without session information
        state = MagicMock(spec=TurnState)
        # Remove conversation attribute entirely
        del state.conversation
        # Make sure get method doesn't exist
        if hasattr(state, 'get'):
            del state.get

        mock_plan = MagicMock()
        mock_plan.response = 'AI response without session'
        mock_original_planner.begin_task.return_value = mock_plan

        audit_planner = AuditActionPlanner(mock_original_planner)

        # Execute begin_task
        result = await audit_planner.begin_task(mock_turn_context, state)

        # Verify AI response was logged with None session_id
        mock_ai_response_logger.log_ai_response.assert_called_once()
        call_args = mock_ai_response_logger.log_ai_response.call_args[1]

        assert call_args['session_id'] is None

    def test_create_audit_planner_function(self, mock_original_planner):
        """Test the create_audit_planner helper function."""
        audit_planner = create_audit_planner(mock_original_planner)

        assert isinstance(audit_planner, AuditActionPlanner)
        assert audit_planner.original_planner == mock_original_planner

    @pytest.mark.asyncio
    async def test_extract_ai_response_from_plan_various_attributes(self, mock_original_planner):
        """Test extraction of AI response from plans with various attribute structures."""
        audit_planner = AuditActionPlanner(mock_original_planner)

        # Test with response attribute
        plan1 = MagicMock()
        plan1.response = 'Response from response attribute'
        plan1.message = None
        plan1.content = None
        result1 = audit_planner._extract_ai_response_from_plan(plan1)
        assert result1 == 'Response from response attribute'

        # Test with message attribute
        plan2 = MagicMock()
        plan2.response = None
        plan2.message = 'Response from message attribute'
        plan2.content = None
        result2 = audit_planner._extract_ai_response_from_plan(plan2)
        assert result2 == 'Response from message attribute'

        # Test with content attribute
        plan3 = MagicMock()
        plan3.response = None
        plan3.message = None
        plan3.content = 'Response from content attribute'
        result3 = audit_planner._extract_ai_response_from_plan(plan3)
        assert result3 == 'Response from content attribute'

        # Test with no response content
        plan4 = MagicMock()
        plan4.response = None
        plan4.message = None
        plan4.content = None
        plan4.commands = None
        result4 = audit_planner._extract_ai_response_from_plan(plan4)
        assert result4 is None
