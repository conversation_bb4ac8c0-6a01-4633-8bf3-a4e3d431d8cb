"""
Tests for bot response capture functionality in audit decorators.

This module tests the new AuditTurnContext wrapper that automatically
captures bot responses for audit logging.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from botbuilder.core import TurnContext
from botbuilder.schema import Activity, ActivityTypes

from src.utils.audit_decorators import AuditTurnContext, create_audit_context, audit_message_handler
from src.utils.sql.models import AuditEventType, AuditStatus


class TestAuditTurnContext:
    """Test the AuditTurnContext wrapper functionality."""

    @pytest.fixture
    def mock_turn_context(self):
        """Create a mock TurnContext for testing."""
        context = MagicMock(spec=TurnContext)
        
        # Mock activity with user info
        activity = MagicMock()
        activity.from_property = MagicMock()
        activity.from_property.id = 'test_user_123'
        activity.from_property.name = 'Test User'
        activity.id = 'activity_456'
        activity.channel_id = 'test_channel'
        activity.text = 'Hello bot'
        
        context.activity = activity
        context.send_activity = AsyncMock(return_value=MagicMock(id='response_789'))
        
        return context

    @pytest.fixture
    def mock_audit_service(self):
        """Create a mock AuditService for testing."""
        with patch('src.utils.audit_decorators.AuditService') as mock_service:
            service_instance = AsyncMock()
            mock_service.return_value = service_instance
            yield service_instance

    @pytest.mark.asyncio
    async def test_audit_context_creation(self, mock_turn_context):
        """Test creating an audit context wrapper."""
        session_id = 'test_session_123'
        
        audit_context = create_audit_context(mock_turn_context, session_id)
        
        assert isinstance(audit_context, AuditTurnContext)
        assert audit_context._original_context == mock_turn_context
        assert audit_context._session_id == session_id
        assert audit_context._user_id == 'test_user_123'
        assert audit_context._user_name == 'Test User'

    @pytest.mark.asyncio
    async def test_send_activity_text_logging(self, mock_turn_context, mock_audit_service):
        """Test that text responses are logged correctly."""
        audit_context = AuditTurnContext(mock_turn_context, 'session_123')
        audit_context._audit_service = mock_audit_service
        
        # Send a text response
        response_text = "Hello! How can I help you today?"
        await audit_context.send_activity(response_text)
        
        # Verify original send_activity was called
        mock_turn_context.send_activity.assert_called_once_with(response_text)
        
        # Verify audit logging was called
        mock_audit_service.log_bot_response.assert_called_once()
        call_args = mock_audit_service.log_bot_response.call_args[1]
        
        assert call_args['user_id'] == 'test_user_123'
        assert call_args['user_name'] == 'Test User'
        assert call_args['response_content'] == response_text
        assert call_args['session_id'] == 'session_123'
        assert call_args['activity_id'] == 'activity_456'
        assert call_args['channel_id'] == 'test_channel'
        assert 'duration_ms' in call_args

    @pytest.mark.asyncio
    async def test_send_activity_with_activity_object(self, mock_turn_context, mock_audit_service):
        """Test that Activity objects are logged correctly."""
        audit_context = AuditTurnContext(mock_turn_context, 'session_123')
        audit_context._audit_service = mock_audit_service
        
        # Create an Activity object
        activity = Activity(
            type=ActivityTypes.message,
            text="This is a bot response with an Activity object"
        )
        
        await audit_context.send_activity(activity)
        
        # Verify original send_activity was called
        mock_turn_context.send_activity.assert_called_once_with(activity)
        
        # Verify audit logging was called with correct content
        mock_audit_service.log_bot_response.assert_called_once()
        call_args = mock_audit_service.log_bot_response.call_args[1]
        
        assert call_args['response_content'] == "This is a bot response with an Activity object"

    @pytest.mark.asyncio
    async def test_send_activity_with_attachments(self, mock_turn_context, mock_audit_service):
        """Test that activities with attachments are logged correctly."""
        audit_context = AuditTurnContext(mock_turn_context, 'session_123')
        audit_context._audit_service = mock_audit_service
        
        # Create an Activity with attachments
        attachment = MagicMock()
        attachment.content_type = 'application/vnd.microsoft.card.adaptive'
        
        activity = Activity(
            type=ActivityTypes.message,
            attachments=[attachment]
        )
        
        await audit_context.send_activity(activity)
        
        # Verify audit logging was called with attachment summary
        mock_audit_service.log_bot_response.assert_called_once()
        call_args = mock_audit_service.log_bot_response.call_args[1]
        
        expected_content = "[Bot sent 1 attachment(s): application/vnd.microsoft.card.adaptive]"
        assert call_args['response_content'] == expected_content

    @pytest.mark.asyncio
    async def test_send_activity_error_handling(self, mock_turn_context, mock_audit_service):
        """Test that audit errors don't break the original functionality."""
        audit_context = AuditTurnContext(mock_turn_context, 'session_123')
        audit_context._audit_service = mock_audit_service
        
        # Make audit service fail
        mock_audit_service.log_bot_response.side_effect = Exception("Audit service error")
        
        # Send activity should still work
        response_text = "Hello despite audit error"
        result = await audit_context.send_activity(response_text)
        
        # Verify original send_activity was still called
        mock_turn_context.send_activity.assert_called_once_with(response_text)
        
        # Verify we got the result back
        assert result == mock_turn_context.send_activity.return_value

    @pytest.mark.asyncio
    async def test_attribute_delegation(self, mock_turn_context):
        """Test that other attributes are delegated to the original context."""
        audit_context = AuditTurnContext(mock_turn_context, 'session_123')
        
        # Access an attribute that should be delegated
        assert audit_context.activity == mock_turn_context.activity
        
        # Test method delegation
        mock_turn_context.some_method = MagicMock(return_value="delegated_result")
        assert audit_context.some_method() == "delegated_result"

    @pytest.mark.asyncio
    async def test_no_user_id_no_logging(self, mock_audit_service):
        """Test that no logging occurs when user_id is not available."""
        # Create context without user info
        context = MagicMock(spec=TurnContext)
        context.activity = None
        context.send_activity = AsyncMock()
        
        audit_context = AuditTurnContext(context, 'session_123')
        audit_context._audit_service = mock_audit_service
        
        await audit_context.send_activity("Hello")
        
        # Verify no audit logging was attempted
        mock_audit_service.log_bot_response.assert_not_called()


class TestAuditMessageHandlerIntegration:
    """Test the integration of bot response capture with message handlers."""

    @pytest.fixture
    def mock_turn_context(self):
        """Create a mock TurnContext for testing."""
        context = MagicMock(spec=TurnContext)
        
        # Mock activity with user info
        activity = MagicMock()
        activity.from_property = MagicMock()
        activity.from_property.id = 'test_user_123'
        activity.from_property.name = 'Test User'
        activity.id = 'activity_456'
        activity.channel_id = 'test_channel'
        activity.text = '/help'
        
        context.activity = activity
        context.send_activity = AsyncMock(return_value=MagicMock(id='response_789'))
        
        return context

    @pytest.mark.asyncio
    async def test_message_handler_with_bot_response_capture(self, mock_turn_context):
        """Test that message handlers automatically capture bot responses."""
        
        @audit_message_handler("test_handler")
        async def test_handler(context, state=None):
            # This should be captured in audit logs
            await context.send_activity("This is a bot response from the handler")
            return False
        
        with patch('src.utils.audit_decorators.AuditService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            
            # Execute the decorated handler
            result = await test_handler(mock_turn_context, None)
            
            # Verify the handler executed successfully
            assert result is False
            
            # Verify user message was logged
            assert mock_service.log_user_message.called
            
            # Verify bot response was logged
            assert mock_service.log_bot_response.called
            
            # Check bot response logging details
            bot_response_call = mock_service.log_bot_response.call_args[1]
            assert bot_response_call['user_id'] == 'test_user_123'
            assert bot_response_call['response_content'] == "This is a bot response from the handler"
