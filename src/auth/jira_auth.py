"""
Module for handling Jira OAuth authentication flows.

This module provides comprehensive functionality for Jira authentication,
including initiating auth flows, handling callbacks, refreshing tokens,
and managing token expiration. All token management is handled exclusively
through SQLTokenStorage, which stores tokens in a SQL database.

The module has been refactored to remove all dependencies on environment variables
and state objects for token storage, simplifying the token management process.
"""

import traceback
from http import HTTPStatus
from typing import Union

import pendulum
from aiohttp import ClientSession
from botbuilder.core import TurnContext
from botbuilder.schema import Activity
from loguru import logger
from requests_oauthlib import OAuth2Session

from config import Config
from tools.cards import create_jira_auth_card

from .token_manager import SQLTokenStorage


class JiraAuthManager:
    """Manages Jira OAuth authentication flows."""

    @staticmethod
    def get_expire_time(expires_in: int, before: int = 900) -> pendulum.DateTime:
        """
        Calculate token expiration time with a buffer period.

        Args:
            expires_in: Token expiration time in seconds
            before: Buffer time in seconds to subtract from expiration time (default: 15 minutes)

        Returns:
            pendulum.DateTime: Calculated expiration time
        """
        now = pendulum.now()
        expire_time = now.add(seconds=expires_in - before)
        return expire_time

    @staticmethod
    def check_token_expiration(
        expire_time: Union[str, pendulum.DateTime, pendulum.Date, pendulum.Time, pendulum.Duration],
    ) -> bool:
        """
        Check if a token is expired.

        Args:
            expire_time: Token expiration time in various formats

        Returns:
            bool: True if token is expired, False otherwise

        Raises:
            TypeError: If expire_time is an unsupported type
        """
        now = pendulum.now()

        if isinstance(expire_time, str):
            expire_time = pendulum.parse(expire_time, tz='Australia/Sydney')

        # Convert all types to DateTime for comparison
        if isinstance(expire_time, pendulum.DateTime):
            pass  # Already the right type
        elif isinstance(expire_time, pendulum.Date):
            expire_time = pendulum.datetime(expire_time.year, expire_time.month, expire_time.day, tz='Australia/Sydney')
        elif isinstance(expire_time, pendulum.Time):
            expire_time = pendulum.datetime(
                now.year,
                now.month,
                now.day,
                expire_time.hour,
                expire_time.minute,
                expire_time.second,
                tz='Australia/Sydney',
            )
        elif isinstance(expire_time, pendulum.Duration):
            expire_time = now + expire_time
        else:
            raise TypeError(f'Unsupported type: {type(expire_time)}')

        if now >= expire_time:
            logger.debug(f'Token expired at {expire_time}, now is {now}')
            return True
        else:
            return False

    @staticmethod
    async def on_jira_auth(context: TurnContext):
        """
        Initiate Jira authentication flow and send auth URL to user using an Adaptive Card.

        Args:
            context: Bot turn context
        """

        # Define required scopes
        scope = [
            'read:me',
            'read:jira-user',
            'read:jira-work',
            'write:jira-work',
            'offline_access',
        ]
        audience = 'api.atlassian.com'

        # Get user ID to use as OAuth state
        user_id = context.activity.from_property.id
        user_name = context.activity.from_property.name
        auth_state = f'{user_id}|{user_name}'

        # Create OAuth session
        jira_oauth = OAuth2Session(
            Config.JIRA_CLIENT_ID,
            scope=scope,
            redirect_uri=f'{Config.BOT_ENDPOINT}/auth/jira/callback',
        )

        # Generate authorization URL with user_id as the state parameter
        authorization_url, _ = jira_oauth.authorization_url(
            Config.JIRA_AUTHORIASATION_URI_BASE, audience=audience, state=auth_state
        )

        # No need to store the state separately as we're using the user_id directly
        logger.info(f'Initiated auth for user {user_name} with user_id as state parameter')

        # Create and send the Jira authentication Adaptive Card
        auth_card = create_jira_auth_card(authorization_url)
        await context.send_activity(Activity(attachments=[auth_card]))

    @staticmethod
    async def handle_callback(code: str, auth_state: str) -> dict:
        """
        Handle the OAuth callback from Jira.

        Args:
            code: Authorization code from Jira
            auth_state: OAuth state from Jira, which is the user_id

        Returns:
            dict: Token data if successful

        Raises:
            ValueError: If state verification fails
        """
        # The auth_state is now directly the user_id
        user_parts = auth_state.split('|')
        if len(user_parts) != 2:
            logger.error(f'Invalid auth_state format: {auth_state}')
            raise ValueError('Invalid authentication state format')

        user_id, user_name = user_parts
        logger.info(f'Processing OAuth callback for user {user_id} ({user_name})')

        # Create SQL token storage
        token_storage = SQLTokenStorage()

        try:
            logger.debug('Creating OAuth session')
            jira_oauth = OAuth2Session(
                Config.JIRA_CLIENT_ID,
                state=auth_state,
                redirect_uri=f'{Config.BOT_ENDPOINT}/auth/jira/callback',
            )

            logger.debug(f'Fetching token from {Config.JIRA_TOKEN_URI}')
            token_data = jira_oauth.fetch_token(
                Config.JIRA_TOKEN_URI,
                client_secret=Config.JIRA_CLIENT_SECRET,
                code=code,
            )
            # check if token_data is valid
            if not token_data:
                logger.error('Failed to obtain access token')
                raise ValueError('Failed to obtain access token')

            logger.debug('Token obtained successfully')
            # Add expiration time
            expires_at = JiraAuthManager.get_expire_time(token_data['expires_in'])
            token_data['expires_at'] = expires_at.isoformat()
            logger.debug(f'Token will expire at: {expires_at}')

            # Get the cloud ID from Jira API
            cloud_id = None
            logger.debug(f'Retrieving cloud ID from {Config.JIRA_CLOUD_ID_URI}')
            async with ClientSession() as session:
                async with session.get(
                    url=Config.JIRA_CLOUD_ID_URI,
                    headers={
                        'Authorization': f'Bearer {token_data["access_token"]}',
                        'Accept': 'application/json',
                    },
                ) as resp:
                    if resp.status == HTTPStatus.OK:
                        cloud_data = await resp.json()
                        if cloud_data and len(cloud_data) > 0:
                            cloud_id = cloud_data[0].get('id')
                            logger.info(f'Retrieved cloud ID: {cloud_id}')
                        else:
                            logger.warning('No cloud ID found in response')
                    else:
                        error_text = await resp.text()
                        logger.error(f'Failed to get cloud ID: {resp.status} - {error_text}')
                        raise ValueError('Failed to get cloud ID')

            # Store tokens in SQL database using the user_id from the state parameter
            logger.debug(f'Saving tokens for user {user_name} with cloud ID: {cloud_id}')
            await token_storage.save_tokens(user_id, user_name, token_data, str(cloud_id))
            logger.info(f'Tokens saved for user {user_name} with cloud ID: {cloud_id}')

            return token_data

        except Exception as e:
            logger.error(f'Error in OAuth callback handling: {str(e)}')
            logger.debug(f'Error traceback: {traceback.format_exc()}')
            raise

    @staticmethod
    async def refresh_token(user_id: str, user_name: str) -> bool:
        """
        Refresh Jira token.

        Args:
            user_id: User ID

        Returns:
            bool: True if token was refreshed successfully, False otherwise
        """
        # Create SQL token storage
        token_storage = SQLTokenStorage()

        # Get tokens from storage
        tokens = token_storage.get_tokens(user_id)
        refresh_token = tokens.get('refresh_token')

        if not refresh_token:
            logger.debug('No refresh token found.')
            return False

        try:
            jira_oauth = OAuth2Session(
                Config.JIRA_CLIENT_ID,
                token=tokens,
            )
            refreshed_tokens: dict = jira_oauth.refresh_token(
                token_url=Config.JIRA_TOKEN_URI,
                client_id=Config.JIRA_CLIENT_ID,
                client_secret=Config.JIRA_CLIENT_SECRET,
            )

            # Add expiration time
            expires_at = JiraAuthManager.get_expire_time(refreshed_tokens['expires_in'])
            refreshed_tokens['expires_at'] = expires_at.isoformat()

            await token_storage.save_tokens(user_id, user_name, refreshed_tokens)
            logger.debug(f'Successfully refreshed token for user {user_name}')
            return True

        except Exception as e:
            logger.error(f'Exception while refreshing token: {e}')
            return False
