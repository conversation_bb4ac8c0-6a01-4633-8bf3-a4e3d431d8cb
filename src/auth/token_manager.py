"""
Module for managing Jira OAuth tokens.
"""

import datetime
import traceback
from abc import ABC, abstractmethod
from typing import Dict, List, Optional

import pendulum
from aiohttp import ClientSession
from loguru import logger
from sqlalchemy.exc import SQLAlchemyError

from config import Config
from utils.sql.db_connection import DatabaseConnectionManager
from utils.sql.models import PMATokens, create_tables


class TokenStorage(ABC):
    """Abstract base class for token storage implementations."""

    @abstractmethod
    async def save_tokens(self, user_id: str, user_name: str, token_data: dict, cloud_id: Optional[str] = None) -> None:
        """
        Save tokens for a user.

        Args:
            user_id: User ID
            user_name: User name
            token_data: Token data including access_token, refresh_token, and expires_at
            cloud_id: Optional Jira cloud instance identifier
        """
        pass

    @abstractmethod
    def get_tokens(self, user_id: str) -> dict:
        """Get tokens for a user."""
        pass

    @abstractmethod
    def save_auth_state(self, user_id: str, state: str) -> None:
        """Save OAuth state for a user."""
        pass

    @abstractmethod
    def get_auth_state(self, user_id: str) -> str:
        """Get OAuth state for a user."""
        pass


class SQLTokenStorage(TokenStorage):
    """
    Implementation of TokenStorage using SQL database.

    This class provides methods for storing and retrieving tokens
    directly from a SQL database without a separate repository layer.
    """

    def __init__(self):
        """
        Initialize the SQL token storage.

        Args:
            state: Optional state object for compatibility with StateTokenStorage
        """
        self.db_manager = DatabaseConnectionManager()
        # Ensure tables exist
        create_tables(self.db_manager.engine)
        self.auth_states = {}  # In-memory storage for auth states

    async def save_tokens(self, user_id: str, user_name: str, token_data: dict, cloud_id: Optional[str] = None) -> None:
        """
        Save tokens for a user.

        Args:
            user_id: User ID
            user_name: User name
            token_data: Token data including access_token, refresh_token, and expires_at
            cloud_id: Optional Jira cloud instance identifier
        """
        try:
            # Parse expires_at if it's a string
            if isinstance(token_data.get('expires_at'), str):
                expires_at = pendulum.parse(token_data['expires_at'])
            else:
                expires_at = token_data.get('expires_at', pendulum.now().add(hours=1))

            # Save to database
            try:
                with self.db_manager.get_session() as session:
                    # Check if token exists
                    token = session.query(PMATokens).filter(PMATokens.user_id == user_id).first()

                    if token:
                        # Update existing token
                        token.auth_token = token_data['access_token']
                        token.refresh_token = token_data['refresh_token']
                        token.expire_at = expires_at
                        token.user_name = user_name
                        if cloud_id is not None:
                            token.cloud_id = cloud_id
                        # updated_at will be set automatically by SQLAlchemy
                    else:
                        # Create new token
                        token = PMATokens(
                            user_id=user_id,
                            user_name=user_name,
                            auth_token=token_data['access_token'],
                            refresh_token=token_data['refresh_token'],
                            expire_at=expires_at,
                            cloud_id=cloud_id,
                        )
                        session.add(token)

                    session.commit()
                    success = True
            except SQLAlchemyError as e:
                logger.error(f'Error saving token for user {user_id}: {e}')
                success = False

            if not success:
                logger.error(f'Failed to save tokens for user {user_id}')

        except Exception as e:
            logger.error(f'Error saving tokens for user {user_id}: {e}')

    def get_tokens(self, user_id: str) -> dict:
        """
        Get tokens for a user.

        Args:
            user_id: User ID

        Returns:
            dict: Token data including access_token, refresh_token, and expires_at
        """
        try:
            # Try to get from database
            try:
                with self.db_manager.get_session() as session:
                    token = session.query(PMATokens).filter(PMATokens.user_id == user_id).first()

                    if token:
                        token_data = {
                            'auth_token': token.auth_token,
                            'refresh_token': token.refresh_token,
                            'expire_at': token.expire_at,
                            'user_name': token.user_name,
                            'cloud_id': token.cloud_id,
                        }
                    else:
                        token_data = None
            except SQLAlchemyError as e:
                logger.error(f'Error getting token for user {user_id}: {e}')
                token_data = None

            if token_data:
                return {
                    'access_token': token_data['auth_token'],
                    'refresh_token': token_data['refresh_token'],
                    'expires_at': token_data['expire_at'].isoformat()
                    if isinstance(token_data['expire_at'], datetime.datetime)
                    else token_data['expire_at'],
                    'user_name': token_data['user_name'],
                    'cloud_id': token_data['cloud_id'],
                }

            # If not found anywhere, return empty dict with None values
            return {
                'access_token': None,
                'refresh_token': None,
                'expires_at': None,
                'user_name': None,
                'cloud_id': None,
            }

        except Exception as e:
            logger.error(f'Error getting tokens for user {user_id}: {e}')
            return {
                'access_token': None,
                'refresh_token': None,
                'expires_at': None,
                'user_name': None,
                'cloud_id': None,
            }

    def delete_token(self, user_id: str) -> bool:
        """
        Delete a token for a user.

        Args:
            user_id: User ID

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with self.db_manager.get_session() as session:
                token = session.query(PMATokens).filter(PMATokens.user_id == user_id).first()

                if token:
                    session.delete(token)
                    session.commit()
                    return True
                return False
        except SQLAlchemyError as e:
            logger.error(f'Error deleting token for user {user_id}: {e}')
            return False

    def get_all_tokens(self) -> List[Dict]:
        """
        Get all tokens.

        Returns:
            List[Dict]: List of token data
        """
        try:
            with self.db_manager.get_session() as session:
                tokens = session.query(PMATokens).all()

                return [
                    {
                        'user_id': token.user_id,
                        'user_name': token.user_name,
                        'access_token': token.auth_token,
                        'refresh_token': token.refresh_token,
                        'expires_at': token.expire_at.isoformat()
                        if isinstance(token.expire_at, datetime.datetime)
                        else token.expire_at,
                        'created_at': token.created_at,
                        'updated_at': token.updated_at,
                    }
                    for token in tokens
                ]
        except SQLAlchemyError as e:
            logger.error(f'Error getting all tokens: {e}')
            return []

    def save_auth_state(self, user_id: str, state: str) -> None:
        """
        Save OAuth state for a user.

        Note: This method is kept for compatibility with the TokenStorage interface,
        but is no longer needed for the main OAuth flow since we now use the user_id
        directly as the state parameter.

        Args:
            user_id: User ID
            state: OAuth state
        """
        # Store in memory (for backward compatibility)
        self.auth_states[user_id] = state
        logger.debug(f'Auth state saved for user {user_id} (deprecated)')

    def get_auth_state(self, user_id: str) -> str:
        """
        Get OAuth state for a user.

        Note: This method is kept for compatibility with the TokenStorage interface,
        but is no longer needed for the main OAuth flow since we now use the user_id
        directly as the state parameter.

        Args:
            user_id: User ID

        Returns:
            str: OAuth state
        """
        # Try to get from memory (for backward compatibility)
        state = self.auth_states.get(user_id, '')

        return state


class JiraTokenManager:
    """Manages Jira OAuth tokens."""

    def __init__(self, token_storage: TokenStorage):
        self.token_storage = token_storage

    def is_token_valid(self, user_id: str) -> bool:
        """
        Check if the user's token is valid and not expired.

        Args:
            user_id: User ID

        Returns:
            bool: True if token is valid and not expired
        """
        tokens = self.token_storage.get_tokens(user_id)

        # Check if we have tokens
        if not tokens['access_token'] or not tokens['refresh_token']:
            return False

        # Check if token is expired
        if not tokens['expires_at']:
            return False

        try:
            # Parse expires_at as datetime
            if isinstance(tokens['expires_at'], str):
                expires_at = pendulum.parse(tokens['expires_at'])
            else:
                expires_at = tokens['expires_at']

            # Check if token is expired
            return pendulum.now() < expires_at
        except Exception as e:
            logger.error(f'Error checking token expiration: {e}')
            return False

    async def refresh_token(self, user_id: str, user_name: str) -> bool:
        """
        Refresh the access token using the refresh token.

        Args:
            user_id: User ID
            context: Optional TurnContext to get user information

        Returns:
            bool: True if refresh was successful
        """
        logger.info(f'Attempting to refresh token for user: {user_id}')
        tokens = self.token_storage.get_tokens(user_id)
        refresh_token = tokens['refresh_token']

        if not refresh_token:
            logger.warning(f'No refresh token found for user: {user_id}')
            return False

        try:
            logger.debug('Creating HTTP session for token refresh')
            async with ClientSession() as session:
                logger.debug(f'Sending refresh token request to: {Config.JIRA_TOKEN_URI}')
                response = await session.post(
                    url=Config.JIRA_TOKEN_URI,
                    headers={'Content-Type': 'application/json'},
                    json={
                        'grant_type': 'refresh_token',
                        'client_id': Config.JIRA_CLIENT_ID,
                        'client_secret': Config.JIRA_CLIENT_SECRET,
                        'refresh_token': refresh_token,
                    },
                )

                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f'Token refresh failed: {response.status} - {error_text}')
                    return False

                token_data = await response.json()
                logger.debug('Received new token data')

                # Add expiration time
                expires_at = pendulum.now().add(seconds=token_data['expires_in'])
                token_data['expires_at'] = expires_at.isoformat()
                logger.debug(f'New token will expire at: {expires_at}')

                # Get the existing cloud_id to preserve it
                existing_tokens = self.token_storage.get_tokens(user_id)
                cloud_id = existing_tokens.get('cloud_id')
                logger.debug(f'Preserving cloud_id: {cloud_id}')

                # Store tokens with context for user_name retrieval, preserving cloud_id
                await self.token_storage.save_tokens(user_id, user_name, token_data, cloud_id)
                logger.info(f'Token refreshed successfully for user: {user_id}')
                return True

        except Exception as e:
            logger.error(f'Error refreshing token for user {user_id}: {e}')
            logger.debug(f'Refresh error traceback: {traceback.format_exc()}')
            return False

    async def ensure_valid_token(self, user_id: str, user_name: str) -> bool:
        """
        Ensure the user has a valid token, refreshing if needed.

        Args:
            user_id: User ID
            context: Optional TurnContext to get user information

        Returns:
            bool: True if a valid token is available
        """
        if self.is_token_valid(user_id):
            return True

        # Try to refresh
        return await self.refresh_token(user_id, user_name)
