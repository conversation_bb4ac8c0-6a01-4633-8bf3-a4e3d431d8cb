[{"version": "0.0.7", "releaseNotes": {"fix": [""], "feat": ["Implement comprehensive Loguru-based logging configuration with timezone support", "Implement action logging documentation and models", "Refactor authentication and token management", "Add new issue models and utilities", "Update dependencies and project configuration", "Enhance test coverage with new test modules", "Clean up deprecated files and improve code organization"]}, "releaseTime": "2025-05-26 22:03:05"}, {"version": "0.0.6", "releaseNotes": {"fix": [""], "feat": ["Now the user authentication token is saved in a SQL DB.", "Disable the submit button when logging time once it is clicked to prevent from submitting duplicate time log.", "Create adaptive cards for some of the messages the bot returns.", "Optimise infrastructure code"]}, "releaseTime": "2025-05-18 1:20:00"}, {"version": "0.0.5", "releaseNotes": {"fix": [], "feat": ["Allow user to schedule a reminder by /schedule HH:MM command"]}, "releaseTime": "2025-04-22 16:20:00"}, {"version": "0.0.4", "releaseNotes": {"fix": [], "feat": ["Add a /help command", "Allow user to mention other users in the comment, i.e. @username"]}, "releaseTime": "2025-04-16 20:10:00"}, {"version": "0.0.3", "releaseNotes": {"fix": ["BIRI-10278: An assistant message with 'tool_calls' must be followed by tool messages responding to each 'tool_call_id'."], "feat": ["Add a default starting time and a duration in the logging time form", "Dynamically retrieve tickets"]}, "releaseTime": "2025-04-16 13:10:00"}, {"version": "0.0.2", "releaseNotes": {"fix": ["BIRI-10278: An assistant message with 'tool_calls' must be followed by tool messages responding to each 'tool_call_id'."], "feat": ["Add a /clear command to reset history", "Add a /version command to get the update info and version number", "Provide three possible tickets for user to choose when inferring from user's summary", "Add a default starting time in the logging time form", "Dynamically retrieve tickets", "Optimise the function to get worklog"]}, "releaseTime": "2025-04-11 10:20:00"}, {"version": "0.0.1", "releaseNotes": {"fix": ["BIRI-10278: An assistant message with 'tool_calls' must be followed by tool messages responding to each 'tool_call_id'."], "feat": ["Add a /version command", "Provide three possible tickets for user to choose when inferring from user's summary", "Add a default starting time in the logging time form"]}, "releaseTime": "2025-04-09 17:20:00"}]