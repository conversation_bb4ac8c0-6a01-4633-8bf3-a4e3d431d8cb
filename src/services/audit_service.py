"""
Audit service for comprehensive logging and tracking of user interactions.

This module provides services for logging user interactions, bot actions,
system events, and errors to the database for comprehensive auditing.
"""

import json
import traceback
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any, Dict, Optional

import pytz
from loguru import logger
from sqlalchemy.exc import SQLAlchemyError

from src.utils.sql.db_connection import DatabaseConnectionManager
from src.utils.sql.models import AuditEventType, AuditLog, AuditStatus, UserSession


class AuditService:
    """
    Service for managing audit logs and user session tracking.

    This service provides methods for logging various types of events
    and maintaining user session information for comprehensive auditing.
    """

    def __init__(self):
        """Initialize the audit service."""
        self.db_manager = DatabaseConnectionManager()
        self.sydney_tz = pytz.timezone('Australia/Sydney')

    def _get_sydney_now(self) -> datetime:
        """Get current Sydney time."""
        return datetime.now(self.sydney_tz)

    def _serialize_data(self, data: Any) -> Optional[str]:
        """
        Safely serialize data to JSON string.

        Args:
            data: Data to serialize

        Returns:
            JSON string or None if serialization fails
        """
        if data is None:
            return None

        try:
            return json.dumps(data, default=str, ensure_ascii=False)
        except (TypeError, ValueError) as e:
            logger.warning(f'Failed to serialize data for audit log: {e}')
            return str(data)

    async def log_event(
        self,
        event_type: AuditEventType,
        event_status: AuditStatus = AuditStatus.SUCCESS,
        user_id: Optional[str] = None,
        user_name: Optional[str] = None,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None,
        activity_id: Optional[str] = None,
        channel_id: Optional[str] = None,
        action_name: Optional[str] = None,
        endpoint: Optional[str] = None,
        method: Optional[str] = None,
        message_content: Optional[str] = None,
        parameters: Optional[Dict[str, Any]] = None,
        response_data: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None,
        stack_trace: Optional[str] = None,
        duration_ms: Optional[float] = None,
        event_id: Optional[str] = None,
    ) -> str:
        """
        Log an audit event to the database.

        Args:
            event_type: Type of event being logged
            event_status: Status of the event
            user_id: ID of the user involved
            user_name: Name of the user involved
            session_id: Session ID for tracking user sessions
            request_id: Request ID for tracking requests
            activity_id: Activity ID from bot framework
            channel_id: Channel ID where event occurred
            action_name: Name of action being executed
            endpoint: API endpoint being called
            method: HTTP method used
            message_content: Content of user message or bot response
            parameters: Parameters passed to action/endpoint
            response_data: Response data from action/endpoint
            error_message: Error message if event failed
            stack_trace: Full stack trace for errors
            duration_ms: Duration of operation in milliseconds
            event_id: Optional custom event ID (UUID will be generated if not provided)

        Returns:
            The event ID of the logged event
        """
        if event_id is None:
            event_id = str(uuid.uuid4())

        try:
            with self.db_manager.session_scope() as session:
                audit_log = AuditLog(
                    event_id=event_id,
                    event_type=event_type.value,
                    event_status=event_status.value,
                    user_id=user_id,
                    user_name=user_name,
                    session_id=session_id,
                    request_id=request_id,
                    activity_id=activity_id,
                    channel_id=channel_id,
                    action_name=action_name,
                    endpoint=endpoint,
                    method=method,
                    message_content=message_content,
                    parameters=self._serialize_data(parameters),
                    response_data=self._serialize_data(response_data),
                    error_message=error_message,
                    stack_trace=stack_trace,
                    duration_ms=duration_ms,
                    created_at=self._get_sydney_now(),
                )

                session.add(audit_log)
                # session.commit() is handled by session_scope context manager

                logger.debug(f'Logged audit event [id={event_id}] type={event_type.value} status={event_status.value}')
                return event_id

        except SQLAlchemyError as e:
            logger.error(f'Failed to log audit event [id={event_id}]: {e}')
            logger.debug(f'Audit log error traceback:\n{traceback.format_exc()}')
            raise

    async def log_user_message(
        self,
        user_id: str,
        user_name: str,
        message_content: str,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None,
        activity_id: Optional[str] = None,
        channel_id: Optional[str] = None,
    ) -> str:
        """
        Log a user message event.

        Args:
            user_id: ID of the user
            user_name: Name of the user
            message_content: Content of the user's message
            session_id: Session ID for tracking
            request_id: Request ID for tracking
            activity_id: Activity ID from bot framework
            channel_id: Channel ID where message was sent

        Returns:
            The event ID of the logged event
        """
        return await self.log_event(
            event_type=AuditEventType.USER_MESSAGE,
            event_status=AuditStatus.SUCCESS,
            user_id=user_id,
            user_name=user_name,
            session_id=session_id,
            request_id=request_id,
            activity_id=activity_id,
            channel_id=channel_id,
            message_content=message_content,
        )

    async def log_bot_response(
        self,
        user_id: str,
        user_name: str,
        response_content: str,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None,
        activity_id: Optional[str] = None,
        channel_id: Optional[str] = None,
        duration_ms: Optional[float] = None,
    ) -> str:
        """
        Log a bot response event.

        Args:
            user_id: ID of the user
            user_name: Name of the user
            response_content: Content of the bot's response
            session_id: Session ID for tracking
            request_id: Request ID for tracking
            activity_id: Activity ID from bot framework
            channel_id: Channel ID where response was sent
            duration_ms: Time taken to generate response

        Returns:
            The event ID of the logged event
        """
        return await self.log_event(
            event_type=AuditEventType.BOT_RESPONSE,
            event_status=AuditStatus.SUCCESS,
            user_id=user_id,
            user_name=user_name,
            session_id=session_id,
            request_id=request_id,
            activity_id=activity_id,
            channel_id=channel_id,
            message_content=response_content,
            duration_ms=duration_ms,
        )

    async def log_action_execution(
        self,
        action_name: str,
        user_id: str,
        user_name: str,
        parameters: Optional[Dict[str, Any]] = None,
        response_data: Optional[Dict[str, Any]] = None,
        event_status: AuditStatus = AuditStatus.SUCCESS,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None,
        activity_id: Optional[str] = None,
        channel_id: Optional[str] = None,
        duration_ms: Optional[float] = None,
        error_message: Optional[str] = None,
        stack_trace: Optional[str] = None,
    ) -> str:
        """
        Log an action execution event.

        Args:
            action_name: Name of the action being executed
            user_id: ID of the user
            user_name: Name of the user
            parameters: Parameters passed to the action
            response_data: Response data from the action
            event_status: Status of the action execution
            session_id: Session ID for tracking
            request_id: Request ID for tracking
            activity_id: Activity ID from bot framework
            channel_id: Channel ID where action was executed
            duration_ms: Time taken to execute action
            error_message: Error message if action failed
            stack_trace: Stack trace if action failed

        Returns:
            The event ID of the logged event
        """
        return await self.log_event(
            event_type=AuditEventType.ACTION_EXECUTION,
            event_status=event_status,
            user_id=user_id,
            user_name=user_name,
            session_id=session_id,
            request_id=request_id,
            activity_id=activity_id,
            channel_id=channel_id,
            action_name=action_name,
            parameters=parameters,
            response_data=response_data,
            duration_ms=duration_ms,
            error_message=error_message,
            stack_trace=stack_trace,
        )

    async def log_authentication_event(
        self,
        user_id: str,
        user_name: str,
        event_status: AuditStatus,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None,
        activity_id: Optional[str] = None,
        channel_id: Optional[str] = None,
        error_message: Optional[str] = None,
        parameters: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Log an authentication event.

        Args:
            user_id: ID of the user
            user_name: Name of the user
            event_status: Status of the authentication
            session_id: Session ID for tracking
            request_id: Request ID for tracking
            activity_id: Activity ID from bot framework
            channel_id: Channel ID where authentication occurred
            error_message: Error message if authentication failed
            parameters: Additional authentication parameters

        Returns:
            The event ID of the logged event
        """
        return await self.log_event(
            event_type=AuditEventType.AUTHENTICATION,
            event_status=event_status,
            user_id=user_id,
            user_name=user_name,
            session_id=session_id,
            request_id=request_id,
            activity_id=activity_id,
            channel_id=channel_id,
            error_message=error_message,
            parameters=parameters,
        )

    async def log_error_event(
        self,
        error_message: str,
        stack_trace: str,
        user_id: Optional[str] = None,
        user_name: Optional[str] = None,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None,
        activity_id: Optional[str] = None,
        channel_id: Optional[str] = None,
        action_name: Optional[str] = None,
        endpoint: Optional[str] = None,
        parameters: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Log an error event.

        Args:
            error_message: Error message
            stack_trace: Full stack trace
            user_id: ID of the user (if applicable)
            user_name: Name of the user (if applicable)
            session_id: Session ID for tracking
            request_id: Request ID for tracking
            activity_id: Activity ID from bot framework
            channel_id: Channel ID where error occurred
            action_name: Name of action that caused error
            endpoint: Endpoint that caused error
            parameters: Parameters that caused error

        Returns:
            The event ID of the logged event
        """
        return await self.log_event(
            event_type=AuditEventType.ERROR,
            event_status=AuditStatus.FAILURE,
            user_id=user_id,
            user_name=user_name,
            session_id=session_id,
            request_id=request_id,
            activity_id=activity_id,
            channel_id=channel_id,
            action_name=action_name,
            endpoint=endpoint,
            error_message=error_message,
            stack_trace=stack_trace,
            parameters=parameters,
        )

    async def log_api_call(
        self,
        endpoint: str,
        method: str,
        event_status: AuditStatus,
        user_id: Optional[str] = None,
        user_name: Optional[str] = None,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None,
        parameters: Optional[Dict[str, Any]] = None,
        response_data: Optional[Dict[str, Any]] = None,
        duration_ms: Optional[float] = None,
        error_message: Optional[str] = None,
    ) -> str:
        """
        Log an API call event.

        Args:
            endpoint: API endpoint being called
            method: HTTP method used
            event_status: Status of the API call
            user_id: ID of the user (if applicable)
            user_name: Name of the user (if applicable)
            session_id: Session ID for tracking
            request_id: Request ID for tracking
            parameters: Parameters sent to API
            response_data: Response data from API
            duration_ms: Time taken for API call
            error_message: Error message if API call failed

        Returns:
            The event ID of the logged event
        """
        return await self.log_event(
            event_type=AuditEventType.API_CALL,
            event_status=event_status,
            user_id=user_id,
            user_name=user_name,
            session_id=session_id,
            request_id=request_id,
            endpoint=endpoint,
            method=method,
            parameters=parameters,
            response_data=response_data,
            duration_ms=duration_ms,
            error_message=error_message,
        )

    async def log_system_event(
        self,
        event_description: str,
        event_status: AuditStatus = AuditStatus.SUCCESS,
        parameters: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None,
    ) -> str:
        """
        Log a system event.

        Args:
            event_description: Description of the system event
            event_status: Status of the system event
            parameters: Additional parameters
            error_message: Error message if event failed

        Returns:
            The event ID of the logged event
        """
        return await self.log_event(
            event_type=AuditEventType.SYSTEM_EVENT,
            event_status=event_status,
            message_content=event_description,
            parameters=parameters,
            error_message=error_message,
        )


class SessionService:
    """
    Service for managing user sessions and interaction tracking.

    This service provides methods for creating, updating, and tracking
    user sessions to understand conversation flows and user engagement.
    """

    def __init__(self):
        """Initialize the session service."""
        self.db_manager = DatabaseConnectionManager()
        self.sydney_tz = pytz.timezone('Australia/Sydney')

    def _get_sydney_now(self) -> datetime:
        """Get current Sydney time."""
        return datetime.now(self.sydney_tz)

    def _ensure_timezone_aware(self, dt: Any) -> Optional[datetime]:
        """
        Ensure a datetime object is timezone-aware (Sydney timezone).

        Args:
            dt: Datetime object that may be timezone-naive or timezone-aware

        Returns:
            Timezone-aware datetime object in Sydney timezone
        """
        if dt is None:
            return None

        if dt.tzinfo is None:
            # Assume naive datetime is in Sydney timezone
            return self.sydney_tz.localize(dt)
        else:
            # Convert to Sydney timezone if it's already timezone-aware
            return dt.astimezone(self.sydney_tz)

    async def get_or_create_session(
        self,
        user_id: str,
        user_name: Optional[str] = None,
        channel_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
    ) -> str:
        """
        Get existing session or create a new one for the user.

        Args:
            user_id: ID of the user
            user_name: Name of the user
            channel_id: Channel ID where interaction occurred
            conversation_id: Conversation ID from bot framework

        Returns:
            Session ID
        """
        try:
            with self.db_manager.session_scope() as session:
                # Look for an active session (within last 30 minutes)
                cutoff_time = self._get_sydney_now() - timedelta(minutes=30)

                existing_session = (
                    session.query(UserSession)
                    .filter(
                        UserSession.user_id == user_id,
                        UserSession.last_activity_at > cutoff_time,
                    )
                    .order_by(UserSession.last_activity_at.desc())
                    .first()
                )

                if existing_session:
                    # Update last activity time
                    existing_session.last_activity_at = self._get_sydney_now()

                    # Update session duration - ensure both datetimes are timezone-aware
                    first_activity_aware = self._ensure_timezone_aware(existing_session.first_activity_at)
                    last_activity_aware = self._ensure_timezone_aware(existing_session.last_activity_at)
                    if first_activity_aware and last_activity_aware:
                        duration = (last_activity_aware - first_activity_aware).total_seconds() / 60
                        existing_session.session_duration_minutes = duration

                    logger.debug(f'Updated existing session {existing_session.session_id} for user {user_id}')
                    return existing_session.session_id

                # Create new session
                session_id = str(uuid.uuid4())
                now = self._get_sydney_now()

                new_session = UserSession(
                    session_id=session_id,
                    user_id=user_id,
                    user_name=user_name,
                    channel_id=channel_id,
                    conversation_id=conversation_id,
                    message_count=0,
                    action_count=0,
                    error_count=0,
                    first_activity_at=now,
                    last_activity_at=now,
                    created_at=now,
                )

                session.add(new_session)
                logger.debug(f'Created new session {session_id} for user {user_name} with id {user_id}')
                return session_id

        except SQLAlchemyError as e:
            logger.error(f'Failed to get or create session for user {user_id}: {e}')
            logger.debug(f'Session error traceback:\n{traceback.format_exc()}')
            # Return a temporary session ID if database fails
            return str(uuid.uuid4())

    async def update_session_metrics(
        self,
        session_id: str,
        message_increment: int = 0,
        action_increment: int = 0,
        error_increment: int = 0,
    ) -> None:
        """
        Update session metrics.

        Args:
            session_id: Session ID to update
            message_increment: Number of messages to add to count
            action_increment: Number of actions to add to count
            error_increment: Number of errors to add to count
        """
        try:
            with self.db_manager.session_scope() as session:
                user_session = session.query(UserSession).filter(UserSession.session_id == session_id).first()

                if user_session:
                    user_session.message_count += message_increment
                    user_session.action_count += action_increment
                    user_session.error_count += error_increment
                    user_session.last_activity_at = self._get_sydney_now()

                    # Update session duration - ensure both datetimes are timezone-aware
                    first_activity_aware = self._ensure_timezone_aware(user_session.first_activity_at)
                    last_activity_aware = self._ensure_timezone_aware(user_session.last_activity_at)
                    if first_activity_aware and last_activity_aware:
                        duration = (last_activity_aware - first_activity_aware).total_seconds() / 60
                        user_session.session_duration_minutes = duration

                    logger.debug(f'Updated session metrics for {session_id}')
                else:
                    logger.warning(f'Session {session_id} not found for metrics update')

        except SQLAlchemyError as e:
            logger.error(f'Failed to update session metrics for {session_id}: {e}')
            logger.debug(f'Session metrics error traceback:\n{traceback.format_exc()}')

    async def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get session information.

        Args:
            session_id: Session ID to retrieve

        Returns:
            Dictionary with session information or None if not found
        """
        try:
            with self.db_manager.session_scope() as session:
                user_session = session.query(UserSession).filter(UserSession.session_id == session_id).first()

                if user_session:
                    return {
                        'session_id': user_session.session_id,
                        'user_id': user_session.user_id,
                        'user_name': user_session.user_name,
                        'channel_id': user_session.channel_id,
                        'conversation_id': user_session.conversation_id,
                        'message_count': user_session.message_count,
                        'action_count': user_session.action_count,
                        'error_count': user_session.error_count,
                        'first_activity_at': user_session.first_activity_at,
                        'last_activity_at': user_session.last_activity_at,
                        'session_duration_minutes': user_session.session_duration_minutes,
                        'created_at': user_session.created_at,
                    }

                return None

        except SQLAlchemyError as e:
            logger.error(f'Failed to get session info for {session_id}: {e}')
            logger.debug(f'Session info error traceback:\n{traceback.format_exc()}')
            return None
