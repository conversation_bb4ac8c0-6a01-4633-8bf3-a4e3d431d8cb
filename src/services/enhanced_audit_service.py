"""
Enhanced audit service with privacy controls, data classification, and retention policies.

This module extends the base audit service with advanced features including:
- Text content sanitization and PII detection
- Configurable privacy controls and data retention
- Performance monitoring and alerting
- GDPR compliance features
"""

import time
import traceback
import uuid
from datetime import timed<PERSON><PERSON>
from enum import Enum
from typing import Any, Dict, Optional

import pytz
from loguru import logger

from src.services.audit_service import AuditService
from src.utils.sql.models import AuditEventType, AuditLog, AuditStatus, UserSession
from src.utils.text_sanitizer import (
    ContentType,
    SensitivityLevel,
    TextContentSanitizer,
    get_default_sanitizer,
)


class RetentionPolicy(Enum):
    """Data retention policies based on sensitivity and type."""

    IMMEDIATE = 'immediate'  # Delete immediately after processing
    SHORT_TERM = 'short_term'  # 30 days
    MEDIUM_TERM = 'medium_term'  # 90 days
    LONG_TERM = 'long_term'  # 1 year
    PERMANENT = 'permanent'  # Keep indefinitely


class PrivacyConfig:
    """Configuration for privacy controls and data handling."""

    def __init__(self):
        # Default sanitization settings
        self.enable_sanitization = True
        self.default_sensitivity_level = SensitivityLevel.INTERNAL
        self.sanitize_user_messages = True
        self.sanitize_bot_responses = True
        self.sanitize_action_parameters = True
        self.sanitize_api_requests = True

        # Retention policies by data type and sensitivity
        self.retention_policies = {
            (AuditEventType.USER_MESSAGE, SensitivityLevel.PUBLIC): RetentionPolicy.LONG_TERM,
            (AuditEventType.USER_MESSAGE, SensitivityLevel.INTERNAL): RetentionPolicy.MEDIUM_TERM,
            (AuditEventType.USER_MESSAGE, SensitivityLevel.CONFIDENTIAL): RetentionPolicy.SHORT_TERM,
            (AuditEventType.USER_MESSAGE, SensitivityLevel.RESTRICTED): RetentionPolicy.IMMEDIATE,
            (AuditEventType.BOT_RESPONSE, SensitivityLevel.PUBLIC): RetentionPolicy.LONG_TERM,
            (AuditEventType.BOT_RESPONSE, SensitivityLevel.INTERNAL): RetentionPolicy.MEDIUM_TERM,
            (AuditEventType.BOT_RESPONSE, SensitivityLevel.CONFIDENTIAL): RetentionPolicy.SHORT_TERM,
            (AuditEventType.BOT_RESPONSE, SensitivityLevel.RESTRICTED): RetentionPolicy.IMMEDIATE,
            (AuditEventType.ACTION_EXECUTION, SensitivityLevel.PUBLIC): RetentionPolicy.LONG_TERM,
            (AuditEventType.ACTION_EXECUTION, SensitivityLevel.INTERNAL): RetentionPolicy.MEDIUM_TERM,
            (AuditEventType.ACTION_EXECUTION, SensitivityLevel.CONFIDENTIAL): RetentionPolicy.SHORT_TERM,
            (AuditEventType.ACTION_EXECUTION, SensitivityLevel.RESTRICTED): RetentionPolicy.IMMEDIATE,
            (AuditEventType.ERROR, SensitivityLevel.PUBLIC): RetentionPolicy.MEDIUM_TERM,
            (AuditEventType.ERROR, SensitivityLevel.INTERNAL): RetentionPolicy.MEDIUM_TERM,
            (AuditEventType.ERROR, SensitivityLevel.CONFIDENTIAL): RetentionPolicy.SHORT_TERM,
            (AuditEventType.ERROR, SensitivityLevel.RESTRICTED): RetentionPolicy.SHORT_TERM,
            (AuditEventType.SYSTEM_EVENT, SensitivityLevel.PUBLIC): RetentionPolicy.PERMANENT,
            (AuditEventType.SYSTEM_EVENT, SensitivityLevel.INTERNAL): RetentionPolicy.LONG_TERM,
            (AuditEventType.SYSTEM_EVENT, SensitivityLevel.CONFIDENTIAL): RetentionPolicy.MEDIUM_TERM,
            (AuditEventType.SYSTEM_EVENT, SensitivityLevel.RESTRICTED): RetentionPolicy.SHORT_TERM,
        }

        # Retention periods in days
        self.retention_periods = {
            RetentionPolicy.IMMEDIATE: 0,
            RetentionPolicy.SHORT_TERM: 30,
            RetentionPolicy.MEDIUM_TERM: 90,
            RetentionPolicy.LONG_TERM: 365,
            RetentionPolicy.PERMANENT: -1,  # Never delete
        }

        # PII handling settings
        self.mask_pii_in_logs = True
        self.hash_sensitive_data = True
        self.enable_gdpr_compliance = True

        # Performance settings
        self.enable_performance_monitoring = True
        self.performance_alert_threshold_ms = 5000  # Alert if operations take > 5 seconds
        self.batch_size_for_cleanup = 1000


class DataClassifier:
    """Classifies data based on content and context for appropriate handling."""

    def __init__(self, sanitizer: Optional[TextContentSanitizer] = None):
        self.sanitizer = sanitizer or get_default_sanitizer()
        self.classification_cache = {}
        self.cache_ttl = 3600  # 1 hour cache TTL

    def classify_content(
        self, content: str, content_type: ContentType, context: Optional[Dict[str, Any]] = None
    ) -> SensitivityLevel:
        """Classify content sensitivity with caching."""
        if not content:
            return SensitivityLevel.PUBLIC

        # Create cache key
        cache_key = f'{hash(content)}_{content_type.value}_{hash(str(context))}'

        # Check cache
        if cache_key in self.classification_cache:
            cached_result, timestamp = self.classification_cache[cache_key]
            if time.time() - timestamp < self.cache_ttl:
                return cached_result

        # Classify content
        sensitivity = self.sanitizer.classify_sensitivity(content, content_type)

        # Apply context-based adjustments
        if context:
            # Increase sensitivity for error messages
            if content_type == ContentType.ERROR_MESSAGE:
                if sensitivity == SensitivityLevel.PUBLIC:
                    sensitivity = SensitivityLevel.INTERNAL

            # Increase sensitivity for admin users
            if context.get('user_role') == 'admin':
                if sensitivity == SensitivityLevel.PUBLIC:
                    sensitivity = SensitivityLevel.INTERNAL

            # Increase sensitivity for production environment
            if context.get('environment') == 'production':
                if sensitivity == SensitivityLevel.PUBLIC:
                    sensitivity = SensitivityLevel.INTERNAL

        # Cache result
        self.classification_cache[cache_key] = (sensitivity, time.time())

        return sensitivity


class PerformanceMonitor:
    """Monitor audit service performance and generate alerts."""

    def __init__(self, privacy_config: PrivacyConfig):
        self.config = privacy_config
        self.metrics = {
            'total_operations': 0,
            'total_duration_ms': 0.0,
            'sanitization_operations': 0,
            'sanitization_duration_ms': 0.0,
            'database_operations': 0,
            'database_duration_ms': 0.0,
            'errors': 0,
            'alerts_generated': 0,
        }
        self.operation_history = []
        self.max_history_size = 1000

    def track_operation(
        self, operation_type: str, duration_ms: float, success: bool = True, metadata: Optional[Dict] = None
    ):
        """Track operation performance."""
        self.metrics['total_operations'] += 1
        self.metrics['total_duration_ms'] += duration_ms

        if operation_type == 'sanitization':
            self.metrics['sanitization_operations'] += 1
            self.metrics['sanitization_duration_ms'] += duration_ms
        elif operation_type == 'database':
            self.metrics['database_operations'] += 1
            self.metrics['database_duration_ms'] += duration_ms

        if not success:
            self.metrics['errors'] += 1

        # Track operation history
        operation_record = {
            'timestamp': time.time(),
            'operation_type': operation_type,
            'duration_ms': duration_ms,
            'success': success,
            'metadata': metadata or {},
        }

        self.operation_history.append(operation_record)

        # Maintain history size
        if len(self.operation_history) > self.max_history_size:
            self.operation_history = self.operation_history[-self.max_history_size :]

        # Check for performance alerts
        if self.config.enable_performance_monitoring and duration_ms > self.config.performance_alert_threshold_ms:
            self._generate_performance_alert(operation_type, duration_ms, metadata)

    def _generate_performance_alert(self, operation_type: str, duration_ms: float, metadata: Optional[Dict]):
        """Generate performance alert for slow operations."""
        self.metrics['alerts_generated'] += 1

        logger.warning(
            f'PERFORMANCE ALERT: {operation_type} operation took {duration_ms:.2f}ms '
            f'(threshold: {self.config.performance_alert_threshold_ms}ms). '
            f'Metadata: {metadata}'
        )

    def get_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics."""
        avg_duration = (
            self.metrics['total_duration_ms'] / self.metrics['total_operations']
            if self.metrics['total_operations'] > 0
            else 0
        )

        return {
            **self.metrics,
            'average_duration_ms': avg_duration,
            'error_rate': (
                self.metrics['errors'] / self.metrics['total_operations'] if self.metrics['total_operations'] > 0 else 0
            ),
            'recent_operations': self.operation_history[-10:],  # Last 10 operations
        }


class EnhancedAuditService(AuditService):
    """Extended audit service with privacy controls and data classification."""

    def __init__(self, privacy_config: Optional[PrivacyConfig] = None):
        super().__init__()
        self.privacy_config = privacy_config or PrivacyConfig()
        self.sanitizer = get_default_sanitizer()
        self.data_classifier = DataClassifier(self.sanitizer)
        self.performance_monitor = PerformanceMonitor(self.privacy_config)
        self.sydney_tz = pytz.timezone('Australia/Sydney')

        # Track sanitization metadata
        self.sanitization_metadata = {}

    def _get_content_type_for_event(self, event_type: AuditEventType) -> ContentType:
        """Map audit event type to content type for sanitization."""
        mapping = {
            AuditEventType.USER_MESSAGE: ContentType.USER_MESSAGE,
            AuditEventType.BOT_RESPONSE: ContentType.BOT_RESPONSE,
            AuditEventType.ACTION_EXECUTION: ContentType.ACTION_PARAMETERS,
            AuditEventType.API_CALL: ContentType.API_REQUEST,
            AuditEventType.ERROR: ContentType.ERROR_MESSAGE,
            AuditEventType.SYSTEM_EVENT: ContentType.SYSTEM_LOG,
        }
        return mapping.get(event_type, ContentType.SYSTEM_LOG)

    def _sanitize_content_if_enabled(
        self, content: Optional[str], event_type: AuditEventType, context: Optional[Dict] = None
    ) -> tuple[Optional[str], Optional[Dict]]:
        """Sanitize content if sanitization is enabled."""
        if not content or not self.privacy_config.enable_sanitization:
            return content, None

        start_time = time.time()

        try:
            content_type = self._get_content_type_for_event(event_type)
            sanitized = self.sanitizer.sanitize_content(content, content_type)

            duration_ms = (time.time() - start_time) * 1000
            self.performance_monitor.track_operation(
                'sanitization',
                duration_ms,
                True,
                {'content_length': len(content), 'patterns_detected': len(sanitized.detected_patterns)},
            )

            # Store sanitization metadata
            sanitization_metadata = sanitized.to_dict()

            return sanitized.sanitized_content, sanitization_metadata

        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            self.performance_monitor.track_operation('sanitization', duration_ms, False, {'error': str(e)})
            logger.error(f'Content sanitization failed: {e}')
            return content, None

    def _sanitize_parameters_if_enabled(
        self, parameters: Optional[Dict[str, Any]]
    ) -> tuple[Optional[Dict[str, Any]], Optional[Dict]]:
        """Sanitize parameters dictionary if sanitization is enabled."""
        if not parameters or not self.privacy_config.enable_sanitization:
            return parameters, None

        start_time = time.time()

        try:
            sanitized_params, sanitization_log = self.sanitizer.sanitize_dict(parameters, ContentType.ACTION_PARAMETERS)

            duration_ms = (time.time() - start_time) * 1000
            self.performance_monitor.track_operation(
                'sanitization',
                duration_ms,
                True,
                {'param_count': len(parameters), 'sanitized_keys': len(sanitization_log)},
            )

            sanitization_metadata = {
                'sanitized_keys': sanitization_log,
                'original_key_count': len(parameters),
                'sanitized_key_count': len(sanitization_log),
            }

            return sanitized_params, sanitization_metadata

        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            self.performance_monitor.track_operation('sanitization', duration_ms, False, {'error': str(e)})
            logger.error(f'Parameter sanitization failed: {e}')
            return parameters, None

    async def log_event_enhanced(
        self,
        event_type: AuditEventType,
        event_status: AuditStatus = AuditStatus.SUCCESS,
        user_id: Optional[str] = None,
        user_name: Optional[str] = None,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None,
        activity_id: Optional[str] = None,
        channel_id: Optional[str] = None,
        action_name: Optional[str] = None,
        endpoint: Optional[str] = None,
        method: Optional[str] = None,
        message_content: Optional[str] = None,
        parameters: Optional[Dict[str, Any]] = None,
        response_data: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None,
        stack_trace: Optional[str] = None,
        duration_ms: Optional[float] = None,
        event_id: Optional[str] = None,
        sensitivity_level: Optional[SensitivityLevel] = None,
        context: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Enhanced log event with sanitization and privacy controls.
        """
        if event_id is None:
            event_id = str(uuid.uuid4())

        start_time = time.time()

        try:
            # Sanitize content based on privacy configuration
            sanitized_message_content = message_content
            sanitized_parameters = parameters
            sanitized_response_data = response_data
            sanitized_error_message = error_message

            sanitization_metadata = {}

            # Sanitize message content
            if message_content:
                sanitized_message_content, msg_metadata = self._sanitize_content_if_enabled(
                    message_content, event_type, context
                )
                if msg_metadata:
                    sanitization_metadata['message_content'] = msg_metadata

            # Sanitize parameters
            if parameters:
                sanitized_parameters, param_metadata = self._sanitize_parameters_if_enabled(parameters)
                if param_metadata:
                    sanitization_metadata['parameters'] = param_metadata

            # Sanitize response data
            if response_data:
                sanitized_response_data, resp_metadata = self._sanitize_parameters_if_enabled(response_data)
                if resp_metadata:
                    sanitization_metadata['response_data'] = resp_metadata

            # Sanitize error message
            if error_message:
                sanitized_error_message, err_metadata = self._sanitize_content_if_enabled(
                    error_message, AuditEventType.ERROR, context
                )
                if err_metadata:
                    sanitization_metadata['error_message'] = err_metadata

            # Store sanitization metadata for audit trail
            if sanitization_metadata:
                self.sanitization_metadata[event_id] = sanitization_metadata

            # Use the parent class method for actual database logging
            db_start_time = time.time()

            result_event_id = await super().log_event(
                event_type=event_type,
                event_status=event_status,
                user_id=user_id,
                user_name=user_name,
                session_id=session_id,
                request_id=request_id,
                activity_id=activity_id,
                channel_id=channel_id,
                action_name=action_name,
                endpoint=endpoint,
                method=method,
                message_content=sanitized_message_content,
                parameters=sanitized_parameters,
                response_data=sanitized_response_data,
                error_message=sanitized_error_message,
                stack_trace=stack_trace,
                duration_ms=duration_ms,
                event_id=event_id,
            )

            db_duration_ms = (time.time() - db_start_time) * 1000
            self.performance_monitor.track_operation('database', db_duration_ms, True)

            total_duration_ms = (time.time() - start_time) * 1000

            # Log enhanced audit activity
            logger.debug(
                f'Enhanced audit event logged [id={event_id}] '
                f'type={event_type.value} status={event_status.value} '
                f'sanitization_applied={bool(sanitization_metadata)} '
                f'duration={total_duration_ms:.3f}ms'
            )

            return result_event_id

        except Exception as e:
            total_duration_ms = (time.time() - start_time) * 1000
            self.performance_monitor.track_operation('database', total_duration_ms, False, {'error': str(e)})
            logger.error(f'Enhanced audit logging failed [id={event_id}]: {e}')
            raise

    async def log_user_message_enhanced(
        self,
        user_id: str,
        user_name: str,
        message_content: str,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None,
        activity_id: Optional[str] = None,
        channel_id: Optional[str] = None,
        sensitivity_level: Optional[SensitivityLevel] = None,
        context: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Log user message with enhanced privacy controls."""
        return await self.log_event_enhanced(
            event_type=AuditEventType.USER_MESSAGE,
            event_status=AuditStatus.SUCCESS,
            user_id=user_id,
            user_name=user_name,
            session_id=session_id,
            request_id=request_id,
            activity_id=activity_id,
            channel_id=channel_id,
            message_content=message_content,
            sensitivity_level=sensitivity_level,
            context=context,
        )

    async def log_bot_response_enhanced(
        self,
        user_id: str,
        user_name: str,
        response_content: str,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None,
        activity_id: Optional[str] = None,
        channel_id: Optional[str] = None,
        duration_ms: Optional[float] = None,
        sensitivity_level: Optional[SensitivityLevel] = None,
        context: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Log bot response with enhanced privacy controls."""
        return await self.log_event_enhanced(
            event_type=AuditEventType.BOT_RESPONSE,
            event_status=AuditStatus.SUCCESS,
            user_id=user_id,
            user_name=user_name,
            session_id=session_id,
            request_id=request_id,
            activity_id=activity_id,
            channel_id=channel_id,
            message_content=response_content,
            duration_ms=duration_ms,
            sensitivity_level=sensitivity_level,
            context=context,
        )

    async def log_action_execution_enhanced(
        self,
        action_name: str,
        user_id: str,
        user_name: str,
        parameters: Optional[Dict[str, Any]] = None,
        response_data: Optional[Dict[str, Any]] = None,
        event_status: AuditStatus = AuditStatus.SUCCESS,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None,
        activity_id: Optional[str] = None,
        channel_id: Optional[str] = None,
        duration_ms: Optional[float] = None,
        error_message: Optional[str] = None,
        stack_trace: Optional[str] = None,
        sensitivity_level: Optional[SensitivityLevel] = None,
        context: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Log action execution with enhanced privacy controls."""
        return await self.log_event_enhanced(
            event_type=AuditEventType.ACTION_EXECUTION,
            event_status=event_status,
            user_id=user_id,
            user_name=user_name,
            session_id=session_id,
            request_id=request_id,
            activity_id=activity_id,
            channel_id=channel_id,
            action_name=action_name,
            parameters=parameters,
            response_data=response_data,
            duration_ms=duration_ms,
            error_message=error_message,
            stack_trace=stack_trace,
            sensitivity_level=sensitivity_level,
            context=context,
        )

    async def cleanup_expired_data(self) -> Dict[str, Any]:
        """Clean up expired audit data based on retention policies."""
        if not self.privacy_config.enable_gdpr_compliance:
            return {'total_deleted': 0, 'deleted_by_policy': {}, 'errors': 0}

        start_time = time.time()
        cleanup_stats = {
            'total_deleted': 0,
            'deleted_by_policy': {},
            'errors': 0,
        }

        try:
            with self.db_manager.session_scope() as session:
                current_time = self._get_sydney_now()

                for (event_type, sensitivity_level), retention_policy in self.privacy_config.retention_policies.items():
                    retention_days = self.privacy_config.retention_periods[retention_policy]

                    if retention_days == -1:  # Permanent retention
                        continue

                    if retention_days == 0:  # Immediate deletion
                        # For immediate deletion, we should have handled this during logging
                        continue

                    # Calculate cutoff date
                    cutoff_date = current_time - timedelta(days=retention_days)

                    # Query and delete expired records
                    deleted_count = (
                        session.query(AuditLog)
                        .filter(AuditLog.event_type == event_type.value, AuditLog.created_at < cutoff_date)
                        .delete(synchronize_session=False)
                    )

                    if deleted_count > 0:
                        policy_key = f'{event_type.value}_{sensitivity_level.value}'
                        cleanup_stats['deleted_by_policy'][policy_key] = deleted_count
                        cleanup_stats['total_deleted'] += deleted_count

                        logger.info(
                            f'Cleaned up {deleted_count} expired audit records: '
                            f'type={event_type.value}, sensitivity={sensitivity_level.value}, '
                            f'retention={retention_days} days'
                        )

                # Commit all deletions
                session.commit()

        except Exception as e:
            cleanup_stats['errors'] += 1
            logger.error(f'Data cleanup failed: {e}')
            logger.debug(f'Cleanup error traceback:\n{traceback.format_exc()}')

        duration_ms = (time.time() - start_time) * 1000
        self.performance_monitor.track_operation('cleanup', duration_ms, cleanup_stats['errors'] == 0, cleanup_stats)

        logger.info(f'Data cleanup completed: {cleanup_stats}')
        return cleanup_stats

    def get_sanitization_metadata(self, event_id: str) -> Optional[Dict]:
        """Get sanitization metadata for a specific event."""
        return self.sanitization_metadata.get(event_id)

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics."""
        return self.performance_monitor.get_metrics()

    async def export_user_data(self, user_id: str) -> Dict[str, Any]:
        """Export all data for a specific user (GDPR compliance)."""
        start_time = time.time()

        try:
            with self.db_manager.session_scope() as session:
                # Get all audit logs for the user
                audit_logs = session.query(AuditLog).filter(AuditLog.user_id == user_id).all()

                # Get user sessions
                user_sessions = session.query(UserSession).filter(UserSession.user_id == user_id).all()

                # Format data for export
                export_data = {
                    'user_id': user_id,
                    'export_timestamp': self._get_sydney_now().isoformat(),
                    'audit_logs': [
                        {
                            'event_id': log.event_id,
                            'event_type': log.event_type,
                            'event_status': log.event_status,
                            'created_at': log.created_at.isoformat() if log.created_at is not None else None,
                            'action_name': log.action_name,
                            'message_content': log.message_content,
                            'duration_ms': log.duration_ms,
                        }
                        for log in audit_logs
                    ],
                    'user_sessions': [
                        {
                            'session_id': user_session.session_id,
                            'message_count': user_session.message_count,
                            'action_count': user_session.action_count,
                            'error_count': user_session.error_count,
                            'first_activity_at': user_session.first_activity_at.isoformat()
                            if user_session.first_activity_at is not None
                            else None,
                            'last_activity_at': user_session.last_activity_at.isoformat()
                            if user_session.last_activity_at is not None
                            else None,
                            'session_duration_minutes': user_session.session_duration_minutes,
                        }
                        for user_session in user_sessions
                    ],
                    'total_audit_logs': len(audit_logs),
                    'total_sessions': len(user_sessions),
                }

                duration_ms = (time.time() - start_time) * 1000
                self.performance_monitor.track_operation(
                    'export',
                    duration_ms,
                    True,
                    {'user_id': user_id, 'audit_logs': len(audit_logs), 'sessions': len(user_sessions)},
                )

                logger.info(f'User data exported for {user_id}: {len(audit_logs)} logs, {len(user_sessions)} sessions')
                return export_data

        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            self.performance_monitor.track_operation('export', duration_ms, False, {'error': str(e)})
            logger.error(f'User data export failed for {user_id}: {e}')
            raise

    async def delete_user_data(self, user_id: str) -> Dict[str, int]:
        """Delete all data for a specific user (GDPR right to be forgotten)."""
        start_time = time.time()
        deletion_stats = {'audit_logs_deleted': 0, 'sessions_deleted': 0, 'errors': 0}

        try:
            with self.db_manager.session_scope() as session:
                # Delete audit logs
                audit_deleted = (
                    session.query(AuditLog).filter(AuditLog.user_id == user_id).delete(synchronize_session=False)
                )

                # Delete user sessions
                sessions_deleted = (
                    session.query(UserSession).filter(UserSession.user_id == user_id).delete(synchronize_session=False)
                )

                deletion_stats['audit_logs_deleted'] = audit_deleted
                deletion_stats['sessions_deleted'] = sessions_deleted

                # Remove sanitization metadata for this user
                events_to_remove = [
                    event_id
                    for event_id, metadata in self.sanitization_metadata.items()
                    if metadata.get('user_id') == user_id
                ]
                for event_id in events_to_remove:
                    del self.sanitization_metadata[event_id]

                session.commit()

                duration_ms = (time.time() - start_time) * 1000
                self.performance_monitor.track_operation('deletion', duration_ms, True, deletion_stats)

                logger.info(f'User data deleted for {user_id}: {deletion_stats}')
                return deletion_stats

        except Exception as e:
            deletion_stats['errors'] += 1
            duration_ms = (time.time() - start_time) * 1000
            self.performance_monitor.track_operation('deletion', duration_ms, False, {'error': str(e)})
            logger.error(f'User data deletion failed for {user_id}: {e}')
            raise


# Global enhanced audit service instance
_enhanced_audit_service = None


def get_enhanced_audit_service(privacy_config: Optional[PrivacyConfig] = None) -> EnhancedAuditService:
    """Get the global enhanced audit service instance."""
    global _enhanced_audit_service
    if _enhanced_audit_service is None:
        _enhanced_audit_service = EnhancedAuditService(privacy_config)
    return _enhanced_audit_service
