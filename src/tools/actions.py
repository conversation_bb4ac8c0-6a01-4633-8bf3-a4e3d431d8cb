"""Jira Action Functions Module

This module contains action functions for interacting with <PERSON><PERSON> from the bot framework.
These functions are designed to be used with the Teams AI ActionPlanner and handle
common Jira operations like creating tickets, adding comments, and updating statuses.

Each function follows a similar pattern of accepting context and state parameters,
and returning a confirmation message after performing the requested action.
"""

import time
import uuid

from atlassian import <PERSON>ra
from atlassian.errors import ApiError
from botbuilder.schema import Activity
from loguru import logger
from requests.exceptions import HTTPError, RequestException
from teams.ai.actions import ActionTurnContext

from config import Config
from src.models import Issue
from state import AppTurnState
from tools.cards import create_ticket_list_card, create_time_logging_card
from utils.audit_decorators import audit_action, audit_message_handler
from utils.decorators import with_jira_client
from utils.logging_config import LoggingContext


@audit_action("create_ticket")
@with_jira_client
async def create_ticket(
    context: ActionTurnContext,
    state: AppTurnState,
    jira: Jira = None,
):
    """
    Creates a new Jira ticket with the provided summary and description.

    Args:
        context: The turn context containing the activity and data from the request.
        state: The application state containing user information and tokens.

    Returns:
        str: A confirmation message with the created ticket key.

    The function expects the following in context.data:
        - summary: The title/summary of the ticket
        - description: The detailed description of the ticket
    """
    # Generate a unique operation ID for tracking this action
    action_id = str(uuid.uuid4())
    start_time = time.time()

    # Get user information if available
    user_id = 'unknown'
    user_name = 'unknown'
    if context.activity and context.activity.from_property:
        user_id = context.activity.from_property.id
        user_name = context.activity.from_property.name

    # Log the action start with context
    logger.info(f'ACTION CALLED: create_ticket [id={action_id}] by user {user_name}')
    logger.debug(
        f'Action parameters [id={action_id}]: summary={context.data.get("summary", "None")}, description_length={len(context.data.get("description", ""))}'
    )

    try:
        # Use LoggingContext to add structured context to all logs within this function
        with LoggingContext(action_id=action_id, function='create_ticket', user_id=user_id, user_name=user_name):
            # Validate required data
            if not context.data.get('summary'):
                logger.warning(f"Missing required field 'summary' in create_ticket [id={action_id}]")
                return 'Error: Ticket summary is required.'

            summary = context.data['summary']
            description = context.data.get('description', '')

            logger.info(f"Creating Jira ticket with summary: '{summary[:30]}...' [id={action_id}]")

            # Create the ticket
            issue = jira.issue_create(
                fields={
                    'project': {'key': Config.JIRA_PROJECT_KEY},
                    'summary': summary,
                    'description': description,
                    'issuetype': {'name': 'Task'},
                    'assignee': {'accountId': jira.myself()['accountId']},
                }
            )

            # Log success with timing information
            duration = time.time() - start_time
            logger.info(f'Successfully created ticket {issue["key"]} [id={action_id}] in {duration:.3f}s')

            return f'Ticket {issue["key"]} created.'
    except KeyError as e:
        duration = time.time() - start_time
        logger.error(f'Missing field in create_ticket [id={action_id}] after {duration:.3f}s: {e}')
        return f'Error creating ticket: Missing required field {e}'
    except (RequestException, HTTPError) as e:
        duration = time.time() - start_time
        logger.error(f'HTTP error in create_ticket [id={action_id}] after {duration:.3f}s: {e}')
        return 'Error creating ticket: Connection to Jira failed. Please try again later.'
    except ApiError as e:
        duration = time.time() - start_time
        logger.error(f'Jira API error in create_ticket [id={action_id}] after {duration:.3f}s: {e}')
        return f'Error creating ticket: {str(e)}'
    except Exception as e:
        duration = time.time() - start_time
        logger.error(f'Unexpected error in create_ticket [id={action_id}] after {duration:.3f}s: {e}')
        logger.exception(e)
        return 'An unexpected error occurred while creating the ticket. Please try again later.'


@audit_action("insert_comment")
@with_jira_client
async def insert_comment(
    context: ActionTurnContext,
    state: AppTurnState,
    jira: Jira = None,
):
    """
    Adds a comment to an existing Jira ticket.

    Args:
        context: The turn context containing the activity and data from the request.
        state: The application state containing user information and tokens.

    Returns:
        str: A confirmation message indicating the comment was added.

    The function expects the following in context.data:
        - key: The Jira ticket key (e.g., 'BIRI-1234')
        - comment: The comment text to add to the ticket
    """
    # Generate a unique operation ID for tracking this action
    action_id = str(uuid.uuid4())
    start_time = time.time()

    # Get user information if available
    user_id = 'unknown'
    user_name = 'unknown'
    if context.activity and context.activity.from_property:
        user_id = context.activity.from_property.id
        user_name = context.activity.from_property.name

    # Log the action start with context
    logger.info(f'ACTION CALLED: insert_comment [id={action_id}] by user {user_name}')

    try:
        # Use LoggingContext to add structured context to all logs within this function
        with LoggingContext(action_id=action_id, function='insert_comment', user_id=user_id, user_name=user_name):
            # Validate required data
            if not context.data.get('key'):
                logger.warning(f"Missing required field 'key' in insert_comment [id={action_id}]")
                return 'Error: Ticket key is required.'
            if not context.data.get('comment'):
                logger.warning(f"Missing required field 'comment' in insert_comment [id={action_id}]")
                return 'Error: Comment text is required.'

            ticket_key = context.data['key']
            comment = context.data['comment']
            mention = context.data.get('mention') or ''

            logger.debug(f'Adding comment to ticket {ticket_key} [id={action_id}]')
            logger.debug(f'Comment length: {len(comment)} characters [id={action_id}]')

            if mention:
                logger.debug(f'Including mention for account: {mention} [id={action_id}]')
                comment += f'\n\n[~accountid:{mention}]'

            # Add the comment to the ticket
            jira.issue_add_comment(ticket_key, comment)

            # Log success with timing information
            duration = time.time() - start_time
            logger.info(f'Successfully added comment to ticket {ticket_key} [id={action_id}] in {duration:.3f}s')

            return f'Comment added to {ticket_key}.'
    except KeyError as e:
        duration = time.time() - start_time
        logger.error(f'Missing field in insert_comment [id={action_id}] after {duration:.3f}s: {e}')
        return f'Error adding comment: Missing required field {e}'
    except (RequestException, HTTPError) as e:
        duration = time.time() - start_time
        logger.error(f'HTTP error in insert_comment [id={action_id}] after {duration:.3f}s: {e}')
        return 'Error adding comment: Connection to Jira failed. Please try again later.'
    except ApiError as e:
        duration = time.time() - start_time
        logger.error(f'Jira API error in insert_comment [id={action_id}] after {duration:.3f}s: {e}')
        return f'Error adding comment: {str(e)}'
    except Exception as e:
        duration = time.time() - start_time
        logger.error(f'Unexpected error in insert_comment [id={action_id}] after {duration:.3f}s: {e}')
        logger.exception(e)
        return 'An unexpected error occurred while adding the comment. Please try again later.'


@audit_action("update_ticket_status")
@with_jira_client
async def update_ticket_status(context: ActionTurnContext, state: AppTurnState, jira: Jira):
    """
    Updates the status of an existing Jira ticket.

    Args:
        context: The turn context containing the activity and data from the request.
        state: The application state containing user information and tokens.

    Returns:
        str: A confirmation message indicating the ticket status was updated.

    The function expects the following in context.data:
        - key: The Jira ticket key (e.g., 'BIRI-1234')
        - status: The new status to set for the ticket
    """
    # Generate a unique operation ID for tracking this action
    action_id = str(uuid.uuid4())
    start_time = time.time()

    # Get user information if available
    user_id = 'unknown'
    user_name = 'unknown'
    if context.activity and context.activity.from_property:
        user_id = context.activity.from_property.id
        user_name = context.activity.from_property.name

    # Log the action start with context
    logger.info(f'ACTION CALLED: update_ticket_status [id={action_id}] by user {user_name}')

    try:
        # Use LoggingContext to add structured context to all logs within this function
        with LoggingContext(action_id=action_id, function='update_ticket_status', user_id=user_id, user_name=user_name):
            # Validate required data
            if not context.data.get('key'):
                logger.warning(f"Missing required field 'key' in update_ticket_status [id={action_id}]")
                return 'Error: Ticket key is required.'
            if not context.data.get('status'):
                logger.warning(f"Missing required field 'status' in update_ticket_status [id={action_id}]")
                return 'Error: Status value is required.'

            ticket_key = context.data['key']
            status = context.data['status']

            logger.info(f"Updating ticket {ticket_key} status to '{status}' [id={action_id}]")

            # Update the ticket status
            jira.issue_transition(ticket_key, status)

            # Log success with timing information
            duration = time.time() - start_time
            logger.info(
                f"Successfully updated ticket {ticket_key} to status '{status}' [id={action_id}] in {duration:.3f}s"
            )

            return f"Ticket {ticket_key} has been set to '{status}'."
    except KeyError as e:
        duration = time.time() - start_time
        logger.error(f'Missing field in update_ticket_status [id={action_id}] after {duration:.3f}s: {e}')
        return f'Error updating status: Missing required field {e}'
    except (RequestException, HTTPError) as e:
        duration = time.time() - start_time
        logger.error(f'HTTP error in update_ticket_status [id={action_id}] after {duration:.3f}s: {e}')
        return 'Error updating status: Connection to Jira failed. Please try again later.'
    except ApiError as e:
        duration = time.time() - start_time
        logger.error(f'Jira API error in update_ticket_status [id={action_id}] after {duration:.3f}s: {e}')
        return f'Error updating status: {str(e)}'
    except Exception as e:
        duration = time.time() - start_time
        logger.error(f'Unexpected error in update_ticket_status [id={action_id}] after {duration:.3f}s: {e}')
        logger.exception(e)
        return 'An unexpected error occurred while updating the ticket status. Please try again later.'


@audit_message_handler("collect_info_from_user")
@audit_action("collect_info_from_user")
async def collect_info_from_user(
    context: ActionTurnContext,
    state: AppTurnState,
):
    """
    Displays an adaptive card to collect time logging information from the user.

    Args:
        context: The turn context containing the activity and data from the request.
        state: The application state containing user information and tokens.

    Returns:
        str: A confirmation message indicating the form was displayed.

    The function expects the following in context.data:
        - key: The Jira ticket key (e.g., 'BIRI-1234')
        - comment: Optional pre-filled comment for the time log

    The function stores the ticket key in the conversation state and displays
    an adaptive card for the user to enter time logging details.
    """
    # Generate a unique operation ID for tracking this action
    action_id = str(uuid.uuid4())
    start_time = time.time()

    # Get user information if available
    user_id = 'unknown'
    user_name = 'unknown'
    if context.activity and context.activity.from_property:
        user_id = context.activity.from_property.id
        user_name = context.activity.from_property.name

    # Log the action start with context
    logger.info(f'ACTION CALLED: collect_info_from_user [id={action_id}] by user {user_name}')

    try:
        # Use LoggingContext to add structured context to all logs within this function
        with LoggingContext(
            action_id=action_id, function='collect_info_from_user', user_id=user_id, user_name=user_name
        ):
            # Validate required data
            if not context.data.get('key'):
                logger.warning(f"Missing required field 'key' in collect_info_from_user [id={action_id}]")
                return 'Error: Ticket key is required.'

            ticket_key = context.data['key']
            default_start_time = context.data.get('start_time') or Config.TIME_LOGGING_DEFAULT_TIME
            default_duration = context.data.get('time_spent') or Config.TIME_LOGGING_DEFAULT_DURATION
            comment = context.data.get('comment') or ''

            logger.debug(f'Collecting time logging info for ticket {ticket_key} [id={action_id}]')
            logger.debug(
                f'Default values [id={action_id}]: start_time={default_start_time}, duration={default_duration}'
            )

            # Store ticket key in conversation state
            state.conversation['ticket_key'] = ticket_key
            logger.debug(f'Stored ticket_key in conversation state [id={action_id}]')

            # Create the adaptive card
            attachment = create_time_logging_card(
                ticket_key=ticket_key,
                default_time=default_start_time,
                default_hours=float(default_duration),
                default_comment=comment,
            )

            logger.debug(f'Created time logging card for ticket {ticket_key} [id={action_id}]')

            # Send the card and store the activity ID for later updates
            response = await context.send_activity(Activity(attachments=[attachment]))

            if response and hasattr(response, 'id'):
                # Store the activity ID in conversation state for later use
                state.conversation['form_activity_id'] = response.id
                logger.debug(f'Stored form_activity_id in conversation state [id={action_id}]')
            else:
                logger.warning(f'Could not get activity ID from response [id={action_id}]')

            # Log success with timing information
            duration = time.time() - start_time
            logger.info(
                f'Successfully displayed time logging form for ticket {ticket_key} [id={action_id}] in {duration:.3f}s'
            )

            return 'A form has been prompted to the user to collect info required to log the ticket. Waiting for user input to finish the time logging.'
    except KeyError as e:
        duration = time.time() - start_time
        logger.error(f'Missing field in collect_info_from_user [id={action_id}] after {duration:.3f}s: {e}')
        return f'Error displaying form: Missing required field {e}'
    except ValueError as e:
        duration = time.time() - start_time
        logger.error(f'Value error in collect_info_from_user [id={action_id}] after {duration:.3f}s: {e}')
        return f'Error displaying form: Invalid value - {e}'
    except Exception as e:
        duration = time.time() - start_time
        logger.error(f'Unexpected error in collect_info_from_user [id={action_id}] after {duration:.3f}s: {e}')
        logger.exception(e)
        return 'An unexpected error occurred while displaying the form. Please try again later.'


@audit_action("get_valid_status")
@with_jira_client
async def get_valid_status(context: ActionTurnContext, state: AppTurnState, jira: Jira = None):
    """
    Retrieves all valid status transitions for a given Jira ticket.

    Args:
        context: The turn context containing the activity and data from the request.
        state: The application state containing user information and tokens.

    Returns:
        list: A list of valid status transitions available for the ticket.

    The function expects the following in context.data:
        - key: The Jira ticket key (e.g., 'BIRI-1234')

    This function is useful for determining what status values can be used
    when calling update_ticket_status().
    """
    # Generate a unique operation ID for tracking this action
    action_id = str(uuid.uuid4())
    start_time = time.time()

    # Get user information if available
    user_id = 'unknown'
    user_name = 'unknown'
    if context.activity and context.activity.from_property:
        user_id = context.activity.from_property.id
        user_name = context.activity.from_property.name

    # Log the action start with context
    logger.info(f'ACTION CALLED: get_valid_status [id={action_id}] by user {user_name}')

    try:
        # Use LoggingContext to add structured context to all logs within this function
        with LoggingContext(action_id=action_id, function='get_valid_status', user_id=user_id, user_name=user_name):
            # Validate required data
            if not context.data.get('key'):
                logger.warning(f"Missing required field 'key' in get_valid_status [id={action_id}]")
                return 'Error: Ticket key is required.'

            ticket_key = context.data['key']
            logger.debug(f'Getting valid status transitions for ticket {ticket_key} [id={action_id}]')

            # Get the transitions
            transitions = jira.get_issue_transitions(ticket_key)

            # Log the results
            if not transitions:
                logger.info(f'No valid status transitions found for ticket {ticket_key} [id={action_id}]')
                return 'No valid status transitions found for this ticket.'

            # Format the result
            status_list = [transition['to'] for transition in transitions]
            result = ', '.join(status_list)

            # Log success with timing information
            duration = time.time() - start_time
            logger.info(
                f'Found {len(status_list)} valid status transitions for ticket {ticket_key} [id={action_id}] in {duration:.3f}s'
            )
            logger.debug(f'Valid statuses: {result} [id={action_id}]')

            return result
    except KeyError as e:
        duration = time.time() - start_time
        logger.error(f'Missing field in get_valid_status [id={action_id}] after {duration:.3f}s: {e}')
        return f'Error getting status transitions: Missing required field {e}'
    except (RequestException, HTTPError) as e:
        duration = time.time() - start_time
        logger.error(f'HTTP error in get_valid_status [id={action_id}] after {duration:.3f}s: {e}')
        return 'Error getting status transitions: Connection to Jira failed. Please try again later.'
    except ApiError as e:
        duration = time.time() - start_time
        logger.error(f'Jira API error in get_valid_status [id={action_id}] after {duration:.3f}s: {e}')
        return f'Error getting status transitions: {str(e)}'
    except Exception as e:
        duration = time.time() - start_time
        logger.error(f'Unexpected error in get_valid_status [id={action_id}] after {duration:.3f}s: {e}')
        logger.exception(e)
        return 'An unexpected error occurred while getting status transitions. Please try again later.'


@audit_action("get_ticket_field_value")
@with_jira_client
async def get_ticket_field_value(context: ActionTurnContext, state: AppTurnState, jira: Jira = None):
    """
    Retrieves the value of a specific field from a Jira ticket.

    Args:
        context: The turn context containing the activity and data from the request.
        state: The application state containing user information and tokens.

    Returns:
        str: The string representation of the requested field's value.

    The function expects the following in context.data:
        - key: The Jira ticket key (e.g., 'BIRI-1234')
        - field: The name of the field to retrieve (e.g., 'summary', 'description', 'status')

    This function is useful for getting specific information about a ticket
    without retrieving the entire ticket object.
    """
    # Generate a unique operation ID for tracking this action
    action_id = str(uuid.uuid4())
    start_time = time.time()

    # Get user information if available
    user_id = 'unknown'
    user_name = 'unknown'
    if context.activity and context.activity.from_property:
        user_id = context.activity.from_property.id
        user_name = context.activity.from_property.name

    # Log the action start with context
    logger.info(f'ACTION CALLED: get_ticket_field_value [id={action_id}] by user {user_name}')

    try:
        # Use LoggingContext to add structured context to all logs within this function
        with LoggingContext(
            action_id=action_id, function='get_ticket_field_value', user_id=user_id, user_name=user_name
        ):
            # Validate required data
            if not context.data.get('key'):
                logger.warning(f"Missing required field 'key' in get_ticket_field_value [id={action_id}]")
                return 'Error: Ticket key is required.'
            if not context.data.get('field'):
                logger.warning(f"Missing required field 'field' in get_ticket_field_value [id={action_id}]")
                return 'Error: Field name is required.'

            # Extract the ticket key and field name from the context data
            ticket_key = context.data['key']
            field = context.data['field']

            logger.debug(f"Getting field '{field}' from ticket {ticket_key} [id={action_id}]")

            # Call the Jira API to get the field value
            value = jira.issue_field_value(ticket_key, field)

            # Log the result
            if value is not None:
                # Truncate long values in logs
                log_value = str(value)
                if len(log_value) > 100:
                    log_value = log_value[:97] + '...'
                logger.debug(f"Field '{field}' value: {log_value} [id={action_id}]")
            else:
                logger.debug(f"Field '{field}' not found or empty [id={action_id}]")

            # Log success with timing information
            duration = time.time() - start_time
            logger.info(f"Retrieved field '{field}' from ticket {ticket_key} [id={action_id}] in {duration:.3f}s")

            # Convert the value to a string to ensure consistent return type
            # This handles cases where the field might be a complex object, number, boolean, etc.
            return str(value) if value is not None else 'Field not found or empty'
    except KeyError as e:
        duration = time.time() - start_time
        logger.error(f'Missing field in get_ticket_field_value [id={action_id}] after {duration:.3f}s: {e}')
        return f'Error getting field value: Missing required field {e}'
    except (RequestException, HTTPError) as e:
        duration = time.time() - start_time
        logger.error(f'HTTP error in get_ticket_field_value [id={action_id}] after {duration:.3f}s: {e}')
        return 'Error getting field value: Connection to Jira failed. Please try again later.'
    except ApiError as e:
        duration = time.time() - start_time
        logger.error(f'Jira API error in get_ticket_field_value [id={action_id}] after {duration:.3f}s: {e}')
        return f'Error getting field value: {str(e)}'
    except Exception as e:
        duration = time.time() - start_time
        logger.error(f'Unexpected error in get_ticket_field_value [id={action_id}] after {duration:.3f}s: {e}')
        logger.exception(e)
        return 'An unexpected error occurred while getting field value. Please try again later.'


@audit_action("retrieve_jira_tickets")
@with_jira_client
async def get_ticket_worklog(context: ActionTurnContext, state: AppTurnState, jira: Jira = None):
    """
    Retrieves the worklog entries for a Jira ticket.

    Args:
        context: The turn context containing the activity and data from the request.
        state: The application state containing user information and tokens.

    Returns:
        str: A formatted string of worklog entries or a message if none exist.

    The function expects the following in context.data:
        - key: The Jira ticket key (e.g., 'BIRI-1234')
    """
    # Generate a unique operation ID for tracking this action
    action_id = str(uuid.uuid4())
    start_time = time.time()

    # Get user information if available
    user_id = 'unknown'
    user_name = 'unknown'
    if context.activity and context.activity.from_property:
        user_id = context.activity.from_property.id
        user_name = context.activity.from_property.name

    # Log the action start with context
    logger.info(f'ACTION CALLED: get_ticket_worklog [id={action_id}] by user {user_name}')

    try:
        # Use LoggingContext to add structured context to all logs within this function
        with LoggingContext(action_id=action_id, function='get_ticket_worklog', user_id=user_id, user_name=user_name):
            # Validate required data
            if not context.data.get('key'):
                logger.warning(f"Missing required field 'key' in get_ticket_worklog [id={action_id}]")
                return 'Error: Ticket key is required.'

            ticket_key = context.data['key']
            logger.debug(f'Getting worklog entries for ticket {ticket_key} [id={action_id}]')

            # Get the worklog entries
            raw_worklogs = jira.issue_get_worklog(ticket_key)

            # Process the results
            if raw_worklogs is not None:
                worklog_entries = raw_worklogs.get('worklogs', [])
                logger.info(f'Found {len(worklog_entries)} worklog entries for ticket {ticket_key} [id={action_id}]')

                # Format the worklog entries
                worklogs = []
                for idx, wl in enumerate(worklog_entries):
                    # Log each worklog entry at debug level
                    if idx < 5:  # Only log first 5 entries in detail
                        logger.debug(
                            f'Worklog {idx + 1}: started={wl.get("started", "unknown")}, '
                            f'time={wl.get("timeSpent", "unknown")}, '
                            f'author={wl.get("author", {}).get("displayName", "unknown")} [id={action_id}]'
                        )

                    # Format the entry for the response
                    worklogs.append(
                        f'worklog {idx + 1}: started at {wl["started"]}, duration: {wl["timeSpent"]}, comment: {wl.get("comment", "")}'
                    )

                if len(worklog_entries) > 5:
                    logger.debug(f'... and {len(worklog_entries) - 5} more worklog entries [id={action_id}]')

                # Log success with timing information
                duration = time.time() - start_time
                logger.info(
                    f'Successfully retrieved worklog for ticket {ticket_key} [id={action_id}] in {duration:.3f}s'
                )

                return '\n'.join(worklogs) if worklogs else 'No worklogs found.'
            else:
                logger.info(f'No worklogs found for ticket {ticket_key} [id={action_id}]')
                return 'No worklogs found.'
    except KeyError as e:
        duration = time.time() - start_time
        logger.error(f'Missing field in get_ticket_worklog [id={action_id}] after {duration:.3f}s: {e}')
        return f'Error getting worklog: Missing required field {e}'
    except (RequestException, HTTPError) as e:
        duration = time.time() - start_time
        logger.error(f'HTTP error in get_ticket_worklog [id={action_id}] after {duration:.3f}s: {e}')
        return 'Error getting worklog: Connection to Jira failed. Please try again later.'
    except ApiError as e:
        duration = time.time() - start_time
        logger.error(f'Jira API error in get_ticket_worklog [id={action_id}] after {duration:.3f}s: {e}')
        return f'Error getting worklog: {str(e)}'
    except Exception as e:
        duration = time.time() - start_time
        logger.error(f'Unexpected error in get_ticket_worklog [id={action_id}] after {duration:.3f}s: {e}')
        logger.exception(e)
        return 'An unexpected error occurred while getting worklog information. Please try again later.'


@audit_action("retrieve_jira_tickets")
@with_jira_client
async def retrieve_jira_tickets(context: ActionTurnContext, state: AppTurnState, jira: Jira = None):
    """
    Retrieves tickets from Jira based on a query.

    Args:
        context: The turn context containing the activity and data from the request.
        state: The application state containing user information and tokens.
        jira: The Jira client instance (injected by the decorator).

    Returns:
        list: A list of Issue instances representing Jira issues.

    The function expects the following in context.data:
        - query: Optional JQL query to filter tickets
        - limit: Optional maximum number of tickets to return
    """
    # Generate a unique operation ID for tracking this action
    action_id = str(uuid.uuid4())
    start_time = time.time()

    # Get user information if available
    user_id = 'unknown'
    user_name = 'unknown'
    if context.activity and context.activity.from_property:
        user_id = context.activity.from_property.id
        user_name = context.activity.from_property.name

    # Log the action start with context
    logger.info(f'ACTION CALLED: retrieve_jira_tickets [id={action_id}] by user {user_name}')

    try:
        # Use LoggingContext to add structured context to all logs within this function
        with LoggingContext(
            action_id=action_id, function='retrieve_jira_tickets', user_id=user_id, user_name=user_name
        ):
            # Get the base query from the context
            base_query = context.data.get('query') or ''
            limit = context.data.get('limit') or Config.JIRA_RESULTS_LIMIT

            logger.debug(f"Base query: '{base_query}', limit: {limit} [id={action_id}]")

            # Get the current user's Jira account ID
            myself = jira.myself()
            if not myself or 'accountId' not in myself:
                logger.error(f'Failed to get user account ID [id={action_id}]')
                raise KeyError('Could not retrieve user account ID')

            account_id = myself['accountId']
            logger.debug(f'User account ID: {account_id} [id={action_id}]')

            # Always include the assignee filter
            if 'assignee =' not in base_query.lower():
                if base_query:
                    query = f"assignee = '{account_id}' AND {base_query}"
                else:
                    query = f"assignee = '{account_id}'"
            else:
                # If there's already an assignee filter, don't modify the query
                query = base_query

            logger.info(f"Executing JQL query: '{query}' with limit {limit} [id={action_id}]")

            # Execute the query
            issues_result = jira.jql(query, limit=limit)

            # Process the results
            if issues_result and 'issues' in issues_result:
                raw_issues = issues_result.get('issues', [])
                total = issues_result.get('total', 0)

                # Log the results
                logger.info(f'Found {len(raw_issues)} issues (total: {total}) [id={action_id}]')

                # Convert raw issues to Issue instances
                issues = []
                for raw_issue in raw_issues:
                    issue = Issue.from_jira_api_response(raw_issue)
                    issues.append(issue)

                # Log some details about the first few issues at debug level
                if issues:
                    for i, issue in enumerate(issues[:5]):  # Log first 5 issues only
                        logger.debug(f'Issue {i + 1}: {issue.key} - {issue.summary[:50]}... [id={action_id}]')
                    if len(issues) > 5:
                        logger.debug(f'... and {len(issues) - 5} more issues [id={action_id}]')

                # Log success with timing information
                duration = time.time() - start_time
                logger.info(
                    f'Successfully retrieved and converted {len(issues)} Jira tickets to Issue instances [id={action_id}] in {duration:.3f}s'
                )

                return f"{len(issues)} issues found: {[issue.to_dict() for issue in issues]}"
            else:
                logger.info(f"No issues found for query: '{query}' [id={action_id}]")
                return []
    except KeyError as e:
        duration = time.time() - start_time
        logger.error(f'Missing field in retrieve_jira_tickets [id={action_id}] after {duration:.3f}s: {e}')
        logger.exception(e)
        raise
    except (RequestException, HTTPError) as e:
        duration = time.time() - start_time
        logger.error(f'HTTP error in retrieve_jira_tickets [id={action_id}] after {duration:.3f}s: {e}')
        logger.exception(e)
        raise
    except ApiError as e:
        duration = time.time() - start_time
        logger.error(f'Jira API error in retrieve_jira_tickets [id={action_id}] after {duration:.3f}s: {e}')
        logger.exception(e)
        raise
    except Exception as e:
        duration = time.time() - start_time
        logger.error(f'Unexpected error in retrieve_jira_tickets [id={action_id}] after {duration:.3f}s: {e}')
        logger.exception(e)
        raise


@audit_message_handler("display_jira_tickets")
@audit_action("display_jira_tickets")
async def display_jira_tickets(context: ActionTurnContext, state: AppTurnState):
    """
    Displays a list of Issue instances as an adaptive card.

    Args:
        context: The turn context containing the activity and data from the request.
        state: The application state containing user information and tokens.

    Returns:
        str: A confirmation message indicating the tickets were displayed.

    The function expects the following in context.data:
        - issues: A list of Issue instances to display
    """
    # Generate a unique operation ID for tracking this action
    action_id = str(uuid.uuid4())
    start_time = time.time()

    # Get user information if available
    user_id = 'unknown'
    user_name = 'unknown'
    if context.activity and context.activity.from_property:
        user_id = context.activity.from_property.id
        user_name = context.activity.from_property.name

    # Log the action start with context
    logger.info(f'ACTION CALLED: display_jira_tickets [id={action_id}] by user {user_name}')

    try:
        # Use LoggingContext to add structured context to all logs within this function
        with LoggingContext(action_id=action_id, function='display_jira_tickets', user_id=user_id, user_name=user_name):
            # Retrieve the tickets from the context
            if not context.data.get('issues'):
                logger.warning(f"Missing required field 'issues' in display_jira_tickets [id={action_id}]")
                return 'Error: Tickets are required.'

            issues = context.data['issues']

            # Log the tickets found
            logger.info(f'Displaying {len(issues)} tickets for user {user_name} [id={action_id}]')

            # Only log ticket details at DEBUG level
            for i, issue in enumerate(issues[:5]):  # Log first 5 tickets only
                logger.debug(f'Ticket {i + 1}: {issue["key"]} - {issue["summary"]} [id={action_id}]')
            if len(issues) > 5:
                logger.debug(f'... and {len(issues) - 5} more tickets [id={action_id}]')

            # Create an adaptive card with the ticket information
            logger.debug(f'Creating ticket list card [id={action_id}]')
            ticket_card = create_ticket_list_card(issues)

            # Send the card as an activity
            logger.debug(f'Sending adaptive card to user [id={action_id}]')
            await context.send_activity(Activity(attachments=[ticket_card]))

            # Log success with timing information
            duration = time.time() - start_time
            logger.info(f'Successfully displayed {len(issues)} tickets [id={action_id}] in {duration:.3f}s')

            # Return a simple confirmation message
            return (
                'Already returned the results to the user in the form of an adaptive card. DO NOT return anything else.'
            )
    except KeyError as e:
        duration = time.time() - start_time
        logger.error(f'Missing field in display_jira_tickets [id={action_id}] after {duration:.3f}s: {e}')
        return f'Error getting tickets: Missing required field {e}'
    except (RequestException, HTTPError) as e:
        duration = time.time() - start_time
        logger.error(f'HTTP error in display_jira_tickets [id={action_id}] after {duration:.3f}s: {e}')
        return 'Error getting tickets: Connection to Jira failed. Please try again later.'
    except ApiError as e:
        duration = time.time() - start_time
        logger.error(f'Jira API error in display_jira_tickets [id={action_id}] after {duration:.3f}s: {e}')
        return f'Error getting tickets: {str(e)}'
    except Exception as e:
        duration = time.time() - start_time
        logger.error(f'Unexpected error in display_jira_tickets [id={action_id}] after {duration:.3f}s: {e}')
        logger.exception(e)
        return 'An unexpected error occurred while getting tickets. Please try again later.'


@audit_action("search_user")
@with_jira_client
async def search_user(context: ActionTurnContext, state: AppTurnState, jira: Jira = None):
    """
    Searches for Jira users based on a query string.

    Args:
        context: The turn context containing the activity and data from the request.
        state: The application state containing user information and tokens.

    Returns:
        str: Information about the found user or an error message.

    The function expects the following in context.data:
        - user_query: The search string to find users
    """
    # Generate a unique operation ID for tracking this action
    action_id = str(uuid.uuid4())
    start_time = time.time()

    # Get user information if available
    user_id = 'unknown'
    user_name = 'unknown'
    if context.activity and context.activity.from_property:
        user_id = context.activity.from_property.id
        user_name = context.activity.from_property.name

    # Log the action start with context
    logger.info(f'ACTION CALLED: search_user [id={action_id}] by user {user_name}')

    try:
        # Use LoggingContext to add structured context to all logs within this function
        with LoggingContext(action_id=action_id, function='search_user', user_id=user_id, user_name=user_name):
            # Validate required data
            if not context.data.get('user_query'):
                logger.warning(f"Missing required field 'user_query' in search_user [id={action_id}]")
                return 'Error: User query is required.'

            user_query = context.data.get('user_query') or ''
            logger.debug(f"Searching for Jira users with query: '{user_query}' [id={action_id}]")

            if user_query:
                # Set cloud flag for API compatibility
                logger.debug(f'Setting jira.cloud=True for user search [id={action_id}]')
                jira.cloud = True  # workaround to allow user_find_by_user_string() to accept query parameter

                # Perform the search
                users = jira.user_find_by_user_string(query=user_query)

                # Reset cloud flag
                logger.debug(f'Resetting jira.cloud=False after user search [id={action_id}]')
                jira.cloud = False

                # Process results
                if users and isinstance(users, list) and len(users) > 0:
                    # Log user details at debug level
                    user_names = []
                    for user in users[:5]:  # Log first 5 users only
                        if isinstance(user, dict) and 'displayName' in user:
                            user_names.append(user['displayName'])
                    logger.debug(f'Found users: {user_names} [id={action_id}]')
                    if len(users) > 5:
                        logger.debug(f'... and {len(users) - 5} more users [id={action_id}]')

                    # Format first user details for response
                    first_user = users[0]
                    display_name = first_user['displayName'] if 'displayName' in first_user else 'Unknown'
                    email = first_user['emailAddress'] if 'emailAddress' in first_user else 'Unknown'
                    account_id = first_user['accountId'] if 'accountId' in first_user else 'Unknown'

                    # Log success with timing information
                    duration = time.time() - start_time
                    logger.info(
                        f"Found {len(users)} users matching query '{user_query}' [id={action_id}] in {duration:.3f}s"
                    )

                    return f'User: {display_name} - {email} - {account_id}'
                else:
                    # Log no results found
                    duration = time.time() - start_time
                    logger.info(f"No users found matching query '{user_query}' [id={action_id}] in {duration:.3f}s")
                    return 'No users found matching the query.'
            else:
                logger.warning(f'Empty user query provided [id={action_id}]')
                return 'Empty user query.'
    except KeyError as e:
        duration = time.time() - start_time
        logger.error(f'Missing field in search_user [id={action_id}] after {duration:.3f}s: {e}')
        return f'Error searching user: Missing required field {e}'
    except (RequestException, HTTPError) as e:
        duration = time.time() - start_time
        logger.error(f'HTTP error in search_user [id={action_id}] after {duration:.3f}s: {e}')
        return 'Error searching user: Connection to Jira failed. Please try again later.'
    except ApiError as e:
        duration = time.time() - start_time
        logger.error(f'Jira API error in search_user [id={action_id}] after {duration:.3f}s: {e}')
        return f'Error searching user: {str(e)}'
    except Exception as e:
        duration = time.time() - start_time
        logger.error(f'Unexpected error in search_user [id={action_id}] after {duration:.3f}s: {e}')
        logger.exception(e)
        return 'An unexpected error occurred while searching for the user. Please try again later.'
