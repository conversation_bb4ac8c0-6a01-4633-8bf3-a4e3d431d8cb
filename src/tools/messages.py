import json
import re
from pathlib import Path

from botbuilder.core import Turn<PERSON>ontext
from botbuilder.schema import Activity

from config import Config
from state import AppTurnState
from utils.audit_decorators import audit_message_handler
from utils.scheduler import Scheduler, remind


@audit_message_handler("get_bot_version")
async def get_bot_version(context: TurnContext, state: AppTurnState) -> bool:
    """
    Retrieves and displays the bot's version information from the release_note.json file.

    Args:
        context: The turn context for sending responses back to the user
        state: The application state (not used in this function but required by the framework)

    Returns:
        bool: Always returns False to indicate the turn shouldn't end

    This function:
    1. Locates the release_note.json file relative to this script's location
    2. Extracts version number, fixes, features, and release date
    3. Formats this information into a readable message
    4. Sends the formatted message to the user
    """
    # Get the src directory path (one level up from the tools directory)
    src_dir = Path(__file__).resolve().parents[1]

    # Open and parse the release_note.json file
    with open(src_dir / 'release_note.json', 'r') as f:
        # Load the JSON content
        release_note = json.load(f)[0]

        # Extract the version number
        version = release_note['version']

        # Format the list of fixes with bullet points and line breaks
        fixes = (
            '\n\n'.join('- ' + fix for fix in release_note['releaseNotes']['fix'])
            if release_note['releaseNotes'].get('fix')
            else '- NA'
        )

        # Format the list of features with bullet points and line breaks
        features = (
            '\n\n'.join('- ' + feat for feat in release_note['releaseNotes']['feat'])
            if release_note['releaseNotes'].get('feat')
            else '- NA'
        )

        # Extract the release date
        release_date = release_note['releaseTime']

    # Construct a formatted message with all the version information
    version_message = (
        f'Version: {version}<br/><br/>Fixes:\n\n{fixes}\n\nFeatures:\n\n{features}\n\nRelease Date: {release_date}'
    )

    # Send the formatted message to the user
    await context.send_activity(version_message)

    # Return False to indicate the turn shouldn't end
    return False


@audit_message_handler("reset_history")
async def reset_history(context: TurnContext, state: AppTurnState) -> bool:
    """
    Resets the conversation history for the current prompt.

    Args:
        context: The turn context for sending responses back to the user
        state: The application state containing conversation history

    Returns:
        bool: Always returns False to indicate the turn shouldn't end

    This function:
    1. Identifies the current prompt by finding the first directory in the prompts folder
    2. Clears the conversation history for that prompt if it exists
    3. Notifies the user that the history has been reset
    """
    # Get the prompts directory path (located in the src directory)
    prompt_dir = Path(__file__).resolve().parents[1] / 'prompts'

    # Find the first directory in the prompts folder - assumes this is the active prompt
    # This is a simplification that works if there's only one prompt or if the first
    # directory is the one we want to reset
    for file in prompt_dir.iterdir():
        if file.is_dir():
            prompt_name = file.name
            break

    # Check if history exists for this prompt and reset it if it does
    if state.conversation.get(f'{prompt_name}_history'):
        # Reset the history by setting it to an empty list
        state.conversation[f'{prompt_name}_history'] = []

    # Notify the user that the history has been reset
    await context.send_activity('History reset.')

    # Return False to indicate the turn shouldn't end
    return False


@audit_message_handler("display_help")
async def display_help(context: TurnContext, state: AppTurnState) -> bool:
    """
    Displays a help message to the user.

    Args:
        context: The turn context for sending responses back to the user
        state: The application state (not used in this function but required by the framework)

    Returns:
        bool: Always returns False to indicate the turn shouldn't end

    This function:
    1. Constructs a help message with a list of available commands
    2. Sends the help message to the user
    """
    msg = Activity(text=Config.HELP_MESSAGE, text_format='markdown')
    await context.send_activity(msg)
    return False


@audit_message_handler("schedule_reminder")
async def schedule_reminder(context: TurnContext, state: AppTurnState) -> bool:
    scheduler = Scheduler.get_instance()
    match = re.match(r'^/schedule\s+(\d{,2}):(\d{,2})', context.activity.text)
    if match:
        hour = match.group(1)
        minute = match.group(2)
        if int(hour) > 23 or int(minute) > 59:
            await context.send_activity('Invalid time format. Please use /schedule HH:MM in 24 hours format.')
            return False
        scheduled_job_id = state.user.get('scheduled_job_id')
        if scheduled_job_id:
            job = scheduler.reschedule_job(
                scheduled_job_id, trigger='cron', hour=hour, minute=minute, day_of_week='mon-fri'
            )
            state.user['scheduled_job_id'] = job.id
            await context.send_activity(f'Rescheduled a reminder for {hour}:{minute} at {job.trigger.fields[4]}.')
        else:
            job = scheduler.add_job(
                remind,
                'cron',
                hour=hour,
                minute=minute,
                day_of_week='mon-fri',
                kwargs={'context': context, 'state': state},
            )
            state.user['scheduled_job_id'] = job.id
            await context.send_activity(f'Scheduled a reminder for {hour}:{minute} at {job.trigger.fields[4]}.')
    else:
        await context.send_activity('Invalid time format. Please use /schedule HH:MM in 24 hours format.')
    return False
