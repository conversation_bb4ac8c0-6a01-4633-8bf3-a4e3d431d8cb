import traceback
import uuid

from botbuilder.core import <PERSON><PERSON><PERSON>x<PERSON>
from loguru import logger

from auth.jira_auth import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from auth.token_manager import SQLTokenStorage
from utils.logging_config import Logging<PERSON>ontext


async def before_turn(context: TurnContext, state=None):
    """
    Handle authentication before processing a turn.

    Args:
        context: Bot turn context
        state: App turn state (kept for backward compatibility, not used)

    Returns:
        bool: True if authentication is valid, False otherwise
    """
    # Generate a unique ID for this turn
    turn_id = str(uuid.uuid4())

    # Get user information if available
    user_id = 'unknown'
    user_name = 'unknown'
    if context.activity and context.activity.from_property:
        user_id = context.activity.from_property.id
        user_name = context.activity.from_property.name

    # Log the turn with context
    logger.bind(
        turn_id=turn_id,
        user_id=user_id,
        user_name=user_name,
        activity_type=getattr(context.activity, 'type', 'unknown'),
        channel_id=getattr(context.activity, 'channel_id', 'unknown'),
    ).debug(f'Processing turn for user {user_name}')

    async def handle_authentication(user_id, user_name, service_name, auth_func, refresh_func):
        """Handle authentication for a specific service."""
        # Create SQL token storage
        token_storage = SQLTokenStorage()

        # Get tokens from storage
        logger.debug(f'Checking {service_name} authentication for user {user_name}')
        tokens = token_storage.get_tokens(user_id)
        access_token = tokens.get('access_token')
        refresh_token = tokens.get('refresh_token')
        expires_at = tokens.get('expires_at')

        if not access_token:
            # No token found, initiate authentication
            logger.info(f'No {service_name} access token found for user {user_name}, initiating authentication')
            await auth_func(context)
            return

        # Check if token is expired and needs refresh
        if expires_at and refresh_token and JiraAuthManager.check_token_expiration(expires_at):
            logger.info(f'{service_name} token expired for user {user_name}, refreshing')
            try:
                await refresh_func(user_id, user_name)
                logger.info(f'Successfully refreshed {service_name} token for user {user_name}')
            except Exception as e:
                logger.error(f'Failed to refresh {service_name} token: {e}')
                logger.exception(e)
                # Initiate new authentication if refresh fails
                logger.info(f'Initiating new {service_name} authentication after refresh failure')
                await auth_func(context)

    try:
        # Get user ID from context
        if context.activity and context.activity.from_property:
            user_id = context.activity.from_property.id
            user_name = context.activity.from_property.name

            with LoggingContext(turn_id=turn_id, user_id=user_id, user_name=user_name):
                await handle_authentication(
                    user_id, user_name, 'jira', JiraAuthManager.on_jira_auth, JiraAuthManager.refresh_token
                )

                # Check if authentication was successful by getting tokens again
                token_storage = SQLTokenStorage()
                tokens = token_storage.get_tokens(user_id)
                is_authenticated = bool(tokens.get('access_token'))

                logger.debug(
                    f'Authentication status for user {user_name}: {"Authenticated" if is_authenticated else "Not authenticated"}'
                )
                return is_authenticated
    except Exception as e:
        logger.error(f'Error in before_turn: {e}')
        logger.exception(e)

    logger.debug('Authentication failed or no user information available')
    return False


async def on_error(context: TurnContext, error: Exception):
    """
    Handle unhandled errors during bot turn processing.

    Args:
        context: Bot turn context
        error: The exception that was raised
    """
    error_id = str(uuid.uuid4())

    # Get user information if available
    user_id = 'unknown'
    user_name = 'unknown'
    if context.activity and context.activity.from_property:
        user_id = context.activity.from_property.id
        user_name = context.activity.from_property.name

    # Log the error with context
    logger.bind(
        error_id=error_id,
        user_id=user_id,
        user_name=user_name,
        activity_id=getattr(context.activity, 'id', 'unknown'),
        channel_id=getattr(context.activity, 'channel_id', 'unknown'),
    ).error(f'Unhandled error in bot turn: {error}')

    # Log the full traceback at debug level
    logger.debug(f'Error traceback [id={error_id}]:\n{traceback.format_exc()}')

    # Send a user-friendly message
    error_message = f"I'm sorry, but something went wrong. Our team has been notified (Error ID: {error_id})."

    try:
        await context.send_activity(error_message)
    except Exception as send_error:
        # If we can't send the message, log that too
        logger.error(f'Failed to send error message to user: {send_error}')
