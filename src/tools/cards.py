from typing import Any, Dict, List

import pendulum
from atlassian import <PERSON><PERSON>
from botbuilder.core import CardFactory, TurnContext
from botbuilder.schema import Activity, Attachment

from state import AppTurnState
from utils.audit_decorators import audit_message_handler
from utils.decorators import with_jira_client


def create_time_logging_card(
    ticket_key: str | None = None,
    default_date: str = pendulum.now().format('YYYY-MM-DD'),
    default_time: str | None = None,
    default_hours: float | None = None,
    default_comment: str | None = None,
) -> Attachment:
    body = []

    body.extend([
        {'type': 'TextBlock', 'text': f'Log time to your ticket{" " + ticket_key if ticket_key else ""}'},
        {
            'type': 'ColumnSet',
            'columns': [
                {
                    'type': 'Column',
                    'width': 'stretch',
                    'items': [
                        {
                            'id': 'id_date',
                            'type': 'Input.Date',
                            'label': 'Date',
                            'value': default_date if default_date is not None else None,
                        }
                    ],
                },
                {
                    'type': 'Column',
                    'width': 'stretch',
                    'items': [
                        {
                            'id': 'id_start_time',
                            'type': 'Input.Time',
                            'label': 'Start Time',
                            'value': default_time if default_time is not None else None,
                        }
                    ],
                },
                {
                    'type': 'Column',
                    'width': 'stretch',
                    'items': [
                        {
                            'id': 'id_hours',
                            'type': 'Input.Number',
                            'label': 'Hours Spent',
                            'value': default_hours if default_hours is not None else None,
                        }
                    ],
                },
            ],
        },
        {
            'id': 'id_comment',
            'type': 'Input.Text',
            'label': 'Comments',
            'placeholder': 'Input your comments here',
            'value': default_comment if default_comment is not None else None,
        },
    ])

    card = {
        'type': 'AdaptiveCard',
        '$schema': 'https://adaptivecards.io/schemas/adaptive-card.json',
        'version': '1.5',
        'body': body,
        'actions': [
            {
                'type': 'Action.Submit',
                'title': 'Submit',
                'iconUrl': 'icon:ArrowExportLtr',
                'style': 'positive',
                'data': {'verb': 'submit'},
            }
        ],
    }

    return CardFactory.adaptive_card(card)


def create_readonly_time_logging_card(
    ticket_key: str,
    date_started: str,
    time_started: str,
    time_spent: float,
    comment: str,
) -> Attachment:
    body = []

    body.extend([
        {'type': 'TextBlock', 'text': f'Time logged successfully to ticket {ticket_key}', 'weight': 'Bolder'},
        {
            'type': 'ColumnSet',
            'columns': [
                {
                    'type': 'Column',
                    'width': 'stretch',
                    'items': [
                        {
                            'type': 'TextBlock',
                            'text': 'Date',
                            'weight': 'Bolder',
                        },
                        {
                            'type': 'TextBlock',
                            'text': date_started,
                        },
                    ],
                },
                {
                    'type': 'Column',
                    'width': 'stretch',
                    'items': [
                        {
                            'type': 'TextBlock',
                            'text': 'Start Time',
                            'weight': 'Bolder',
                        },
                        {
                            'type': 'TextBlock',
                            'text': time_started,
                        },
                    ],
                },
                {
                    'type': 'Column',
                    'width': 'stretch',
                    'items': [
                        {
                            'type': 'TextBlock',
                            'text': 'Hours Spent',
                            'weight': 'Bolder',
                        },
                        {
                            'type': 'TextBlock',
                            'text': str(time_spent),
                        },
                    ],
                },
            ],
        },
        {
            'type': 'TextBlock',
            'text': 'Comment',
            'weight': 'Bolder',
        },
        {
            'type': 'TextBlock',
            'text': comment or '(No comment provided)',
            'wrap': True,
        },
        {
            'type': 'TextBlock',
            'text': '✅ This time log has been submitted and cannot be resubmitted.',
            'color': 'Good',
            'spacing': 'Medium',
        },
    ])

    card = {
        'type': 'AdaptiveCard',
        '$schema': 'https://adaptivecards.io/schemas/adaptive-card.json',
        'version': '1.5',
        'body': body,
        # No actions/buttons in the read-only version
    }

    return CardFactory.adaptive_card(card)


def create_jira_auth_card(authorization_url: str) -> Attachment:
    """
    Create an Adaptive Card for Jira authentication.

    Args:
        authorization_url: The authorization URL to redirect the user to

    Returns:
        Attachment: The Adaptive Card attachment
    """
    card = {
        'type': 'AdaptiveCard',
        '$schema': 'http://adaptivecards.io/schemas/adaptive-card.json',
        'version': '1.5',
        'body': [
            {
                'type': 'ColumnSet',
                'columns': [
                    {
                        'type': 'Column',
                        'width': 'auto',
                        'items': [
                            {
                                'type': 'Image',
                                'url': 'https://wac-cdn.atlassian.com/assets/img/favicons/atlassian/favicon-32x32.png',
                                'size': 'Small',
                                'altText': 'Jira Logo',
                            }
                        ],
                    },
                    {
                        'type': 'Column',
                        'width': 'stretch',
                        'items': [
                            {
                                'type': 'TextBlock',
                                'text': 'Jira Authentication Required',
                                'weight': 'Bolder',
                                'size': 'Large',
                                'color': 'Accent',
                            }
                        ],
                    },
                ],
            },
            {'type': 'TextBlock', 'text': 'You need to authenticate with Jira to use this feature.', 'wrap': True},
            {
                'type': 'TextBlock',
                'text': "Please click the button below to authenticate with your Jira account. You will be redirected to Atlassian's authentication page.",
                'wrap': True,
            },
            {
                'type': 'TextBlock',
                'text': 'After successful authentication, you will be redirected back to this conversation.',
                'wrap': True,
                'spacing': 'Medium',
            },
        ],
        'actions': [
            {
                'type': 'Action.OpenUrl',
                'title': 'Authenticate with Jira',
                'url': authorization_url,
                'style': 'positive',
                'iconUrl': 'https://wac-cdn.atlassian.com/assets/img/favicons/atlassian/favicon-16x16.png',
            },
            {
                'type': 'Action.Submit',
                'title': 'Cancel',
                'style': 'destructive',
                'data': {'verb': 'action', 'action': 'cancel_jira_auth'},
            },
        ],
    }

    return CardFactory.adaptive_card(card)


def create_auth_success_card(user_name: str) -> str:
    """
    Create an HTML success page with Jira branding for authentication callback.

    Args:
        user_name: The name of the authenticated user

    Returns:
        str: HTML content for the success page
    """
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Jira Authentication Successful</title>
        <style>
            body {{
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                background-color: #f4f5f7;
                margin: 0;
                padding: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                color: #172B4D;
            }}
            .container {{
                background-color: white;
                border-radius: 3px;
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                padding: 40px;
                text-align: center;
                max-width: 500px;
            }}
            .header {{
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 20px;
            }}
            .logo {{
                width: 40px;
                height: 40px;
                margin-right: 10px;
            }}
            h1 {{
                color: #0052CC;
                margin: 0;
            }}
            .success-icon {{
                color: #36B37E;
                font-size: 48px;
                margin: 20px 0;
            }}
            .message {{
                margin-bottom: 30px;
                line-height: 1.5;
            }}
            .button {{
                background-color: #0052CC;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 10px 20px;
                font-size: 14px;
                cursor: pointer;
                text-decoration: none;
                display: inline-block;
            }}
            .button:hover {{
                background-color: #0747A6;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <img class="logo" src="https://wac-cdn.atlassian.com/assets/img/favicons/atlassian/favicon-32x32.png" alt="Jira Logo">
                <h1>Authentication Successful</h1>
            </div>
            <div class="success-icon">✓</div>
            <div class="message">
                <p>Hello {user_name}, you have successfully authenticated with Jira.</p>
                <p>You can now close this window and return to the conversation to continue using Jira features.</p>
            </div>
            <button class="button" onclick="window.close()">Close Window</button>
        </div>
    </body>
    </html>
    """
    return html


@audit_message_handler("on_time_logging_submit")
@with_jira_client
async def on_submit(context: TurnContext, state: AppTurnState, data: Any, jira: Jira = None):
    if state.conversation.get('ticket_key'):
        ticket_key = state.conversation['ticket_key']
        time_started = data.get('id_start_time') or '09:00'
        date_started = data['id_date']
        time_spent = data['id_hours']
        comment = data.get('id_comment', '')

        datetime_started = f'{date_started}T{time_started}:00.000+1100'
        time_sec = int(float(time_spent) * 3600)

        if comment:
            jira.issue_worklog(key=ticket_key, started=datetime_started, time_sec=time_sec, comment=comment)
        else:
            jira.issue_worklog(key=ticket_key, started=datetime_started, time_sec=time_sec)

        # Get the ID of the original activity that contains the form
        form_activity_id = state.conversation.get('form_activity_id')
        # Create a readonly version of the card
        readonly_card = create_readonly_time_logging_card(
            ticket_key=ticket_key,
            date_started=date_started,
            time_started=time_started,
            time_spent=time_spent,
            comment=comment,
        )

        # First send confirmation message
        await context.send_activity(
            f'{time_spent}-hour worklog with comment [{comment or "empty"}] has been logged into ticket {ticket_key}.'
        )

        # Update the original card if we have its ID
        if form_activity_id:
            # Create a new activity with the same ID but with the read-only card
            updated_activity = Activity(id=form_activity_id, type='message', attachments=[readonly_card])

            # Update the activity with our new read-only card
            await context.update_activity(updated_activity)
        else:
            # Fallback: send a new message if we don't have the activity ID
            await context.send_activity(Activity(attachments=[readonly_card]))
    else:
        await context.send_activity('No ticket key found.')


@audit_message_handler("on_jira_auth_action")
async def on_jira_auth_action(context: TurnContext, _: AppTurnState, data: dict):
    """Handle actions from the Jira authentication Adaptive Card."""
    action = data.get('action')
    if action == 'cancel_jira_auth':
        await context.send_activity(
            'Jira authentication was canceled. Some features may not be available without authentication.'
        )
        return


def create_ticket_list_card(issues: List[Dict]) -> Attachment:
    """
    Create an Adaptive Card displaying a list of Jira tickets.

    Args:
        issues: A list of Jira issue dictionaries containing ticket information

    Returns:
        Attachment: An Adaptive Card attachment displaying the tickets in a formatted list
    """
    # Create the card header
    body = [
        {
            'type': 'TextBlock',
            'text': 'Your Jira Tickets',
            'weight': 'Bolder',
            'size': 'Large',
            'wrap': True,
        }
    ]

    # If no issues, show a message
    if not issues:
        body.append({'type': 'TextBlock', 'text': 'No tickets found.', 'wrap': True})
    else:
        # Add each ticket as a container with factset
        for issue in issues:
            try:
                key = issue.get('key', 'Unknown')
                summary = issue.get('summary', 'No summary')
                description = issue.get('description', 'No description')
                description = description if description else 'No description'
                status = issue.get('status', 'Unknown')

                # Create a container for each ticket with a button to open in Jira
                ticket_container = {
                    'type': 'Container',
                    'style': 'emphasis',
                    'items': [
                        {
                            'type': 'ColumnSet',
                            'columns': [
                                {
                                    'type': 'Column',
                                    'width': 'auto',
                                    'items': [
                                        {'type': 'TextBlock', 'text': key, 'color': 'accent', 'weight': 'Bolder'}
                                    ],
                                },
                                {
                                    'type': 'Column',
                                    'width': 'stretch',
                                    'items': [{'type': 'TextBlock', 'text': summary, 'wrap': True}],
                                },
                            ],
                        },
                        {
                            'type': 'FactSet',
                            'facts': [
                                {'title': 'Status', 'value': status},
                                {
                                    'title': 'Description',
                                    'value': description[:100] + ('...' if len(description) > 100 else ''),
                                },
                            ],
                        },
                        {
                            'type': 'ActionSet',
                            'actions': [
                                {
                                    'type': 'Action.OpenUrl',
                                    'title': f'Open {key} in Jira',
                                    'url': f'https://petsure.atlassian.net/browse/{key}',
                                    'style': 'positive',
                                    'iconUrl': 'https://wac-cdn.atlassian.com/assets/img/favicons/atlassian/favicon-16x16.png',
                                }
                            ],
                        },
                    ],
                    'separator': True,
                }

                body.append(ticket_container)
            except Exception:
                # Skip this ticket if there's an error processing it
                continue

    # Create the adaptive card
    card = {
        'type': 'AdaptiveCard',
        '$schema': 'http://adaptivecards.io/schemas/adaptive-card.json',
        'version': '1.5',
        'body': body,
        'msteams': {'width': 'Full'},
    }

    return CardFactory.adaptive_card(card)
