"""
Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the MIT License.
"""

import os

from aiohttp import web
from loguru import logger

from api import api
from config import Config
from utils.logging_config import setup_logging

if __name__ == '__main__':
    # Setup logging with appropriate level
    log_level = os.environ.get('LOG_LEVEL', 'INFO').upper()
    setup_logging(log_level)

    # Log application startup
    logger.info('Starting bot_framework application')
    logger.info(f'Server running on localhost:{Config.PORT}')

    try:
        web.run_app(api, host='localhost', port=Config.PORT)
    except Exception as e:
        logger.critical(f'Application crashed: {e}')
        raise
    finally:
        logger.info('Application shutdown')
