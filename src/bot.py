"""
Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the MIT License.

Description: initialize the app and listen for `message` activitys
"""

import os
import re

from botbuilder.core import MemoryStorage, TurnContext
from teams import Application, ApplicationOptions, TeamsAdapter
from teams.ai import AIOptions
from teams.ai.models import AzureOpenAIModelOptions, OpenAIModel, OpenAIModelOptions
from teams.ai.planners import ActionPlanner, ActionPlannerOptions
from teams.ai.prompts import PromptManager, PromptManagerOptions

from config import Config
from src.utils.ai_planner_wrapper import create_audit_planner
from state import AppTurnState
from stream.stream_helper import end_stream_handler
from tools.actions import (
    collect_info_from_user,
    create_ticket,
    display_jira_tickets,
    get_ticket_field_value,
    get_ticket_worklog,
    get_valid_status,
    insert_comment,
    retrieve_jira_tickets,
    search_user,
    update_ticket_status,
)
from tools.cards import on_jira_auth_action, on_submit
from tools.functions import today
from tools.messages import (
    display_help,
    get_bot_version,
    reset_history,
    schedule_reminder,
)
from tools.others import before_turn, on_error
from utils.openai_model_fixed import FixedOpenAIModel

# Import AI planner wrapper for response logging

# fix the complete_prompt function to prvent the "list index out of range" error
# if getattr(OpenAIModel, 'complete_prompt_origin', None) is None:
#     setattr(OpenAIModel, 'complete_prompt_origin', OpenAIModel.complete_prompt)
#     setattr(OpenAIModel, 'complete_prompt', open_ai_model_complete_prompt)


storage = MemoryStorage()

if Config.OPENAI_KEY is None and Config.AZURE_OPENAI_KEY is None:
    raise RuntimeError('Missing environment variables - please check that OPENAI_KEY or AZURE_OPENAI_KEY is set.')


# Create AI components
model: OpenAIModel

if Config.OPENAI_KEY:
    model = OpenAIModel(OpenAIModelOptions(api_key=Config.OPENAI_KEY, default_model='gpt-4o'))
elif Config.AZURE_OPENAI_KEY and Config.AZURE_OPENAI_ENDPOINT:
    model = FixedOpenAIModel(
        AzureOpenAIModelOptions(
            api_key=Config.AZURE_OPENAI_KEY,
            default_model='gpt-4o-standard',
            # api_version="2025-01-01-preview",
            api_version='2024-12-01-preview',
            endpoint=Config.AZURE_OPENAI_ENDPOINT,
            stream=True,
        )
    )

prompts = PromptManager(PromptManagerOptions(prompts_folder=f'{os.path.dirname(os.path.abspath(__file__))}/prompts'))

# Create the original planner
original_planner = ActionPlanner(
    ActionPlannerOptions(
        model=model,
        prompts=prompts,
        default_prompt='find-summarise-insert',
        start_streaming_message='Thinking...',
        end_stream_handler=end_stream_handler,
        enable_feedback_loop=False,
    )
)

# Wrap the planner with audit capabilities for AI response logging
planner = create_audit_planner(original_planner)

app = Application[AppTurnState](
    ApplicationOptions(
        bot_app_id=Config.APP_ID,
        storage=storage,
        adapter=TeamsAdapter(Config),
        ai=AIOptions(planner=planner, enable_feedback_loop=False),
    ),
)


@app.turn_state_factory
async def turn_state_factory(context: TurnContext):
    return await AppTurnState.load(context, storage)


############################################################################################################
############################################## MESSAGE HANDLER #############################################
############################################################################################################


# @app.conversation_update("membersAdded")
# async def on_members_added(context: TurnContext, state: AppTurnState):
#     for member in context.activity.members_added:
#         if member.id != context.activity.recipient.id:
#             await context.send_activity("Hello and welcome to the bot!")
#     return True

app.message('/version')(get_bot_version)
app.message('/clear')(reset_history)
app.message('/help')(display_help)
app.message(re.compile(r'^/schedule\s+\d{2}:\d{2}'))(schedule_reminder)


############################################################################################################
############################################### Adaptive Card ##############################################
############################################################################################################


app.adaptive_cards.action_submit('submit')(on_submit)
app.adaptive_cards.action_submit('action')(on_jira_auth_action)


############################################################################################################
################################################## ACTIONS #################################################
############################################################################################################


app.ai.action('CreateTicket')(create_ticket)
app.ai.action('InsertComment')(insert_comment)
app.ai.action('UpdateTicketStatus')(update_ticket_status)
app.ai.action('CollectInfoFromUser')(collect_info_from_user)
app.ai.action('GetValidStatus')(get_valid_status)
app.ai.action('GetTicketFieldValue')(get_ticket_field_value)
app.ai.action('GetTicketWorklog')(get_ticket_worklog)
app.ai.action('DisplayJiraTicket')(display_jira_tickets)
app.ai.action('SearchUser')(search_user)
app.ai.action('RetrieveJiraTickets')(retrieve_jira_tickets)


############################################################################################################
########################################### FUNCTION REGISTRATION ##########################################
############################################################################################################


prompts.function('today')(today)


############################################################################################################
################################################## OTHERS ##################################################
############################################################################################################


app.before_turn(before_turn)
app.error(on_error)
