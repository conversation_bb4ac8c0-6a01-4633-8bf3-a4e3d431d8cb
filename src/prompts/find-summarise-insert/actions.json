[{"name": "CreateTicket", "description": "Create a Jira ticket", "parameters": {"type": "object", "properties": {"summary": {"type": "string", "description": "The ticket summary"}, "description": {"type": "string", "description": "The ticket description"}}, "required": ["summary", "description"]}}, {"name": "InsertComment", "description": "Insert a comment to a Jira ticket", "parameters": {"type": "object", "properties": {"key": {"type": "string", "description": "The Jira ticket key"}, "comment": {"type": "string", "description": "The comment to insert"}, "mention": {"type": "string", "description": "The account id of the user to mention in the comment"}}, "required": ["key", "comment"]}}, {"name": "UpdateTicketStatus", "description": "Update the status of a Jira ticket", "parameters": {"type": "object", "properties": {"key": {"type": "string", "description": "The Jira ticket key"}, "status": {"type": "string", "description": "The status to update to"}}, "required": ["key", "status"]}}, {"name": "CollectInfoFromUser", "description": "Prompt an adaptive card for user to fill in the info needed to log time to a Jira ticket", "parameters": {"type": "object", "properties": {"key": {"type": "string", "description": "The Jira ticket key"}, "time_spent": {"type": "number", "description": "The time spent on the ticket (in hours)"}, "comment": {"type": "string", "description": "The work summary generated"}}, "required": ["key"]}}, {"name": "GetValidStatus", "description": "Get the valid status transitions for a Jira ticket", "parameters": {"type": "object", "properties": {"key": {"type": "string", "description": "The Jira ticket key"}}, "required": ["key"]}}, {"name": "GetTicketFieldValue", "description": "Get the value of a field for a Jira ticket", "parameters": {"type": "object", "properties": {"key": {"type": "string", "description": "The Jira ticket key"}, "field": {"type": "string", "description": "The field to get the value of"}}, "required": ["key", "field"]}}, {"name": "GetTicketWorklog", "description": "Get the worklog of a Jira ticket", "parameters": {"type": "object", "properties": {"key": {"type": "string", "description": "The Jira ticket key"}}, "required": ["key"]}}, {"name": "RetrieveJiraTickets", "description": "Get the tickets assigned to the user. Call this function when you need the info of the tickets but don't need to display them to the user.", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "The JQL query generated to retrieve tickets"}, "limit": {"type": "integer", "description": "The number of tickets to retrieve"}}, "required": ["query"]}}, {"name": "SearchUser", "description": "Get user info based on the user query", "parameters": {"type": "object", "properties": {"user_query": {"type": "string", "description": "The user query"}}, "required": ["user_query"]}}, {"name": "DisplayJiraTicket", "description": "Call this function whenever you need display a list of tickets to the user", "parameters": {"type": "object", "properties": {"issues": {"type": "array", "description": "The list of tickets to display", "items": {"type": "object", "properties": {"key": {"type": "string", "description": "The Jira ticket key"}, "summary": {"type": "string", "description": "The ticket summary"}, "description": {"type": "string", "description": "The ticket description"}, "status": {"type": "string", "description": "The ticket status"}}, "required": ["key", "summary", "description", "status"]}}}, "required": ["issues"]}}]