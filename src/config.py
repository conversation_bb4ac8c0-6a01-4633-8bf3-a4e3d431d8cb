"""
Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the MIT License.
"""

import os
import urllib.parse

from dotenv import load_dotenv

load_dotenv(override=True)


class Config:
    """Bot Configuration"""

    PORT = 3978
    APP_ID = os.environ['BOT_ID']
    APP_PASSWORD = os.environ['BOT_PASSWORD']
    BOT_ENDPOINT = os.environ.get('BOT_ENDPOINT', '')

    OPENAI_KEY = os.environ.get('OPENAI_KEY', '')
    AZURE_OPENAI_KEY = os.environ.get('AZURE_OPENAI_KEY', '')
    AZURE_OPENAI_ENDPOINT = os.environ.get('AZURE_OPENAI_ENDPOINT', '')

    JIRA_CLIENT_ID = os.environ.get('JIRA_CLIENT_ID', '')
    JIRA_CLIENT_SECRET = os.environ.get('SECRET_JIRA', '')
    JIRA_AUTHORIASATION_URI_BASE = 'https://auth.atlassian.com/authorize'
    JIRA_TOKEN_URI = 'https://auth.atlassian.com/oauth/token'
    JIRA_CLOUD_ID_URI = 'https://api.atlassian.com/oauth/token/accessible-resources'
    JIRA_SCOPES = 'read:jira-user read:jira-work offline_access'
    JIRA_BASE_URL = 'https://api.atlassian.com/ex/jira'

    JIRA_PROJECT_NAME = 'Business Intelligence Reporting & Insights'
    JIRA_PROJECT_KEY = 'BIRI'
    JIRA_RESULTS_LIMIT = 5

    # AZURE_CLIENT_ID = os.environ.get("AZURE_CLIENT_ID", "")
    # AZURE_CLIENT_SECRET = os.environ.get("AZURE_CLIENT_SECRET", "")
    # AZURE_TENANT_ID = os.environ.get("AZURE_TENANT_ID", "")

    DB_SERVER = os.environ.get('DB_SERVER', '10.3.0.50')
    DB_PORT = os.environ.get('DB_PORT', '1433')
    DB_NAME = os.environ.get('DB_NAME', 'FinanceDB')
    DB_USERNAME = os.environ.get('DB_USERNAME', '')
    DB_PASSWORD = os.environ.get('DB_PASSWORD', '')
    # Connection string for SQLAlchemy
    # DB_CONNECTION_STRING = (
    #     f'mssql+pyodbc://{DB_USERNAME}:{DB_PASSWORD}@{DB_SERVER}:{DB_PORT}/{DB_NAME}'
    #     '?driver=ODBC+Driver+17+for+SQL+Server'
    # )
    connection_string = urllib.parse.quote_plus(
        f'DRIVER={{ODBC Driver 17 for SQL Server}};'
        f'SERVER={DB_SERVER};'
        f'DATABASE={DB_NAME};'
        f'UID={DB_USERNAME};'
        f'PWD={DB_PASSWORD}'
    )
    DB_CONNECTION_STRING = f'mssql+pyodbc:///?odbc_connect={connection_string}'

    TIME_LOGGING_DEFAULT_TIME = '09:00'
    TIME_LOGGING_DEFAULT_DURATION = 1

    HELP_MESSAGE = "# Project Manager Bot - Help Guide 🤖\n<br/>Hello! I'm your Project Manager Bot, your AI assistant designed to help you manage your Jira tickets seamlessly within Microsoft Teams. Please Login before using me.<br/><br/>You can interact with me using natural language. Just tell me what you need regarding your Jira tickets, or use the commands listed below.<br/><br/>What I Can Do For You:\r\n- Summarize Your Work: Need a quick record? I can summarize the work you have done.\n\t- *Example: \"I've been working on the login feature for the chat bot.\"*\n- Categorize Tasks: I can help categorize your work based on the tickets you're involved with into the following categories.\n\t- Requirement Clarification (RC)\n\t- Dataset Preparation (DP)\n\t- Data Analysis (DA)\n\t- Dashboard Creation (DC)\n\t- Error and Issue Handling (EH)\n- Log Time Effortlessly: Simply tell me how much time you spent on a task, and I'll log it to the relevant Jira ticket for you.\n\t- *Example: \"Log 2 hours on the 'Develop login feature' ticket.\"*\n- Find Ticket Information: Ask me about specific tickets or their details (like status, assignee, description). I can query Jira based on our conversation.\n\t- *Example: \"What's the status of the ticket about the UI bug?\"*\n- Add Comments (with mentions!): Need to add a quick note or update? Dictate your comment to me, and I'll add it to the specified Jira ticket. You can even mention other team members in your comment by putting his name in a pair of square brackets, and you don't even need to user the full name!\n\t- *Example: \"Add a comment to TKT-123 saying the testing is complete, mentioning [Phillip H].\"*\n- Smart Ticket Retrieval: Describe the work you've done, and I'll intelligently find and suggest the most relevant Jira ticket to associate it with.\n\t- *Example: \"I've completed the backend for the login feature.\"*\n\t- *Example: \"List 5 tickets of mine with highest priority.\"*\n- Manage Sprint Tickets: Move tickets through different statuses or stages within your current sprint board directly from Teams.\n\t- *Example: \"Move the 'API integration' ticket to 'In Progress'.\"*\r\n\r\nBuilt-in Commands:\r\n- /clear: Clears our current conversation history, giving us a fresh start.\n- /version: Displays my current version number and any recent update information.\r\n- /schedule HH:MM: Sets a reminder for the specified time, every weekday. 24 hours format only.\n- /help: Shows this help message again.\r\n\r\nJust start chatting, and let me help streamline your Jira workflow!"
    REMIND_MESSAGE = (
        'Hi there! Just wanted to check in and see how things are going. Is there anything I can help you with today?'
    )
