aiohappyeyeballs==2.6.1 ; python_version >= "3.9" and python_version < "3.11"
aiohttp==3.10.5 ; python_version >= "3.9" and python_version < "3.11"
aioodbc==0.5.0 ; python_version >= "3.9" and python_version < "3.11"
aiosignal==1.3.2 ; python_version >= "3.9" and python_version < "3.11"
aiounittest==1.3.0 ; python_version >= "3.9" and python_version < "3.11"
annotated-types==0.7.0 ; python_version >= "3.9" and python_version < "3.11"
anyio==4.9.0 ; python_version >= "3.9" and python_version < "3.11"
apscheduler==3.11.0 ; python_version >= "3.9" and python_version < "3.11"
asttokens==3.0.0 ; python_version >= "3.9" and python_version < "3.11"
async-timeout==4.0.3 ; python_version >= "3.9" and python_version < "3.11"
atlassian-python-api==4.0.3 ; python_version >= "3.9" and python_version < "3.11"
attrs==25.3.0 ; python_version >= "3.9" and python_version < "3.11"
azure-ai-contentsafety==1.0.0 ; python_version >= "3.9" and python_version < "3.11"
azure-core==1.33.0 ; python_version >= "3.9" and python_version < "3.11"
babel==2.9.1 ; python_version >= "3.9" and python_version < "3.11"
beautifulsoup4==4.13.3 ; python_version >= "3.9" and python_version < "3.11"
botbuilder-core==4.16.2 ; python_version >= "3.9" and python_version < "3.11"
botbuilder-dialogs==4.16.2 ; python_version >= "3.9" and python_version < "3.11"
botbuilder-integration-aiohttp==4.16.2 ; python_version >= "3.9" and python_version < "3.11"
botbuilder-schema==4.16.2 ; python_version >= "3.9" and python_version < "3.11"
botframework-connector==4.16.2 ; python_version >= "3.9" and python_version < "3.11"
botframework-streaming==4.16.2 ; python_version >= "3.9" and python_version < "3.11"
certifi==2025.1.31 ; python_version >= "3.9" and python_version < "3.11"
cffi==1.17.1 ; python_version >= "3.9" and python_version < "3.11" and platform_python_implementation != "PyPy"
charset-normalizer==3.4.1 ; python_version >= "3.9" and python_version < "3.11"
colorama==0.4.6 ; python_version >= "3.9" and python_version < "3.11"
cryptography==43.0.3 ; python_version >= "3.9" and python_version < "3.11"
dataclasses-json==0.6.7 ; python_version >= "3.9" and python_version < "3.11"
datedelta==1.4 ; python_version >= "3.9" and python_version < "3.11"
defusedxml==0.7.1 ; python_version >= "3.9" and python_version < "3.11"
deprecated==1.2.18 ; python_version >= "3.9" and python_version < "3.11"
distro==1.9.0 ; python_version >= "3.9" and python_version < "3.11"
emoji==1.7.0 ; python_version >= "3.9" and python_version < "3.11"
exceptiongroup==1.2.2 ; python_version >= "3.9" and python_version < "3.11"
executing==2.2.0 ; python_version >= "3.9" and python_version < "3.11"
frozenlist==1.5.0 ; python_version >= "3.9" and python_version < "3.11"
grapheme==0.6.0 ; python_version >= "3.9" and python_version < "3.11"
greenlet==3.2.2 ; python_version >= "3.9" and python_version < "3.11" and (platform_machine == "aarch64" or platform_machine == "ppc64le" or platform_machine == "x86_64" or platform_machine == "amd64" or platform_machine == "AMD64" or platform_machine == "win32" or platform_machine == "WIN32")
h11==0.14.0 ; python_version >= "3.9" and python_version < "3.11"
httpcore==1.0.8 ; python_version >= "3.9" and python_version < "3.11"
httpx==0.28.1 ; python_version >= "3.9" and python_version < "3.11"
icecream==2.1.4 ; python_version >= "3.9" and python_version < "3.11"
idna==3.10 ; python_version >= "3.9" and python_version < "3.11"
isodate==0.7.2 ; python_version >= "3.9" and python_version < "3.11"
jira==3.8.0 ; python_version >= "3.9" and python_version < "3.11"
jiter==0.9.0 ; python_version >= "3.9" and python_version < "3.11"
jmespath==1.0.1 ; python_version >= "3.9" and python_version < "3.11"
jsonpickle==1.4.2 ; python_version >= "3.9" and python_version < "3.11"
jsonschema-specifications==2024.10.1 ; python_version >= "3.9" and python_version < "3.11"
jsonschema==4.23.0 ; python_version >= "3.9" and python_version < "3.11"
loguru==0.7.3 ; python_version >= "3.9" and python_version < "3.11"
marshmallow==3.26.1 ; python_version >= "3.9" and python_version < "3.11"
msal==1.32.0 ; python_version >= "3.9" and python_version < "3.11"
msrest==0.7.1 ; python_version >= "3.9" and python_version < "3.11"
multidict==6.4.3 ; python_version >= "3.9" and python_version < "3.11"
multipledispatch==1.0.0 ; python_version >= "3.9" and python_version < "3.11"
mypy-extensions==1.0.0 ; python_version >= "3.9" and python_version < "3.11"
oauthlib==3.2.2 ; python_version >= "3.9" and python_version < "3.11"
openai==1.74.0 ; python_version >= "3.9" and python_version < "3.11"
packaging==24.2 ; python_version >= "3.9" and python_version < "3.11"
pendulum==3.0.0 ; python_version >= "3.9" and python_version < "3.11"
pillow==11.1.0 ; python_version >= "3.9" and python_version < "3.11"
propcache==0.3.1 ; python_version >= "3.9" and python_version < "3.11"
pycparser==2.22 ; python_version >= "3.9" and python_version < "3.11" and platform_python_implementation != "PyPy"
pydantic-core==2.33.1 ; python_version >= "3.9" and python_version < "3.11"
pydantic==2.11.3 ; python_version >= "3.9" and python_version < "3.11"
pygments==2.19.1 ; python_version >= "3.9" and python_version < "3.11"
pyjwt==2.10.1 ; python_version >= "3.9" and python_version < "3.11"
pymssql==2.3.4 ; python_version >= "3.9" and python_version < "3.11"
pyodbc==5.2.0 ; python_version >= "3.9" and python_version < "3.11"
python-dateutil==2.9.0.post0 ; python_version >= "3.9" and python_version < "3.11"
python-dotenv==1.0.1 ; python_version >= "3.9" and python_version < "3.11"
pytz==2025.2 ; python_version >= "3.9" and python_version < "3.11"
pyyaml==6.0.2 ; python_version >= "3.9" and python_version < "3.11"
recognizers-text-choice==1.0.2a2 ; python_version >= "3.9" and python_version < "3.11"
recognizers-text-date-time==1.0.2a2 ; python_version >= "3.9" and python_version < "3.11"
recognizers-text-number-with-unit==1.0.2a2 ; python_version >= "3.9" and python_version < "3.11"
recognizers-text-number==1.0.2a2 ; python_version >= "3.9" and python_version < "3.11"
recognizers-text==1.0.2a2 ; python_version >= "3.9" and python_version < "3.11"
referencing==0.36.2 ; python_version >= "3.9" and python_version < "3.11"
regex==2024.11.6 ; python_version >= "3.9" and python_version < "3.11"
requests-oauthlib==2.0.0 ; python_version >= "3.9" and python_version < "3.11"
requests-toolbelt==1.0.0 ; python_version >= "3.9" and python_version < "3.11"
requests==2.32.3 ; python_version >= "3.9" and python_version < "3.11"
rpds-py==0.24.0 ; python_version >= "3.9" and python_version < "3.11"
six==1.17.0 ; python_version >= "3.9" and python_version < "3.11"
sniffio==1.3.1 ; python_version >= "3.9" and python_version < "3.11"
soupsieve==2.6 ; python_version >= "3.9" and python_version < "3.11"
sqlalchemy==2.0.41 ; python_version >= "3.9" and python_version < "3.11"
teams-ai==1.8.0 ; python_version >= "3.9" and python_version < "3.11"
tiktoken==0.9.0 ; python_version >= "3.9" and python_version < "3.11"
tqdm==4.67.1 ; python_version >= "3.9" and python_version < "3.11"
types-pyyaml==6.0.12.20250402 ; python_version >= "3.9" and python_version < "3.11"
typing-extensions==4.12.2 ; python_version >= "3.9" and python_version < "3.11"
typing-inspect==0.9.0 ; python_version >= "3.9" and python_version < "3.11"
typing-inspection==0.4.0 ; python_version >= "3.9" and python_version < "3.11"
tzdata==2025.1 ; python_version >= "3.9" and python_version < "3.11"
tzlocal==5.3.1 ; python_version >= "3.9" and python_version < "3.11"
urllib3==2.3.0 ; python_version >= "3.9" and python_version < "3.11"
win32-setctime==1.2.0 ; python_version >= "3.9" and python_version < "3.11" and sys_platform == "win32"
wrapt==1.17.2 ; python_version >= "3.9" and python_version < "3.11"
yarl==1.19.0 ; python_version >= "3.9" and python_version < "3.11"
