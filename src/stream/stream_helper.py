import asyncio
import time
from typing import Optional

from botbuilder.core import Turn<PERSON>ontext
from loguru import logger
from teams.ai.models import PromptResponse
from teams.state import MemoryBase
from teams.streaming import StreamingResponse

from src.utils.ai_response_logger import ai_response_logger
from src.utils.logging_config import Logging<PERSON><PERSON>x<PERSON>


def end_stream_handler(
    context: TurnContext,
    state: MemoryBase,
    response: PromptResponse[str],
    streamer: StreamingResponse,
):
    """
    Enhanced stream handler that captures AI responses for logging.

    This handler is called when AI streaming completes and logs the
    final AI-generated response to our comprehensive logging system.
    """
    if not streamer:
        return

    # Extract the final streamed message
    final_message = getattr(streamer, 'message', None) or ''

    # Get session information from state if available
    session_id = None
    try:
        # Try to get session ID from various possible locations in state
        conversation = getattr(state, 'conversation', None)
        if conversation and hasattr(conversation, 'get'):
            session_id = conversation.get('session_id')
        elif hasattr(state, 'get'):
            session_id = state.get('session_id')  # type: ignore
    except (AttributeError, TypeError):
        # Fallback if state structure is different
        pass

    # Calculate streaming duration if available
    duration_ms = None
    try:
        temp_data = getattr(state, 'temp', {})
        if hasattr(temp_data, 'get'):
            start_time = temp_data.get('stream_start_time')
            if start_time:
                duration_ms = (time.time() - start_time) * 1000
    except (AttributeError, TypeError):
        # Fallback if temp structure is different
        pass

    # Log the AI streaming response asynchronously
    if final_message and context:
        # Create a task to log the response without blocking
        asyncio.create_task(
            _log_streaming_response(
                context=context,
                final_content=final_message,
                session_id=session_id,
                duration_ms=duration_ms,
            )
        )

    # ic(streamer.message)

    # card = CardFactory.adaptive_card(
    #     {
    #         "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",
    #         "version": "1.6",
    #         "type": "AdaptiveCard",
    #         "body": [{"type": "TextBlock", "wrap": True, "text": streamer.message}],
    #     }
    # )

    # streamer.set_attachments([card])


async def _log_streaming_response(
    context: TurnContext,
    final_content: str,
    session_id: Optional[str] = None,
    duration_ms: Optional[float] = None,
) -> None:
    """
    Log a streaming AI response asynchronously.

    Args:
        context: The turn context
        final_content: The complete streamed response
        session_id: Optional session ID for tracking
        duration_ms: Total streaming duration
    """
    try:
        # Log with structured context
        with LoggingContext(
            operation='ai_streaming_complete',
            session_id=session_id,
            content_length=len(final_content),
        ):
            duration_str = f'{duration_ms:.3f}ms' if duration_ms is not None else 'unknown duration'
            logger.info(f'AI streaming completed: {len(final_content)} characters in {duration_str}')

        # Log the streaming response
        await ai_response_logger.log_streaming_ai_response(
            context=context,
            final_content=final_content,
            session_id=session_id,
            duration_ms=duration_ms,
        )

    except Exception as e:
        logger.warning(f'Failed to log streaming AI response: {e}')
        # Don't re-raise to avoid breaking the streaming flow
