"""
Issue model for Jira issues.
"""

from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional


@dataclass
class User:
    """Represents a Jira user."""

    account_id: str
    email_address: str
    display_name: str

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'User':
        """Create a User instance from a dictionary."""
        return cls(
            account_id=data.get('accountId', ''),
            email_address=data.get('emailAddress', ''),
            display_name=data.get('displayName', ''),
        )


@dataclass
class StatusCategory:
    """Represents a Jira status category."""

    id: Optional[int] = None
    name: str = ''

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StatusCategory':
        """Create a StatusCategory instance from a dictionary."""
        return cls(id=data.get('id'), name=data.get('name', ''))


@dataclass
class Status:
    """Represents a Jira issue status."""

    name: str
    id: str
    status_category: StatusCategory

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Status':
        """Create a Status instance from a dictionary."""
        return cls(
            name=data.get('name', ''),
            id=data.get('id', ''),
            status_category=StatusCategory.from_dict(data.get('statusCategory', {})),
        )


@dataclass
class IssueType:
    """Represents a Jira issue type."""

    name: str
    subtask: bool

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'IssueType':
        """Create an IssueType instance from a dictionary."""
        return cls(name=data.get('name', ''), subtask=data.get('subtask', False))


@dataclass
class TimeTracking:
    """Represents time tracking information for a Jira issue."""

    remaining_estimate: str
    time_spent: str
    remaining_estimate_seconds: int
    time_spent_seconds: int

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TimeTracking':
        """Create a TimeTracking instance from a dictionary."""
        return cls(
            remaining_estimate=data.get('remainingEstimate', ''),
            time_spent=data.get('timeSpent', ''),
            remaining_estimate_seconds=data.get('remainingEstimateSeconds', 0),
            time_spent_seconds=data.get('timeSpentSeconds', 0),
        )


@dataclass
class Project:
    """Represents a Jira project."""

    id: str
    key: str
    name: str

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Project':
        """Create a Project instance from a dictionary."""
        return cls(id=data.get('id', ''), key=data.get('key', ''), name=data.get('name', ''))


@dataclass
class Priority:
    """Represents a Jira priority."""

    name: str
    id: str

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Priority':
        """Create a Priority instance from a dictionary."""
        return cls(name=data.get('name', ''), id=data.get('id', ''))


@dataclass
class Progress:
    """Represents progress information for a Jira issue."""

    progress: int
    total: int
    percent: int

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Progress':
        """Create a Progress instance from a dictionary."""
        return cls(progress=data.get('progress', 0), total=data.get('total', 0), percent=data.get('percent', 0))


@dataclass
class Comment:
    """Represents a comment section for a Jira issue."""

    comments: List[Dict[str, Any]] = field(default_factory=list)
    total: int = 0
    start_at: int = 0

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Comment':
        """Create a Comment instance from a dictionary."""
        return cls(comments=data.get('comments', []), total=data.get('total', 0), start_at=data.get('startAt', 0))


@dataclass
class Worklog:
    """Represents a worklog entry for a Jira issue."""

    author: User
    comment: str
    created: str
    updated: str
    started: str
    time_spent: str
    time_spent_seconds: int
    id: str
    issue_id: str

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Worklog':
        """Create a Worklog instance from a dictionary."""
        return cls(
            author=User.from_dict(data.get('author', {})),
            comment=data.get('comment', ''),
            created=data.get('created', ''),
            updated=data.get('updated', ''),
            started=data.get('started', ''),
            time_spent=data.get('timeSpent', ''),
            time_spent_seconds=data.get('timeSpentSeconds', 0),
            id=data.get('id', ''),
            issue_id=data.get('issueId', ''),
        )


@dataclass
class CustomField:
    """Represents a custom field value."""

    id: Any
    value: str

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> Optional['CustomField']:
        """Create a CustomField instance from a dictionary."""
        if not data:
            return None
        return cls(id=data.get('id'), value=data.get('value', ''))


@dataclass
class Issue:
    """Represents a Jira issue."""

    id: str
    key: str
    status_category: StatusCategory
    issue_links: List[Dict[str, Any]]
    assignee: User
    subtasks: List[Dict[str, Any]]
    worklogs: List[Worklog]
    issue_type: IssueType
    time_tracking: TimeTracking
    due_date: Optional[str]
    status: Status
    creator: User
    custom_field_10063: Optional[CustomField]
    reporter: User
    project: Project
    updated: str
    time_original_estimate: Optional[str]
    description: str
    summary: str
    comment: Comment
    status_category_change_date: str
    priority: Priority
    aggregate_progress: Progress
    created: str

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Issue':
        """Create an Issue instance from a dictionary."""
        # Handle direct mapping case (sample_object.py format)
        if 'fields' not in data:
            return cls(
                id=data.get('id', ''),
                key=data.get('key', ''),
                status_category=StatusCategory.from_dict(data.get('statusCategory', {})),
                issue_links=data.get('issuelinks', []),
                assignee=User.from_dict(data.get('assignee', {})),
                subtasks=data.get('subtasks', []),
                worklogs=[Worklog.from_dict(wl) for wl in data.get('worklogs', [])],
                issue_type=IssueType.from_dict(data.get('issuetype', {})),
                time_tracking=TimeTracking.from_dict(data.get('timetracking', {})),
                due_date=data.get('duedate'),
                status=Status.from_dict(data.get('status', {})),
                creator=User.from_dict(data.get('creator', {})),
                custom_field_10063=CustomField.from_dict(data.get('customfield_10063', {})),
                reporter=User.from_dict(data.get('reporter', {})),
                project=Project.from_dict(data.get('project', {})),
                updated=data.get('updated', ''),
                time_original_estimate=data.get('timeoriginalestimate'),
                description=data.get('description', ''),
                summary=data.get('summary', ''),
                comment=Comment.from_dict(data.get('comment', {})),
                status_category_change_date=data.get('statuscategorychangedate', ''),
                priority=Priority.from_dict(data.get('priority', {})),
                aggregate_progress=Progress.from_dict(data.get('aggregateprogress', {})),
                created=data.get('created', ''),
            )

        # Handle Jira API response format (sample_issue.py format)
        fields = data.get('fields', {})
        return cls(
            id=data.get('id', ''),
            key=data.get('key', ''),
            status_category=StatusCategory.from_dict(fields.get('statusCategory', {})),
            issue_links=fields.get('issuelinks', []),
            assignee=User.from_dict(fields.get('assignee', {})),
            subtasks=fields.get('subtasks', []),
            worklogs=[Worklog.from_dict(wl) for wl in fields.get('worklog', {}).get('worklogs', [])],
            issue_type=IssueType.from_dict(fields.get('issuetype', {})),
            time_tracking=TimeTracking.from_dict(fields.get('timetracking', {})),
            due_date=fields.get('duedate'),
            status=Status.from_dict(fields.get('status', {})),
            creator=User.from_dict(fields.get('creator', {})),
            custom_field_10063=CustomField.from_dict(fields.get('customfield_10063', {})),
            reporter=User.from_dict(fields.get('reporter', {})),
            project=Project.from_dict(fields.get('project', {})),
            updated=fields.get('updated', ''),
            time_original_estimate=fields.get('timeoriginalestimate'),
            description=fields.get('description', ''),
            summary=fields.get('summary', ''),
            comment=Comment.from_dict(fields.get('comment', {})),
            status_category_change_date=fields.get('statuscategorychangedate', ''),
            priority=Priority.from_dict(fields.get('priority', {})),
            aggregate_progress=Progress.from_dict(fields.get('aggregateprogress', {})),
            created=fields.get('created', ''),
        )

    @classmethod
    def from_jira_api_response(cls, data: Dict[str, Any]) -> 'Issue':
        """Create an Issue instance from a Jira API response."""
        return cls.from_dict(data)

    def to_dict(self) -> Dict[str, Any]:
        """Convert the Issue instance to a dictionary."""
        return {
            'id': self.id,
            'key': self.key,
            'statusCategory': {
                'name': self.status_category.name,
            },
            'issuelinks': self.issue_links,
            'assignee': {
                'accountId': self.assignee.account_id,
                'emailAddress': self.assignee.email_address,
                'displayName': self.assignee.display_name,
            },
            'subtasks': self.subtasks,
            'worklogs': [
                {
                    'author': {
                        'accountId': wl.author.account_id,
                        'emailAddress': wl.author.email_address,
                        'displayName': wl.author.display_name,
                    },
                    'comment': wl.comment,
                    'created': wl.created,
                    'updated': wl.updated,
                    'started': wl.started,
                    'timeSpent': wl.time_spent,
                    'timeSpentSeconds': wl.time_spent_seconds,
                    'id': wl.id,
                    'issueId': wl.issue_id,
                }
                for wl in self.worklogs
            ],
            'issuetype': {
                'name': self.issue_type.name,
                'subtask': self.issue_type.subtask,
            },
            'timetracking': {
                'remainingEstimate': self.time_tracking.remaining_estimate,
                'timeSpent': self.time_tracking.time_spent,
                'remainingEstimateSeconds': self.time_tracking.remaining_estimate_seconds,
                'timeSpentSeconds': self.time_tracking.time_spent_seconds,
            },
            'duedate': self.due_date,
            'status': {
                'name': self.status.name,
                'id': self.status.id,
                'statusCategory': {
                    'id': self.status.status_category.id,
                    'name': self.status.status_category.name,
                },
            },
            'creator': {
                'accountId': self.creator.account_id,
                'emailAddress': self.creator.email_address,
                'displayName': self.creator.display_name,
            },
            'customfield_10063': {'id': self.custom_field_10063.id, 'value': self.custom_field_10063.value}
            if self.custom_field_10063
            else None,
            'reporter': {
                'accountId': self.reporter.account_id,
                'emailAddress': self.reporter.email_address,
                'displayName': self.reporter.display_name,
            },
            'project': {
                'id': self.project.id,
                'key': self.project.key,
                'name': self.project.name,
            },
            'updated': self.updated,
            'timeoriginalestimate': self.time_original_estimate,
            'description': self.description,
            'summary': self.summary,
            'comment': {
                'comments': self.comment.comments,
                'total': self.comment.total,
                'startAt': self.comment.start_at,
            },
            'statuscategorychangedate': self.status_category_change_date,
            'priority': {
                'name': self.priority.name,
                'id': self.priority.id,
            },
            'aggregateprogress': {
                'progress': self.aggregate_progress.progress,
                'total': self.aggregate_progress.total,
                'percent': self.aggregate_progress.percent,
            },
            'created': self.created,
        }
