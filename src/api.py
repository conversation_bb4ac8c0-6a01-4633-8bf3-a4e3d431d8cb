"""
Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the MIT License.

Description: initialize the api and route incoming messages
to our app
"""

import asyncio
import time
import traceback
from http import HTTPStatus

from aiohttp import web
from botbuilder.core.integration import aiohttp_error_middleware
from loguru import logger

from auth.jira_auth import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from bot import app

# Audit imports
from services.enhanced_audit_service import PrivacyConfig
from tools.cards import create_auth_success_card
from utils.enhanced_audit_middleware import create_enhanced_audit_middleware
from utils.logging_config import LoggingContext
from utils.scheduler import Scheduler

routes = web.RouteTableDef()

# Import audit services here to avoid circular imports
from src.services.audit_service import AuditService
from src.utils.audit_middleware import setup_audit_logging
from src.utils.sql.models import AuditStatus

# Initialize audit service
audit_service = AuditService()


@routes.post('/api/messages')
async def on_messages(req: web.Request) -> web.Response:
    request_id = f'req_{int(time.time() * 1000)}'

    # Log the incoming request
    logger.info(f'Received message request [id={request_id}]')
    logger.debug(f'Request headers: {dict(req.headers)}')

    start_time = time.time()

    try:
        # Process the request with context
        with LoggingContext(request_id=request_id, endpoint='/api/messages'):
            res = await app.process(req)

            # Log the response
            process_time = time.time() - start_time
            logger.info(f'Processed message request [id={request_id}] in {process_time:.3f}s')

            if res is not None:
                return res

            return web.Response(status=HTTPStatus.OK)
    except Exception as e:
        # Log any exceptions
        process_time = time.time() - start_time
        logger.error(f'Error processing message request [id={request_id}] after {process_time:.3f}s: {str(e)}')
        logger.exception(e)
        # Re-raise to let the middleware handle it
        raise


@routes.get('/auth/jira/callback')
async def auth_jira_callback(req: web.Request) -> web.Response:
    # Get query parameters
    params = req.query
    code = params.get('code')
    auth_state = params.get('state')

    logger.info(f'Received OAuth callback with state: {auth_state}')

    if not code or not auth_state:
        logger.warning(
            f'Missing required parameters. Code: {"present" if code else "missing"}, State: {"present" if auth_state else "missing"}'
        )
        return web.Response(text='Missing required parameters', status=400)

    try:
        # Extract user name from auth_state for the success page
        user_parts = auth_state.split('|')
        if len(user_parts) != 2:
            logger.error(f'Invalid auth_state format: {auth_state}')
            return web.Response(text='Invalid authentication state format', status=400)

        user_id, user_name = user_parts
        logger.info(f'Processing OAuth callback for user: {user_name} (ID: {user_id})')

        # Process the callback
        logger.debug('Calling JiraAuthManager.handle_callback')
        await JiraAuthManager.handle_callback(code=code, auth_state=auth_state)
        logger.info(f'OAuth callback processed successfully for user: {user_name}')

        # Return styled success page
        logger.debug('Creating success page')
        success_html = create_auth_success_card(user_name)
        return web.Response(
            text=success_html,
            content_type='text/html',
        )

    except ValueError as e:
        logger.error(f'Authentication value error: {str(e)}')
        return web.Response(text=f'Authentication failed: {str(e)}', status=400)
    except Exception as e:
        logger.error(f'Error in auth callback: {e}')
        logger.error(f'Error traceback: {traceback.format_exc()}')
        return web.Response(text='An error occurred during authentication', status=500)


# Create audit middleware
audit_middleware = create_enhanced_audit_middleware(PrivacyConfig())

# Create application with audit middleware
api = web.Application(middlewares=[aiohttp_error_middleware, audit_middleware])
api.add_routes(routes)


async def start_scheduler():
    logger.info('Starting scheduler')
    try:
        Scheduler.get_instance().start()
        logger.info('Scheduler started successfully')
    except Exception as e:
        logger.error(f'Failed to start scheduler: {e}')
        logger.exception(e)
        raise


async def on_startup(app_instance: web.Application):
    logger.info('Application startup initiated')

    # Setup audit logging
    try:
        setup_audit_logging()
        logger.info('Audit logging configured successfully')
    except Exception as e:
        logger.error(f'Error setting up audit logging: {e}')
        logger.exception(e)

    # Initialize database tables if needed
    from utils.sql.init_db import init_database

    try:
        logger.info('Initializing database')
        success = init_database()
        if success:
            logger.info('Database initialized successfully')

            # Log system startup event
            await audit_service.log_system_event(
                event_description='Bot framework application started successfully',
                event_status=AuditStatus.SUCCESS,
                parameters={'startup_time': time.time()},
            )
        else:
            logger.warning('Database initialization may not have completed successfully')
            await audit_service.log_system_event(
                event_description='Database initialization completed with warnings',
                event_status=AuditStatus.WARNING,
            )
    except Exception as e:
        logger.error(f'Error initializing database: {e}')
        logger.exception(e)

        # Log system startup error
        await audit_service.log_system_event(
            event_description='Database initialization failed during startup',
            event_status=AuditStatus.FAILURE,
            error_message=str(e),
        )

    # Start the scheduler
    asyncio.create_task(start_scheduler())

    logger.info('Application startup completed')


api.on_startup.append(on_startup)
