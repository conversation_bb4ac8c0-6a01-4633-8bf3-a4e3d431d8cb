"""
Database connection manager for SQL operations.
"""

import threading
import time
import traceback
from contextlib import contextmanager
from typing import Generator, Optional

from loguru import logger
from sqlalchemy import create_engine
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Session, sessionmaker

from config import Config


class DatabaseConnectionManager:
    """
    Singleton class for managing database connections.

    This class provides a centralized way to manage database connections
    and sessions using SQLAlchemy. It follows the singleton pattern to
    ensure only one instance exists throughout the application.
    """

    _instance: Optional['DatabaseConnectionManager'] = None
    _lock = threading.Lock()
    _engine: Optional[Engine] = None
    _session_factory = None

    def __new__(cls) -> 'DatabaseConnectionManager':
        """Create a new instance if one doesn't exist."""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(DatabaseConnectionManager, cls).__new__(cls)
                cls._instance._initialize()
        return cls._instance

    def _initialize(self) -> None:
        """Initialize the database connection."""
        try:
            logger.info('Initializing database connection')

            # Create engine with connection pooling settings
            self._engine = create_engine(
                Config.DB_CONNECTION_STRING,
                pool_pre_ping=True,  # Check connection before using it
                pool_recycle=3600,  # Recycle connections after 1 hour
                pool_size=5,  # Maximum number of connections to keep
                max_overflow=10,  # Maximum number of connections to create beyond pool_size
                echo=False,  # Set to True for SQLAlchemy's built-in SQL query logging
            )

            # Create session factory
            self._session_factory = sessionmaker(
                bind=self._engine,
                autocommit=False,
                autoflush=False,
            )

            logger.info('Database connection initialized successfully')
            logger.debug(f'Database: {Config.DB_NAME} on {Config.DB_SERVER}')

        except Exception as e:
            logger.error(f'Failed to initialize database connection: {e}')
            logger.debug(f'Connection error traceback:\n{traceback.format_exc()}')
            raise

    @property
    def engine(self) -> Engine:
        """Get the SQLAlchemy engine."""
        if self._engine is None:
            self._initialize()
        assert self._engine is not None, 'Engine initialization failed'
        return self._engine

    def get_session(self) -> Session:
        """
        Get a new SQLAlchemy session.

        Returns:
            Session: A new SQLAlchemy session

        Note:
            The caller is responsible for closing the session.
            Use with a context manager for automatic cleanup:

            ```python
            with db_manager.get_session() as session:
                # Use session here
            ```

            Or better, use the session_scope method:

            ```python
            with db_manager.session_scope() as session:
                # Use session here
            ```
        """
        if self._session_factory is None:
            logger.debug('Initializing session factory')
            self._initialize()

        assert self._session_factory is not None, 'Session factory initialization failed'
        logger.debug('Creating new database session')
        return self._session_factory()

    @contextmanager
    def session_scope(self) -> Generator[Session, None, None]:
        """
        Provide a transactional scope around a series of operations.

        This context manager handles session creation, commit, and cleanup.

        Yields:
            Session: A SQLAlchemy session

        Example:
            ```python
            with db_manager.session_scope() as session:
                # Use session here
                # No need to commit or handle exceptions
            ```
        """
        session = self.get_session()
        operation_id = f'db_{int(time.time() * 1000)}'

        try:
            logger.debug(f'Starting database transaction [id={operation_id}]')
            start_time = time.time()

            yield session

            # Commit the transaction
            session.commit()
            duration = time.time() - start_time
            logger.debug(f'Committed database transaction [id={operation_id}] in {duration:.3f}s')

        except Exception as e:
            # Roll back the transaction on error
            session.rollback()
            duration = time.time() - start_time
            logger.error(f'Rolling back database transaction [id={operation_id}] after {duration:.3f}s: {str(e)}')
            logger.debug(f'Transaction error traceback [id={operation_id}]:\n{traceback.format_exc()}')
            raise

        finally:
            # Close the session
            session.close()
            logger.debug(f'Closed database session [id={operation_id}]')

    def dispose(self) -> None:
        """Dispose of the engine and all connections."""
        if self._engine:
            self._engine.dispose()
            logger.info('Database connections disposed')
