"""
<PERSON><PERSON><PERSON> to initialize database tables.

This script creates the necessary database tables for the application.
Run this script before starting the application for the first time.
"""

from loguru import logger

from utils.sql.db_connection import DatabaseConnectionManager
from utils.sql.models import create_tables


def init_database():
    """Initialize the database by creating all tables."""
    try:
        logger.info('Initializing database tables')

        # Get database connection
        db_manager = DatabaseConnectionManager()

        # Create existing tables
        create_tables(db_manager.engine)

        # Run audit migration
        logger.info('Running audit tables migration')
        try:
            from src.utils.sql.audit_migration import run_audit_migration

            audit_success = run_audit_migration()
            if not audit_success:
                logger.warning('Audit tables migration failed, but continuing with main initialization')
        except ImportError as e:
            logger.warning(f'Could not import audit migration: {e}')
        except Exception as e:
            logger.warning(f'Audit migration failed: {e}')

        logger.info('Database initialization completed successfully')
        return True
    except Exception as e:
        logger.error(f'Failed to initialize database: {e}')
        return False


if __name__ == '__main__':
    # When run directly, initialize the database
    success = init_database()
    if success:
        print('Database initialized successfully')
    else:
        print('Failed to initialize database')
