"""
SQLAlchemy models for database tables.
"""

import datetime
import traceback
from enum import Enum

from loguru import logger
from sqlalchemy import Column, DateTime, Float, Index, Integer, MetaData, String, Text
from sqlalchemy.orm import declarative_base

# Create a metadata object with the specified schema
metadata = MetaData(schema='model')

# Create a base class for declarative models
Base = declarative_base(metadata=metadata)


class AuditEventType(Enum):
    """Enumeration of audit event types."""

    USER_MESSAGE = 'user_message'
    BOT_RESPONSE = 'bot_response'
    ACTION_EXECUTION = 'action_execution'
    AUTHENTICATION = 'authentication'
    ERROR = 'error'
    SYSTEM_EVENT = 'system_event'
    DATABASE_OPERATION = 'database_operation'
    API_CALL = 'api_call'


class AuditStatus(Enum):
    """Enumeration of audit event statuses."""

    SUCCESS = 'success'
    FAILURE = 'failure'
    WARNING = 'warning'
    IN_PROGRESS = 'in_progress'


# Function to return current Sydney time for default values
def get_sydney_now():
    """Return current Sydney time for default values in models."""
    import pytz

    sydney_tz = pytz.timezone('Australia/Sydney')
    return datetime.datetime.now(sydney_tz)


class PMATokens(Base):
    """
    Model for storing user authentication tokens.

    This table stores authentication tokens for users in the 'model' schema.
    """

    __tablename__ = 'PMATokens'

    # Primary key
    user_id = Column(String(255), primary_key=True, nullable=False)

    # User information
    user_name = Column(String(255), nullable=True)

    # Authentication tokens
    auth_token = Column(String(4000), nullable=False)
    refresh_token = Column(String(4000), nullable=False)

    # Jira cloud instance identifier
    cloud_id = Column(String(255), nullable=True)

    # Token expiration
    expire_at = Column(DateTime, nullable=False)

    # Audit fields
    created_at = Column(DateTime, nullable=False, default=get_sydney_now)
    updated_at = Column(DateTime, nullable=False, default=get_sydney_now, onupdate=get_sydney_now)

    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<PMATokens(user_id='{self.user_id}', user_name='{self.user_name}', expire_at='{self.expire_at}')>"


class AuditLog(Base):
    """
    Model for storing comprehensive audit logs.

    This table stores all user interactions, bot actions, system events,
    and errors for comprehensive auditing and monitoring.
    """

    __tablename__ = 'AuditLog'

    # Primary key
    id = Column(Integer, primary_key=True, autoincrement=True)

    # Event identification
    event_id = Column(String(36), nullable=False, unique=True)  # UUID
    event_type = Column(String(50), nullable=False)  # AuditEventType enum value
    event_status = Column(String(20), nullable=False)  # AuditStatus enum value

    # User and session information
    user_id = Column(String(255), nullable=True)
    user_name = Column(String(255), nullable=True)
    session_id = Column(String(255), nullable=True)

    # Request/Response information
    request_id = Column(String(36), nullable=True)
    activity_id = Column(String(255), nullable=True)
    channel_id = Column(String(255), nullable=True)

    # Event details
    action_name = Column(String(255), nullable=True)  # For action executions
    endpoint = Column(String(255), nullable=True)  # For API calls
    method = Column(String(10), nullable=True)  # HTTP method

    # Content and metadata
    message_content = Column(Text, nullable=True)  # User message or bot response
    parameters = Column(Text, nullable=True)  # JSON string of parameters
    response_data = Column(Text, nullable=True)  # JSON string of response
    error_message = Column(Text, nullable=True)  # Error details
    stack_trace = Column(Text, nullable=True)  # Full stack trace for errors

    # Performance metrics
    duration_ms = Column(Float, nullable=True)  # Execution time in milliseconds

    # Timestamps
    created_at = Column(DateTime, nullable=False, default=get_sydney_now)

    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<AuditLog(id={self.id}, event_type='{self.event_type}', user_id='{self.user_id}', created_at='{self.created_at}')>"


class UserSession(Base):
    """
    Model for tracking user sessions and interactions.

    This table provides session-level tracking for users to understand
    conversation flows and user engagement patterns.
    """

    __tablename__ = 'UserSession'

    # Primary key
    id = Column(Integer, primary_key=True, autoincrement=True)

    # Session identification
    session_id = Column(String(36), nullable=False, unique=True)  # UUID
    user_id = Column(String(255), nullable=False)
    user_name = Column(String(255), nullable=True)

    # Session metadata
    channel_id = Column(String(255), nullable=True)
    conversation_id = Column(String(255), nullable=True)

    # Session metrics
    message_count = Column(Integer, nullable=False, default=0)
    action_count = Column(Integer, nullable=False, default=0)
    error_count = Column(Integer, nullable=False, default=0)

    # Session timing
    first_activity_at = Column(DateTime, nullable=False, default=get_sydney_now)
    last_activity_at = Column(DateTime, nullable=False, default=get_sydney_now)
    session_duration_minutes = Column(Float, nullable=True)

    # Timestamps
    created_at = Column(DateTime, nullable=False, default=get_sydney_now)
    updated_at = Column(DateTime, nullable=False, default=get_sydney_now, onupdate=get_sydney_now)

    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<UserSession(id={self.id}, session_id='{self.session_id}', user_id='{self.user_id}', message_count={self.message_count})>"


# Create indexes for better query performance
Index('idx_audit_log_user_id', AuditLog.user_id)
Index('idx_audit_log_event_type', AuditLog.event_type)
Index('idx_audit_log_created_at', AuditLog.created_at)
Index('idx_audit_log_event_id', AuditLog.event_id)
Index('idx_audit_log_request_id', AuditLog.request_id)
Index('idx_audit_log_user_event_type', AuditLog.user_id, AuditLog.event_type)
Index('idx_audit_log_user_created_at', AuditLog.user_id, AuditLog.created_at)

Index('idx_user_session_user_id', UserSession.user_id)
Index('idx_user_session_session_id', UserSession.session_id)
Index('idx_user_session_created_at', UserSession.created_at)
Index('idx_user_session_last_activity', UserSession.last_activity_at)


def create_tables(engine) -> None:
    """
    Create all tables defined in this module.

    Args:
        engine: SQLAlchemy engine
    """
    try:
        logger.info("Creating database tables if they don't exist")
        Base.metadata.create_all(engine)
        logger.info('Database tables created or already exist')
    except Exception as e:
        logger.error(f'Error creating database tables: {e}')
        logger.debug(f'Error traceback: {traceback.format_exc()}')
        raise
