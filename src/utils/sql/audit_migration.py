"""
Database migration script for audit tables.

This script safely adds the audit tables to the existing database
without affecting existing data or functionality.
"""

import traceback
from datetime import datetime

import pytz
from loguru import logger
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

from src.utils.sql.db_connection import DatabaseConnectionManager
from src.utils.sql.models import AuditLog, Base, UserSession


class AuditMigration:
    """
    Migration class for adding audit tables to the database.
    """
    
    def __init__(self):
        """Initialize the migration."""
        self.db_manager = DatabaseConnectionManager()
        self.sydney_tz = pytz.timezone('Australia/Sydney')
    
    def _get_sydney_now(self) -> datetime:
        """Get current Sydney time."""
        return datetime.now(self.sydney_tz)
    
    def check_table_exists(self, table_name: str) -> bool:
        """
        Check if a table exists in the database.
        
        Args:
            table_name: Name of the table to check
            
        Returns:
            True if table exists, False otherwise
        """
        try:
            with self.db_manager.session_scope() as session:
                # Query to check if table exists in the model schema
                result = session.execute(
                    text("""
                        SELECT COUNT(*) 
                        FROM information_schema.tables 
                        WHERE table_schema = 'model' 
                        AND table_name = :table_name
                    """),
                    {"table_name": table_name}
                )
                count = result.scalar()
                return count > 0
        except SQLAlchemyError as e:
            logger.error(f"Error checking if table {table_name} exists: {e}")
            return False
    
    def create_audit_tables(self) -> bool:
        """
        Create audit tables if they don't exist.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info("Starting audit tables migration...")
            
            # Check if tables already exist
            audit_log_exists = self.check_table_exists('AuditLog')
            user_session_exists = self.check_table_exists('UserSession')
            
            if audit_log_exists and user_session_exists:
                logger.info("Audit tables already exist, skipping migration")
                return True
            
            # Create tables using SQLAlchemy
            engine = self.db_manager.get_engine()
            
            if not audit_log_exists:
                logger.info("Creating AuditLog table...")
                AuditLog.__table__.create(engine, checkfirst=True)
                logger.info("AuditLog table created successfully")
            else:
                logger.info("AuditLog table already exists")
            
            if not user_session_exists:
                logger.info("Creating UserSession table...")
                UserSession.__table__.create(engine, checkfirst=True)
                logger.info("UserSession table created successfully")
            else:
                logger.info("UserSession table already exists")
            
            # Create indexes
            self._create_indexes()
            
            # Log migration completion
            logger.info("Audit tables migration completed successfully")
            return True
            
        except SQLAlchemyError as e:
            logger.error(f"Error creating audit tables: {e}")
            logger.debug(f"Migration error traceback:\n{traceback.format_exc()}")
            return False
    
    def _create_indexes(self):
        """Create indexes for better query performance."""
        try:
            logger.info("Creating audit table indexes...")
            
            with self.db_manager.session_scope() as session:
                # AuditLog indexes
                indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON model.AuditLog(user_id)",
                    "CREATE INDEX IF NOT EXISTS idx_audit_log_event_type ON model.AuditLog(event_type)",
                    "CREATE INDEX IF NOT EXISTS idx_audit_log_created_at ON model.AuditLog(created_at)",
                    "CREATE INDEX IF NOT EXISTS idx_audit_log_event_id ON model.AuditLog(event_id)",
                    "CREATE INDEX IF NOT EXISTS idx_audit_log_request_id ON model.AuditLog(request_id)",
                    "CREATE INDEX IF NOT EXISTS idx_audit_log_user_event_type ON model.AuditLog(user_id, event_type)",
                    "CREATE INDEX IF NOT EXISTS idx_audit_log_user_created_at ON model.AuditLog(user_id, created_at)",
                    
                    # UserSession indexes
                    "CREATE INDEX IF NOT EXISTS idx_user_session_user_id ON model.UserSession(user_id)",
                    "CREATE INDEX IF NOT EXISTS idx_user_session_session_id ON model.UserSession(session_id)",
                    "CREATE INDEX IF NOT EXISTS idx_user_session_created_at ON model.UserSession(created_at)",
                    "CREATE INDEX IF NOT EXISTS idx_user_session_last_activity ON model.UserSession(last_activity_at)",
                ]
                
                for index_sql in indexes:
                    try:
                        session.execute(text(index_sql))
                        logger.debug(f"Created index: {index_sql.split('idx_')[1].split(' ')[0]}")
                    except SQLAlchemyError as e:
                        # Index might already exist, log warning but continue
                        logger.warning(f"Could not create index (may already exist): {e}")
                
                logger.info("Audit table indexes created successfully")
                
        except SQLAlchemyError as e:
            logger.error(f"Error creating indexes: {e}")
            # Don't fail the migration for index creation errors
    
    def verify_migration(self) -> bool:
        """
        Verify that the migration was successful.
        
        Returns:
            True if verification passed, False otherwise
        """
        try:
            logger.info("Verifying audit tables migration...")
            
            # Check that tables exist
            if not self.check_table_exists('AuditLog'):
                logger.error("AuditLog table not found after migration")
                return False
            
            if not self.check_table_exists('UserSession'):
                logger.error("UserSession table not found after migration")
                return False
            
            # Test basic operations
            with self.db_manager.session_scope() as session:
                # Test AuditLog table
                result = session.execute(text("SELECT COUNT(*) FROM model.AuditLog"))
                audit_count = result.scalar()
                logger.debug(f"AuditLog table has {audit_count} records")
                
                # Test UserSession table
                result = session.execute(text("SELECT COUNT(*) FROM model.UserSession"))
                session_count = result.scalar()
                logger.debug(f"UserSession table has {session_count} records")
            
            logger.info("Audit tables migration verification passed")
            return True
            
        except SQLAlchemyError as e:
            logger.error(f"Error verifying migration: {e}")
            logger.debug(f"Verification error traceback:\n{traceback.format_exc()}")
            return False
    
    def rollback_migration(self) -> bool:
        """
        Rollback the migration by dropping audit tables.
        
        WARNING: This will delete all audit data!
        
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.warning("Starting audit tables rollback...")
            logger.warning("This will delete all audit data!")
            
            with self.db_manager.session_scope() as session:
                # Drop tables in reverse order (due to potential foreign keys)
                tables_to_drop = ['UserSession', 'AuditLog']
                
                for table_name in tables_to_drop:
                    if self.check_table_exists(table_name):
                        session.execute(text(f"DROP TABLE IF EXISTS model.{table_name}"))
                        logger.info(f"Dropped table: {table_name}")
                    else:
                        logger.info(f"Table {table_name} does not exist, skipping")
            
            logger.warning("Audit tables rollback completed")
            return True
            
        except SQLAlchemyError as e:
            logger.error(f"Error during rollback: {e}")
            logger.debug(f"Rollback error traceback:\n{traceback.format_exc()}")
            return False
    
    def run_migration(self) -> bool:
        """
        Run the complete migration process.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info("Starting audit mechanism database migration")
            
            # Step 1: Create tables
            if not self.create_audit_tables():
                logger.error("Failed to create audit tables")
                return False
            
            # Step 2: Verify migration
            if not self.verify_migration():
                logger.error("Migration verification failed")
                return False
            
            logger.info("Audit mechanism database migration completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Unexpected error during migration: {e}")
            logger.debug(f"Migration error traceback:\n{traceback.format_exc()}")
            return False


def run_audit_migration() -> bool:
    """
    Run the audit migration.
    
    Returns:
        True if successful, False otherwise
    """
    migration = AuditMigration()
    return migration.run_migration()


def verify_audit_migration() -> bool:
    """
    Verify the audit migration.
    
    Returns:
        True if verification passed, False otherwise
    """
    migration = AuditMigration()
    return migration.verify_migration()


def rollback_audit_migration() -> bool:
    """
    Rollback the audit migration.
    
    WARNING: This will delete all audit data!
    
    Returns:
        True if successful, False otherwise
    """
    migration = AuditMigration()
    return migration.rollback_migration()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "migrate":
            success = run_audit_migration()
        elif command == "verify":
            success = verify_audit_migration()
        elif command == "rollback":
            print("WARNING: This will delete all audit data!")
            confirm = input("Are you sure you want to rollback? (yes/no): ")
            if confirm.lower() == "yes":
                success = rollback_audit_migration()
            else:
                print("Rollback cancelled")
                success = True
        else:
            print("Usage: python audit_migration.py [migrate|verify|rollback]")
            success = False
    else:
        # Default: run migration
        success = run_audit_migration()
    
    sys.exit(0 if success else 1)
