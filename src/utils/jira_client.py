"""
Module for creating and managing Jira API clients.
"""

import threading

from atlassian import <PERSON>ra

from auth.token_manager import JiraToken<PERSON>anager, TokenStorage
from config import Config


class JiraClientFactory:
    """Factory for creating and managing Jira API clients."""

    # Class-level dictionary to store instances by user_id
    _instances = {}
    # Lock for thread safety
    _lock = threading.Lock()

    def __init__(self, token_storage: TokenStorage):
        self.token_manager = JiraTokenManager(token_storage)

    async def get_client(self, user_id: str, user_name: str) -> <PERSON><PERSON>:
        """
        Get a Jira client for the specified user.

        Args:
            user_id: User ID

        Returns:
            Jira: A Jira API client instance

        Raises:
            ValueError: If no valid tokens are available for the user
        """
        # Ensure token is valid
        is_valid = await self.token_manager.ensure_valid_token(user_id, user_name)
        if not is_valid:
            raise ValueError('No valid Jira token available')

        # Get tokens
        tokens = self.token_manager.token_storage.get_tokens(user_id)
        access_token = tokens['access_token']

        # Get cloud ID from state
        # This assumes cloud_id is stored in state.user.jira_cloud_id
        # You might need to adjust this depending on your actual implementation
        cloud_id = self.token_manager.token_storage.get_tokens(user_id)['cloud_id']

        with self._lock:
            client_key = f'{user_id}_{access_token}_{cloud_id}'

            if client_key not in self._instances:
                self._instances[client_key] = self._create_client(access_token, cloud_id)

            return self._instances[client_key]

    def _create_client(self, access_token: str, cloud_id: str) -> Jira:
        """
        Create a new Jira client.

        Args:
            access_token: Jira access token
            cloud_id: Jira cloud ID

        Returns:
            Jira: A Jira API client instance
        """
        oauth2_dict = {
            'client_id': Config.JIRA_CLIENT_ID,
            'token': {
                'access_token': access_token,
                'token_type': 'Bearer',
            },
        }

        return Jira(
            url=f'{Config.JIRA_BASE_URL}/{cloud_id}',
            oauth2=oauth2_dict,
        )
