"""
Enhanced audit middleware with privacy controls and sanitization.

This module provides enhanced middleware that integrates with the text sanitization
system and privacy controls for secure API request/response logging.
"""

import time
import traceback
import uuid
from typing import Awaitable, Callable, Dict, Optional

from aiohttp import web
from loguru import logger

from src.services.enhanced_audit_service import (
    EnhancedAuditService,
    PrivacyConfig,
    get_enhanced_audit_service,
)
from src.utils.sql.models import AuditStatus
from src.utils.text_sanitizer import ContentType, SensitivityLevel


def _should_skip_audit(endpoint: str) -> bool:
    """
    Determine if the endpoint should be skipped for auditing.

    Args:
        endpoint: The API endpoint path

    Returns:
        True if auditing should be skipped
    """
    skip_patterns = [
        '/health',
        '/ping',
        '/favicon.ico',
        '/static/',
        '/assets/',
        '/metrics',
        '/status',
    ]

    return any(pattern in endpoint for pattern in skip_patterns)


def _extract_request_parameters(request: web.Request, method: str) -> Dict:
    """
    Extract request parameters with enhanced error handling.

    Args:
        request: The HTTP request
        method: HTTP method

    Returns:
        Dictionary of extracted parameters
    """
    parameters = {}

    try:
        # Query parameters
        if request.query:
            parameters['query'] = dict(request.query)

        # Request headers (selective)
        sensitive_headers = {'authorization', 'cookie', 'x-api-key', 'x-auth-token'}
        safe_headers = {}
        for key, value in request.headers.items():
            if key.lower() not in sensitive_headers:
                safe_headers[key] = value
            else:
                safe_headers[key] = '[REDACTED]'
        parameters['headers'] = safe_headers

        # Request body (for POST/PUT requests)
        if method in ['POST', 'PUT', 'PATCH'] and request.content_type:
            if 'application/json' in request.content_type:
                # JSON body will be handled by sanitization
                parameters['content_type'] = 'application/json'
            elif 'application/x-www-form-urlencoded' in request.content_type:
                parameters['content_type'] = 'application/x-www-form-urlencoded'
            elif 'multipart/form-data' in request.content_type:
                parameters['content_type'] = 'multipart/form-data'
                parameters['note'] = 'File upload detected - content not logged'
            else:
                parameters['content_type'] = request.content_type

    except Exception as e:
        logger.debug(f'Failed to extract request parameters: {e}')
        parameters['extraction_error'] = str(e)

    return parameters


def _extract_response_data(response: web.StreamResponse) -> Dict:
    """
    Extract response data with enhanced privacy controls.

    Args:
        response: The HTTP response

    Returns:
        Dictionary of response data
    """
    response_data = {
        'status_code': response.status,
        'content_length': response.content_length,
    }

    try:
        # Add content type if available
        if hasattr(response, 'content_type') and response.content_type:
            response_data['content_type'] = response.content_type

        # For successful JSON responses, include limited body info
        if (200 <= response.status < 300 and
            hasattr(response, 'content_type') and
            response.content_type and
            'application/json' in response.content_type):

            # Only include body for small responses to avoid memory issues
            if (hasattr(response, '_body') and response._body and
                len(response._body) < 2000):  # Increased limit for enhanced logging
                try:
                    response_data['body_preview'] = response._body.decode('utf-8')[:1000]
                except Exception:
                    response_data['body_preview'] = '[Binary or non-UTF8 content]'

    except Exception as e:
        logger.debug(f'Failed to extract response data: {e}')
        response_data['extraction_error'] = str(e)

    return response_data


@web.middleware
async def enhanced_audit_middleware(
    request: web.Request,
    handler: Callable[[web.Request], Awaitable[web.StreamResponse]],
    privacy_config: Optional[PrivacyConfig] = None
) -> web.StreamResponse:
    """
    Enhanced middleware for auditing API requests and responses with privacy controls.

    Args:
        request: The incoming HTTP request
        handler: The request handler function
        privacy_config: Optional privacy configuration

    Returns:
        The response from the handler
    """
    # Initialize enhanced audit service
    enhanced_audit_service = get_enhanced_audit_service(privacy_config)

    # Generate unique request ID
    request_id = f'req_{int(time.time() * 1000)}_{str(uuid.uuid4())[:8]}'
    start_time = time.time()

    # Extract request information
    method = request.method
    endpoint = str(request.url.path)

    # Skip audit logging for health checks and static files
    if _should_skip_audit(endpoint):
        return await handler(request)

    # Extract user information from request headers or query params
    user_id = None
    user_name = None

    # Try to extract user info from various sources
    user_id = request.headers.get('X-User-ID') or request.query.get('user_id')
    user_name = request.headers.get('X-User-Name') or request.query.get('user_name')

    # Extract request parameters with enhanced handling
    parameters = _extract_request_parameters(request, method)

    # Create audit context
    audit_context = {
        'endpoint': endpoint,
        'method': method,
        'user_agent': request.headers.get('User-Agent', 'unknown'),
        'remote_addr': request.remote,
        'environment': 'production',  # Could be configurable
    }

    # Determine sensitivity level based on endpoint
    sensitivity_level = _determine_endpoint_sensitivity(endpoint, method)

    # Log API call start
    logger.info(f'ENHANCED AUDIT: API call {method} {endpoint} started [id={request_id}]')

    try:
        # Execute the handler
        response = await handler(request)

        # Calculate duration
        duration_ms = (time.time() - start_time) * 1000

        # Extract response information
        response_data = _extract_response_data(response)

        # Determine event status based on HTTP status code
        if 200 <= response.status < 300:
            event_status = AuditStatus.SUCCESS
        elif 400 <= response.status < 500:
            event_status = AuditStatus.WARNING
        else:
            event_status = AuditStatus.FAILURE

        # Log successful API call with enhanced features
        await enhanced_audit_service.log_event_enhanced(
            event_type=AuditEventType.API_CALL,
            event_status=event_status,
            user_id=user_id,
            user_name=user_name,
            request_id=request_id,
            endpoint=endpoint,
            method=method,
            parameters=parameters,
            response_data=response_data,
            duration_ms=duration_ms,
            sensitivity_level=sensitivity_level,
            context=audit_context,
        )

        logger.info(
            f'ENHANCED AUDIT: API call {method} {endpoint} completed [id={request_id}] '
            f'status={response.status} in {duration_ms:.3f}ms'
        )

        return response

    except Exception as e:
        # Calculate duration
        duration_ms = (time.time() - start_time) * 1000

        # Get stack trace
        stack_trace = traceback.format_exc()

        # Log failed API call with enhanced features
        await enhanced_audit_service.log_event_enhanced(
            event_type=AuditEventType.API_CALL,
            event_status=AuditStatus.FAILURE,
            user_id=user_id,
            user_name=user_name,
            request_id=request_id,
            endpoint=endpoint,
            method=method,
            parameters=parameters,
            duration_ms=duration_ms,
            error_message=str(e),
            stack_trace=stack_trace,
            sensitivity_level=sensitivity_level,
            context=audit_context,
        )

        logger.error(f'ENHANCED AUDIT: API call {method} {endpoint} failed [id={request_id}] after {duration_ms:.3f}ms: {e}')

        # Re-raise the exception
        raise


def _determine_endpoint_sensitivity(endpoint: str, method: str) -> SensitivityLevel:
    """
    Determine sensitivity level based on endpoint and method.

    Args:
        endpoint: API endpoint path
        method: HTTP method

    Returns:
        Appropriate sensitivity level
    """
    # High sensitivity endpoints
    high_sensitivity_patterns = [
        '/auth/', '/login', '/password', '/token', '/secret',
        '/admin/', '/config/', '/settings/', '/users/'
    ]

    # Medium sensitivity endpoints
    medium_sensitivity_patterns = [
        '/api/messages', '/api/actions', '/data/', '/export/'
    ]

    endpoint_lower = endpoint.lower()

    # Check for high sensitivity patterns
    if any(pattern in endpoint_lower for pattern in high_sensitivity_patterns):
        return SensitivityLevel.CONFIDENTIAL

    # Check for medium sensitivity patterns
    if any(pattern in endpoint_lower for pattern in medium_sensitivity_patterns):
        return SensitivityLevel.INTERNAL

    # POST/PUT/DELETE operations are generally more sensitive
    if method in ['POST', 'PUT', 'DELETE', 'PATCH']:
        return SensitivityLevel.INTERNAL

    # Default to public for GET requests to general endpoints
    return SensitivityLevel.PUBLIC


def create_enhanced_audit_middleware(privacy_config: Optional[PrivacyConfig] = None):
    """
    Create and return an enhanced audit middleware function with privacy controls.

    Args:
        privacy_config: Optional privacy configuration

    Returns:
        The enhanced audit middleware function
    """
    @web.middleware
    async def middleware(request: web.Request, handler):
        return await enhanced_audit_middleware(request, handler, privacy_config)

    return middleware


async def enhanced_messages_handler(
    request: web.Request,
    handler,
    privacy_config: Optional[PrivacyConfig] = None
):
    """
    Enhanced message handler with audit logging for bot messages and privacy controls.

    This function wraps the existing message handler to add enhanced audit logging
    specifically for bot framework messages.

    Args:
        request: The HTTP request
        handler: The message handler function
        privacy_config: Optional privacy configuration
    """
    # Generate unique request ID
    request_id = f'msg_{int(time.time() * 1000)}_{str(uuid.uuid4())[:8]}'
    start_time = time.time()

    # Initialize enhanced audit service
    enhanced_audit_service = get_enhanced_audit_service(privacy_config)

    logger.info(f'ENHANCED AUDIT: Bot message request started [id={request_id}]')

    try:
        # Execute the original handler
        response = await handler(request)

        # Calculate duration
        duration_ms = (time.time() - start_time) * 1000

        # Create audit context
        audit_context = {
            'handler_type': 'bot_message',
            'environment': 'production',
            'request_id': request_id,
        }

        # Log successful message processing with enhanced features
        await enhanced_audit_service.log_event_enhanced(
            event_type=AuditEventType.SYSTEM_EVENT,
            event_status=AuditStatus.SUCCESS,
            request_id=request_id,
            endpoint='/api/messages',
            method='POST',
            parameters={'request_id': request_id, 'duration_ms': duration_ms},
            duration_ms=duration_ms,
            sensitivity_level=SensitivityLevel.INTERNAL,
            context=audit_context,
        )

        logger.info(f'ENHANCED AUDIT: Bot message request completed [id={request_id}] in {duration_ms:.3f}ms')

        return response

    except Exception as e:
        # Calculate duration
        duration_ms = (time.time() - start_time) * 1000

        # Get stack trace
        stack_trace = traceback.format_exc()

        # Create audit context
        audit_context = {
            'handler_type': 'bot_message',
            'environment': 'production',
            'request_id': request_id,
            'error_type': type(e).__name__,
        }

        # Log failed message processing with enhanced features
        await enhanced_audit_service.log_event_enhanced(
            event_type=AuditEventType.ERROR,
            event_status=AuditStatus.FAILURE,
            request_id=request_id,
            endpoint='/api/messages',
            method='POST',
            parameters={'request_id': request_id, 'duration_ms': duration_ms},
            error_message=str(e),
            stack_trace=stack_trace,
            duration_ms=duration_ms,
            sensitivity_level=SensitivityLevel.INTERNAL,
            context=audit_context,
        )

        logger.error(f'ENHANCED AUDIT: Bot message request failed [id={request_id}] after {duration_ms:.3f}ms: {e}')

        # Re-raise the exception
        raise


def setup_enhanced_audit_logging(privacy_config: Optional[PrivacyConfig] = None):
    """
    Setup enhanced audit-specific logging configuration with privacy controls.

    Args:
        privacy_config: Optional privacy configuration for logging
    """
    # Add enhanced audit-specific log format
    enhanced_audit_format = (
        '<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | '
        '<level>{level: <8}</level> | '
        '<cyan>ENHANCED_AUDIT</cyan> | '
        '<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | '
        '<level>{message}</level>'
    )

    # Add enhanced audit file logger in production
    import os

    is_production = os.environ.get('ENVIRONMENT', 'development').lower() == 'production'

    if is_production:
        log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
        os.makedirs(log_dir, exist_ok=True)

        # Enhanced audit logs with better rotation and retention
        logger.add(
            os.path.join(log_dir, 'enhanced_audit_{time}.log'),
            format=enhanced_audit_format,
            level='INFO',
            rotation='50 MB',  # Rotate when file reaches 50 MB
            retention='3 months',  # Keep enhanced audit logs for 3 months
            compression='zip',  # Compress rotated logs
            filter=lambda record: 'ENHANCED_AUDIT:' in record['message'],  # Only enhanced audit messages
            backtrace=True,
            diagnose=True,
            enqueue=True,
        )

        # Separate log for sanitization activities
        logger.add(
            os.path.join(log_dir, 'sanitization_{time}.log'),
            format=enhanced_audit_format,
            level='DEBUG',
            rotation='25 MB',
            retention='1 month',
            compression='zip',
            filter=lambda record: 'Content sanitized:' in record['message'],
            backtrace=False,
            diagnose=False,
            enqueue=True,
        )

    logger.info('ENHANCED_AUDIT: Enhanced audit logging configuration completed')


# Import required types
from src.utils.sql.models import AuditEventType