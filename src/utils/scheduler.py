"""
Demonstrates how to use the asyncio compatible scheduler to schedule a job that executes on 3
second intervals.
"""

import threading
from typing import Optional

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from botbuilder.core import Turn<PERSON>ontext

from config import Config
from state import AppTurnState
from utils.audit_decorators import audit_message_handler


class Scheduler:
    _instance: Optional[AsyncIOScheduler] = None
    _lock = threading.Lock()

    @classmethod
    def get_instance(cls) -> AsyncIOScheduler:
        """Get or create the Scheduler singleton instance."""
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = AsyncIOScheduler()
        return cls._instance


@audit_message_handler('remind')
async def remind(context: TurnContext, state: AppTurnState):
    await context.send_activity(Config.REMIND_MESSAGE)
