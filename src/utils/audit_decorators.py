"""
Audit decorators for automatic logging of user interactions and bot actions.

This module provides decorators that can be applied to functions to automatically
capture audit information without modifying the core business logic.
"""

import functools
import time
import traceback
import uuid
from typing import Callable, Optional

from botbuilder.core import TurnContext
from loguru import logger

from src.services.audit_service import AuditService, SessionService
from src.utils.sql.models import AuditStatus


class AuditContext:
    """
    Context manager for audit information that can be shared across decorators.
    """

    def __init__(self):
        self.request_id: Optional[str] = None
        self.session_id: Optional[str] = None
        self.user_id: Optional[str] = None
        self.user_name: Optional[str] = None
        self.activity_id: Optional[str] = None
        self.channel_id: Optional[str] = None
        self.audit_service: Optional[AuditService] = None
        self.session_service: Optional[SessionService] = None

    def initialize_services(self):
        """Initialize audit and session services."""
        if self.audit_service is None:
            self.audit_service = AuditService()
        if self.session_service is None:
            self.session_service = SessionService()


# Global audit context
audit_context = AuditContext()


def extract_user_info(context) -> tuple[Optional[str], Optional[str], Optional[str], Optional[str]]:
    """
    Extract user information from bot context.

    Args:
        context: Bot turn context or action context

    Returns:
        Tuple of (user_id, user_name, activity_id, channel_id)
    """
    user_id = None
    user_name = None
    activity_id = None
    channel_id = None

    try:
        if hasattr(context, 'activity') and context.activity:
            if hasattr(context.activity, 'from_property') and context.activity.from_property:
                user_id = context.activity.from_property.id
                user_name = context.activity.from_property.name

            activity_id = getattr(context.activity, 'id', None)
            channel_id = getattr(context.activity, 'channel_id', None)
    except Exception as e:
        logger.debug(f'Failed to extract user info from context: {e}')

    return user_id, user_name, activity_id, channel_id


def audit_action(action_name: Optional[str] = None):
    """
    Decorator to audit action function executions.

    Args:
        action_name: Optional custom action name (defaults to function name)
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(context, *args, **kwargs):
            # Initialize audit context
            audit_context.initialize_services()

            # Generate unique IDs
            event_id = str(uuid.uuid4())
            start_time = time.time()

            # Extract user information
            user_id, user_name, activity_id, channel_id = extract_user_info(context)

            # Get or create session
            session_id = None
            if user_id and audit_context.session_service:
                try:
                    session_id = await audit_context.session_service.get_or_create_session(
                        user_id=user_id,
                        user_name=user_name,
                        channel_id=channel_id,
                    )
                except Exception as e:
                    logger.warning(f'Failed to get/create session for audit: {e}')

            # Determine action name
            actual_action_name = action_name or func.__name__

            # Extract parameters
            parameters = {}
            if hasattr(context, 'data') and context.data:
                parameters = dict(context.data)

            # Log action start
            logger.info(f'AUDIT: Action {actual_action_name} started [id={event_id}] by user {user_name}')

            try:
                # Execute the function
                result = await func(context, *args, **kwargs)

                # Calculate duration
                duration_ms = (time.time() - start_time) * 1000

                # Log successful action execution
                if audit_context.audit_service:
                    await audit_context.audit_service.log_action_execution(
                        action_name=actual_action_name,
                        user_id=user_id or 'unknown',
                        user_name=user_name or 'unknown',
                        parameters=parameters,
                        response_data={'result': str(result)[:1000]},  # Limit response size
                        event_status=AuditStatus.SUCCESS,
                        session_id=session_id,
                        activity_id=activity_id,
                        channel_id=channel_id,
                        duration_ms=duration_ms,
                    )

                # Update session metrics
                if session_id and audit_context.session_service:
                    await audit_context.session_service.update_session_metrics(
                        session_id=session_id,
                        action_increment=1,
                    )

                logger.info(f'AUDIT: Action {actual_action_name} completed [id={event_id}] in {duration_ms:.3f}ms')
                return result

            except Exception as e:
                # Calculate duration
                duration_ms = (time.time() - start_time) * 1000

                # Get stack trace
                stack_trace = traceback.format_exc()

                # Log failed action execution
                if audit_context.audit_service:
                    await audit_context.audit_service.log_action_execution(
                        action_name=actual_action_name,
                        user_id=user_id or 'unknown',
                        user_name=user_name or 'unknown',
                        parameters=parameters,
                        event_status=AuditStatus.FAILURE,
                        session_id=session_id,
                        activity_id=activity_id,
                        channel_id=channel_id,
                        duration_ms=duration_ms,
                        error_message=str(e),
                        stack_trace=stack_trace,
                    )

                # Update session metrics
                if session_id and audit_context.session_service:
                    await audit_context.session_service.update_session_metrics(
                        session_id=session_id,
                        action_increment=1,
                        error_increment=1,
                    )

                logger.error(
                    f'AUDIT: Action {actual_action_name} failed [id={event_id}] after {duration_ms:.3f}ms: {e}'
                )
                raise

        return wrapper

    return decorator


def audit_message_handler(handler_name: Optional[str] = None):
    """
    Decorator to audit message handler executions.

    Args:
        handler_name: Optional custom handler name (defaults to function name)
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(context, state=None, *args, **kwargs):
            # Initialize audit context
            audit_context.initialize_services()

            # Generate unique IDs
            event_id = str(uuid.uuid4())
            start_time = time.time()

            # Extract user information
            user_id, user_name, activity_id, channel_id = extract_user_info(context)

            # Get message content
            message_content = None
            if hasattr(context, 'activity') and context.activity and hasattr(context.activity, 'text'):
                message_content = context.activity.text

            # Get or create session
            session_id = None
            if user_id and audit_context.session_service:
                try:
                    session_id = await audit_context.session_service.get_or_create_session(
                        user_id=user_id,
                        user_name=user_name,
                        channel_id=channel_id,
                    )
                except Exception as e:
                    logger.warning(f'Failed to get/create session for audit: {e}')

            # Log user message
            if message_content and audit_context.audit_service and user_id:
                await audit_context.audit_service.log_user_message(
                    user_id=user_id,
                    user_name=user_name or 'unknown',
                    message_content=message_content,
                    session_id=session_id,
                    activity_id=activity_id,
                    channel_id=channel_id,
                )

            # Determine handler name
            actual_handler_name = handler_name or func.__name__

            logger.info(f'AUDIT: Message handler {actual_handler_name} started [id={event_id}] by user {user_name}')

            try:
                # Create audit-enabled context wrapper for bot response capture
                audit_context_wrapper = AuditTurnContext(context, session_id)

                # Execute the function with the audit wrapper
                result = await func(audit_context_wrapper, state, *args, **kwargs)

                # Calculate duration
                duration_ms = (time.time() - start_time) * 1000

                # Update session metrics
                if session_id and audit_context.session_service:
                    await audit_context.session_service.update_session_metrics(
                        session_id=session_id,
                        message_increment=1,
                    )

                logger.info(
                    f'AUDIT: Message handler {actual_handler_name} completed [id={event_id}] in {duration_ms:.3f}ms'
                )
                return result

            except Exception as e:
                # Calculate duration
                duration_ms = (time.time() - start_time) * 1000

                # Get stack trace
                stack_trace = traceback.format_exc()

                # Log error
                if audit_context.audit_service:
                    await audit_context.audit_service.log_error_event(
                        error_message=str(e),
                        stack_trace=stack_trace,
                        user_id=user_id,
                        user_name=user_name,
                        session_id=session_id,
                        activity_id=activity_id,
                        channel_id=channel_id,
                        action_name=actual_handler_name,
                    )

                # Update session metrics
                if session_id and audit_context.session_service:
                    await audit_context.session_service.update_session_metrics(
                        session_id=session_id,
                        message_increment=1,
                        error_increment=1,
                    )

                logger.error(
                    f'AUDIT: Message handler {actual_handler_name} failed [id={event_id}] after {duration_ms:.3f}ms: {e}'
                )
                raise

        return wrapper

    return decorator


class AuditTurnContext:
    """
    Wrapper for TurnContext that automatically captures bot responses for audit logging.

    This class wraps the original TurnContext and intercepts send_activity calls
    to automatically log bot responses to the audit database.
    """

    def __init__(self, original_context: 'TurnContext', session_id: Optional[str] = None):
        """
        Initialize the audit wrapper.

        Args:
            original_context: The original TurnContext to wrap
            session_id: Optional session ID for audit tracking
        """
        self._original_context = original_context
        self._session_id = session_id
        self._audit_service = AuditService()

        # Extract user information once
        self._user_id, self._user_name, self._activity_id, self._channel_id = extract_user_info(original_context)

    def __getattr__(self, name):
        """Delegate all other attributes to the original context."""
        return getattr(self._original_context, name)

    async def send_activity(self, activity_or_text, speak: Optional[str] = None):
        """
        Override send_activity to capture bot responses for audit logging.

        Args:
            activity_or_text: Activity object or text string to send
            speak: Optional speak text for voice responses

        Returns:
            The response from the original send_activity call
        """
        start_time = time.time()

        try:
            # Call the original send_activity method
            if speak is not None:
                response = await self._original_context.send_activity(activity_or_text, speak)
            else:
                response = await self._original_context.send_activity(activity_or_text)

            # Calculate duration
            duration_ms = (time.time() - start_time) * 1000

            # Extract response content for logging
            response_content = self._extract_response_content(activity_or_text)

            # Log the bot response if we have content and user info
            if response_content and self._user_id:
                try:
                    await self._audit_service.log_bot_response(
                        user_id=self._user_id,
                        user_name=self._user_name or 'unknown',
                        response_content=response_content,
                        session_id=self._session_id,
                        activity_id=self._activity_id,
                        channel_id=self._channel_id,
                        duration_ms=duration_ms,
                    )
                    logger.debug(f'AUDIT: Bot response logged for user {self._user_name}')
                except Exception as e:
                    logger.warning(f'Failed to log bot response for audit: {e}')

            return response

        except Exception as e:
            # Log error but don't interfere with the original functionality
            logger.warning(f'Error in audit wrapper send_activity: {e}')
            # Still call the original method
            if speak is not None:
                return await self._original_context.send_activity(activity_or_text, speak)
            else:
                return await self._original_context.send_activity(activity_or_text)

    def _extract_response_content(self, activity_or_text) -> Optional[str]:
        """
        Extract text content from activity or text for logging.

        Args:
            activity_or_text: Activity object or text string

        Returns:
            Extracted text content or None
        """
        try:
            if isinstance(activity_or_text, str):
                return activity_or_text
            elif hasattr(activity_or_text, 'text') and activity_or_text.text:
                return activity_or_text.text
            elif hasattr(activity_or_text, 'attachments') and activity_or_text.attachments:
                # For adaptive cards and other attachments, log a summary
                attachment_count = len(activity_or_text.attachments)
                attachment_types = [
                    att.content_type for att in activity_or_text.attachments if hasattr(att, 'content_type')
                ]
                return f'[Bot sent {attachment_count} attachment(s): {", ".join(attachment_types)}]'
            else:
                return '[Bot sent activity without text content]'
        except Exception as e:
            logger.debug(f'Failed to extract response content: {e}')
            return '[Bot response - content extraction failed]'


def create_audit_context(original_context: 'TurnContext', session_id: Optional[str] = None) -> AuditTurnContext:
    """
    Create an audit-enabled TurnContext wrapper.

    Args:
        original_context: The original TurnContext to wrap
        session_id: Optional session ID for audit tracking

    Returns:
        AuditTurnContext wrapper that captures bot responses
    """
    return AuditTurnContext(original_context, session_id)
