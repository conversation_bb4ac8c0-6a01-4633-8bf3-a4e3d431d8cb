"""
Module for decorators used in the application.
"""

import functools

from loguru import logger

from auth.token_manager import SQLTokenStorage

from .jira_client import JiraClientFactory


def with_jira_client(func):
    """
    Decorator to inject Jira client into action function.

    This decorator:
    1. Checks for valid authentication
    2. Gets or creates a Jira client instance
    3. Adds the client to the function arguments
    4. Handles errors gracefully
    """

    @functools.wraps(func)
    async def wrapper(context, *args, **kwargs):
        try:
            if not context.activity or not context.activity.from_property:
                return 'Error: Unable to identify user.'

            user_id = context.activity.from_property.id
            user_name = context.activity.from_property.name

            # Create SQL token storage with state for backward compatibility
            token_storage = SQLTokenStorage()

            # Create client factory
            client_factory = JiraClientFactory(token_storage)

            try:
                # Get Jira client
                jira = await client_factory.get_client(user_id, user_name)

                # Add jira client to kwargs
                kwargs['jira'] = jira
                return await func(context, *args, **kwargs)

            except ValueError:
                # No valid token available, authentication required
                return 'Authentication required. Please log in to <PERSON><PERSON> first.'

        except Exception as e:
            logger.error(f'Error getting <PERSON>ra client: {e}')
            return 'Error connecting to Jira. Please try again later.'

    return wrapper
