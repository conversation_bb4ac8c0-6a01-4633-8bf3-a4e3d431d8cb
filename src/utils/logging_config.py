"""
Logging configuration for the application.

This module configures the Loguru logger with appropriate settings for
development and production environments. It sets up log rotation,
formatting, and output destinations.
"""

import os
import sys
from datetime import datetime

import pytz
from loguru import logger


def sydney_time_formatter(record):
    """
    Format the time in Australia/Sydney timezone.

    Args:
        record: The log record to format

    Returns:
        str: Formatted timestamp in Sydney timezone
    """
    # Get the naive datetime from the record
    dt = datetime.fromtimestamp(record['time'].timestamp())

    # Convert to Sydney timezone
    sydney_tz = pytz.timezone('Australia/Sydney')
    sydney_time = sydney_tz.localize(dt.replace(tzinfo=None))

    # Format the time
    return sydney_time.strftime('%Y-%m-%d %H:%M:%S.%f %z')


def setup_logging(log_level='INFO'):
    """
    Configure Loguru logger with appropriate settings.

    Args:
        log_level: The minimum log level to capture (default: INFO)
    """
    # Remove default logger
    logger.remove()

    # Determine if we're in development or production
    is_production = os.environ.get('ENVIRONMENT', 'development').lower() == 'production'

    # Set log format based on environment
    log_format = (
        '<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | '
        '<level>{level: <8}</level> | '
        '<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | '
        '<level>{message}</level>'
    )

    if is_production:
        # More detailed format for production
        log_format = (
            '<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | '
            '<level>{level: <8}</level> | '
            '<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | '
            '<yellow>{thread.name}</yellow> | '
            '<magenta>{extra}</magenta> | '
            '<level>{message}</level>'
        )

    # Add console logger
    logger.add(
        sys.stderr,
        format=log_format,
        level=log_level,
        colorize=True,
        backtrace=True,
        diagnose=True,
    )

    # Add file logger with rotation in production
    if is_production:
        log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
        os.makedirs(log_dir, exist_ok=True)

        logger.add(
            os.path.join(log_dir, 'bot_framework_{time}.log'),
            format=log_format,
            level=log_level,
            rotation='10 MB',  # Rotate when file reaches 10 MB
            retention='1 week',  # Keep logs for 1 week
            compression='zip',  # Compress rotated logs
            backtrace=True,
            diagnose=True,
            enqueue=True,  # Use a queue for thread safety
        )

    # Configure exception handling
    def handle_exception(exc_type, exc_value, exc_traceback):
        """
        Handle uncaught exceptions by logging them.
        """
        if issubclass(exc_type, KeyboardInterrupt):
            # Let KeyboardInterrupt pass through
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return

        # Log the exception
        logger.opt(exception=(exc_type, exc_value, exc_traceback)).critical('Uncaught exception: {0}', exc_value)

    # Set the exception hook
    sys.excepthook = handle_exception

    # Log startup message
    logger.info('Logging configured with level: {}', log_level)
    logger.info('Environment: {}', 'Production' if is_production else 'Development')


def get_logger():
    """
    Get the configured logger instance.

    Returns:
        Logger: The configured Loguru logger instance
    """
    return logger


# Add a context manager for database operations
class LoggingContext:
    """
    Context manager for adding context to logs.

    Example:
        with LoggingContext(user_id="123", operation="create_ticket"):
            logger.info("Creating ticket")
    """

    def __init__(self, **kwargs):
        self.context = kwargs

    def __enter__(self):
        # Add context to logger
        logger.configure(extra=self.context)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        # Reset context
        logger.configure(extra={})

        # Log exception if one occurred
        if exc_type is not None:
            logger.opt(exception=(exc_type, exc_val, exc_tb)).error('Exception occurred in context: {}', self.context)

        return False  # Don't suppress exceptions
