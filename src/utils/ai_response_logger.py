"""
AI Response Logger for capturing and logging AI-generated messages.

This module provides utilities to capture AI-generated responses from the
Teams AI framework and integrate them with the existing Loguru logging
and audit system.
"""

import uuid
from typing import Any, Dict, Optional

from botbuilder.core import Turn<PERSON>ontext
from loguru import logger

from src.services.audit_service import AuditService
from src.utils.audit_decorators import extract_user_info
from src.utils.logging_config import LoggingContext


class AIResponseLogger:
    """
    Logger for AI-generated responses that integrates with the existing
    Loguru logging and audit system.
    """

    def __init__(self):
        """Initialize the AI response logger."""
        self.audit_service = AuditService()

    async def log_ai_response(
        self,
        context: TurnContext,
        response_content: str,
        response_type: str = 'ai_completion',
        session_id: Optional[str] = None,
        duration_ms: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Log an AI-generated response.

        Args:
            context: The turn context
            response_content: The AI-generated response content
            response_type: Type of AI response (e.g., 'ai_completion', 'ai_streaming')
            session_id: Optional session ID for tracking
            duration_ms: Time taken to generate the response
            metadata: Additional metadata about the response

        Returns:
            The event ID of the logged response
        """
        # Extract user information
        user_id, user_name, activity_id, channel_id = extract_user_info(context)

        # Generate unique event ID
        event_id = str(uuid.uuid4())

        # Log with Loguru using structured logging
        with LoggingContext(
            event_id=event_id,
            user_id=user_id,
            user_name=user_name,
            response_type=response_type,
            session_id=session_id,
        ):
            logger.info(
                f'AI Response Generated [id={event_id}] for user {user_name}: '
                f'{response_content[:100]}{"..." if len(response_content) > 100 else ""}'
            )

            # Log detailed response information
            logger.debug(
                f'AI Response Details [id={event_id}]: '
                f'type={response_type}, duration={duration_ms}ms, '
                f'content_length={len(response_content)}, metadata={metadata}'
            )

        # Log to audit database if user information is available
        if user_id and response_content:
            try:
                await self.audit_service.log_bot_response(
                    user_id=user_id,
                    user_name=user_name or 'unknown',
                    response_content=response_content,
                    session_id=session_id,
                    activity_id=activity_id,
                    channel_id=channel_id,
                    duration_ms=duration_ms,
                )
                logger.debug(f'AI response logged to audit database [id={event_id}]')
            except Exception as e:
                logger.warning(f'Failed to log AI response to audit database [id={event_id}]: {e}')

        return event_id

    async def log_streaming_ai_response(
        self,
        context: TurnContext,
        final_content: str,
        session_id: Optional[str] = None,
        duration_ms: Optional[float] = None,
        chunk_count: Optional[int] = None,
    ) -> str:
        """
        Log a streaming AI response.

        Args:
            context: The turn context
            final_content: The complete streamed response content
            session_id: Optional session ID for tracking
            duration_ms: Total time taken for streaming
            chunk_count: Number of chunks received during streaming

        Returns:
            The event ID of the logged response
        """
        metadata = {
            'streaming': True,
            'chunk_count': chunk_count,
            'total_duration_ms': duration_ms,
        }

        return await self.log_ai_response(
            context=context,
            response_content=final_content,
            response_type='ai_streaming',
            session_id=session_id,
            duration_ms=duration_ms,
            metadata=metadata,
        )

    async def log_ai_error(
        self,
        context: TurnContext,
        error_message: str,
        error_type: str = 'ai_error',
        session_id: Optional[str] = None,
        duration_ms: Optional[float] = None,
        stack_trace: Optional[str] = None,
    ) -> str:
        """
        Log an AI-related error.

        Args:
            context: The turn context
            error_message: The error message
            error_type: Type of AI error
            session_id: Optional session ID for tracking
            duration_ms: Time taken before error occurred
            stack_trace: Full stack trace if available

        Returns:
            The event ID of the logged error
        """
        # Extract user information
        user_id, user_name, activity_id, channel_id = extract_user_info(context)

        # Generate unique event ID
        event_id = str(uuid.uuid4())

        # Log with Loguru
        with LoggingContext(
            event_id=event_id,
            user_id=user_id,
            user_name=user_name,
            error_type=error_type,
            session_id=session_id,
        ):
            logger.error(f'AI Error [id={event_id}] for user {user_name}: {error_message}')

            if stack_trace:
                logger.debug(f'AI Error Stack Trace [id={event_id}]:\n{stack_trace}')

        # Log to audit database
        if user_id:
            try:
                await self.audit_service.log_error_event(
                    error_message=error_message,
                    stack_trace=stack_trace or '',
                    user_id=user_id,
                    user_name=user_name,
                    session_id=session_id,
                    activity_id=activity_id,
                    channel_id=channel_id,
                    action_name=error_type,
                )
                logger.debug(f'AI error logged to audit database [id={event_id}]')
            except Exception as e:
                logger.warning(f'Failed to log AI error to audit database [id={event_id}]: {e}')

        return event_id


# Global instance for easy access
ai_response_logger = AIResponseLogger()
