"""
Audit middleware for automatic logging of API requests and responses.

This module provides middleware that can be added to the aiohttp application
to automatically capture audit information for all API requests.
"""

import time
import traceback
import uuid
from typing import Awaitable, Callable

from aiohttp import web
from loguru import logger

from src.services.audit_service import AuditService
from src.utils.sql.models import AuditStatus


def _should_skip_audit(endpoint: str) -> bool:
    """
    Determine if the endpoint should be skipped for auditing.

    Args:
        endpoint: The API endpoint path

    Returns:
        True if auditing should be skipped
    """
    skip_patterns = [
        '/health',
        '/ping',
        '/favicon.ico',
        '/static/',
        '/assets/',
    ]

    return any(pattern in endpoint for pattern in skip_patterns)


@web.middleware
async def audit_middleware(
    request: web.Request, handler: Callable[[web.Request], Awaitable[web.StreamResponse]]
) -> web.StreamResponse:
    """
    Middleware for auditing API requests and responses.

    Args:
        request: The incoming HTTP request
        handler: The request handler function

    Returns:
        The response from the handler
    """
    # Initialize audit service
    audit_service = AuditService()

    # Generate unique request ID
    request_id = f'req_{int(time.time() * 1000)}_{str(uuid.uuid4())[:8]}'
    start_time = time.time()

    # Extract request information
    method = request.method
    endpoint = str(request.url.path)

    # Skip audit logging for health checks and static files
    if _should_skip_audit(endpoint):
        return await handler(request)

    # Extract user information from request headers or query params
    user_id = None
    user_name = None

    # Try to extract user info from various sources
    user_id = request.headers.get('X-User-ID') or request.query.get('user_id')
    user_name = request.headers.get('X-User-Name') or request.query.get('user_name')

    # Extract request parameters
    parameters = {}
    try:
        # Query parameters
        if request.query:
            parameters['query'] = dict(request.query)

        # Request body (for POST/PUT requests)
        if method in ['POST', 'PUT', 'PATCH'] and request.content_type:
            if 'application/json' in request.content_type:
                try:
                    body = await request.json()
                    parameters['body'] = body
                except Exception:
                    pass
            elif 'application/x-www-form-urlencoded' in request.content_type:
                try:
                    body = await request.post()
                    parameters['body'] = dict(body)
                except Exception:
                    pass
    except Exception as e:
        logger.debug(f'Failed to extract request parameters: {e}')

    # Log API call start
    logger.info(f'AUDIT: API call {method} {endpoint} started [id={request_id}]')

    try:
        # Execute the handler
        response = await handler(request)

        # Calculate duration
        duration_ms = (time.time() - start_time) * 1000

        # Extract response information
        response_data = {}
        status_code = response.status

        # Determine event status based on HTTP status code
        if 200 <= status_code < 300:
            event_status = AuditStatus.SUCCESS
        elif 400 <= status_code < 500:
            event_status = AuditStatus.WARNING
        else:
            event_status = AuditStatus.FAILURE

        # Try to extract response body for successful JSON responses
        if (
            event_status == AuditStatus.SUCCESS
            and hasattr(response, 'content_type')
            and response.content_type
            and 'application/json' in response.content_type
        ):
            try:
                # For Response objects (not StreamResponse), include the body
                if hasattr(response, 'body') and hasattr(response, '_body') and response._body:
                    if len(response._body) < 1000:
                        response_data['body'] = response._body.decode('utf-8')
                response_data['content_type'] = response.content_type
            except Exception:
                pass

        response_data['status_code'] = status_code
        response_data['content_length'] = response.content_length

        # Log successful API call
        await audit_service.log_api_call(
            endpoint=endpoint,
            method=method,
            event_status=event_status,
            user_id=user_id,
            user_name=user_name,
            request_id=request_id,
            parameters=parameters,
            response_data=response_data,
            duration_ms=duration_ms,
        )

        logger.info(
            f'AUDIT: API call {method} {endpoint} completed [id={request_id}] '
            f'status={status_code} in {duration_ms:.3f}ms'
        )

        return response

    except Exception as e:
        # Calculate duration
        duration_ms = (time.time() - start_time) * 1000

        # Get stack trace
        stack_trace = traceback.format_exc()

        # Log failed API call
        await audit_service.log_api_call(
            endpoint=endpoint,
            method=method,
            event_status=AuditStatus.FAILURE,
            user_id=user_id,
            user_name=user_name,
            request_id=request_id,
            parameters=parameters,
            duration_ms=duration_ms,
            error_message=str(e),
        )

        # Also log as error event
        await audit_service.log_error_event(
            error_message=str(e),
            stack_trace=stack_trace,
            user_id=user_id,
            user_name=user_name,
            request_id=request_id,
            endpoint=endpoint,
            parameters=parameters,
        )

        logger.error(f'AUDIT: API call {method} {endpoint} failed [id={request_id}] after {duration_ms:.3f}ms: {e}')

        # Re-raise the exception
        raise


def create_audit_middleware():
    """
    Create and return an audit middleware function.

    Returns:
        The audit middleware function
    """
    return audit_middleware


async def enhanced_messages_handler(request: web.Request, handler):
    """
    Enhanced message handler with audit logging for bot messages.

    This function wraps the existing message handler to add audit logging
    specifically for bot framework messages.
    """
    # Generate unique request ID
    request_id = f'msg_{int(time.time() * 1000)}_{str(uuid.uuid4())[:8]}'
    start_time = time.time()

    # Initialize audit service
    audit_service = AuditService()

    logger.info(f'AUDIT: Bot message request started [id={request_id}]')

    try:
        # Execute the original handler
        response = await handler(request)

        # Calculate duration
        duration_ms = (time.time() - start_time) * 1000

        # Log successful message processing
        await audit_service.log_system_event(
            event_description=f'Bot message processed successfully [id={request_id}]',
            event_status=AuditStatus.SUCCESS,
            parameters={'request_id': request_id, 'duration_ms': duration_ms},
        )

        logger.info(f'AUDIT: Bot message request completed [id={request_id}] in {duration_ms:.3f}ms')

        return response

    except Exception as e:
        # Calculate duration
        duration_ms = (time.time() - start_time) * 1000

        # Get stack trace
        stack_trace = traceback.format_exc()

        # Log failed message processing
        await audit_service.log_error_event(
            error_message=str(e),
            stack_trace=stack_trace,
            request_id=request_id,
            endpoint='/api/messages',
            parameters={'request_id': request_id, 'duration_ms': duration_ms},
        )

        logger.error(f'AUDIT: Bot message request failed [id={request_id}] after {duration_ms:.3f}ms: {e}')

        # Re-raise the exception
        raise


def setup_audit_logging():
    """
    Setup audit-specific logging configuration.

    This function configures additional logging specifically for audit events.
    """
    # Add audit-specific log format
    audit_format = (
        '<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | '
        '<level>{level: <8}</level> | '
        '<cyan>AUDIT</cyan> | '
        '<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | '
        '<level>{message}</level>'
    )

    # Add audit file logger in production
    import os

    is_production = os.environ.get('ENVIRONMENT', 'development').lower() == 'production'

    if is_production:
        log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
        os.makedirs(log_dir, exist_ok=True)

        logger.add(
            os.path.join(log_dir, 'audit_{time}.log'),
            format=audit_format,
            level='INFO',
            rotation='50 MB',  # Rotate when file reaches 50 MB
            retention='1 month',  # Keep audit logs for 1 month
            compression='zip',  # Compress rotated logs
            filter=lambda record: 'AUDIT:' in record['message'],  # Only audit messages
            backtrace=True,
            diagnose=True,
            enqueue=True,
        )

    logger.info('AUDIT: Audit logging configuration completed')
