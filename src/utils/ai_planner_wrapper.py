"""
AI Planner Wrapper for capturing AI responses from the Teams AI framework.

This module provides a wrapper around the ActionPlanner to intercept and log
AI-generated responses that might not go through the standard message handlers.
"""

import time
from typing import Any, Optional

from botbuilder.core import TurnContext
from loguru import logger
from teams.ai.planners import ActionPlanner
from teams.state import TurnState

from src.utils.ai_response_logger import ai_response_logger
from src.utils.logging_config import LoggingContext


class AuditActionPlanner:
    """
    Wrapper around ActionPlanner that captures AI responses for audit logging.

    This wrapper intercepts the plan generation process to log AI responses
    that are generated by the planner but might not go through the standard
    message handler decorators.
    """

    def __init__(self, original_planner: ActionPlanner):
        """
        Initialize the audit wrapper.

        Args:
            original_planner: The original ActionPlanner to wrap
        """
        self.original_planner = original_planner
        self._start_times = {}  # Track start times for duration calculation

    def __getattr__(self, name):
        """Delegate all other attributes to the original planner."""
        return getattr(self.original_planner, name)

    @property
    def options(self):
        """Delegate options property to original planner."""
        return self.original_planner.options

    async def begin_task(
        self,
        context: TurnContext,
        state: TurnState,
    ) -> Any:
        """
        Override begin_task to capture AI responses.

        Args:
            context: The turn context
            state: The turn state

        Returns:
            The plan generated by the AI
        """
        # Generate unique operation ID for tracking
        operation_id = f'ai_plan_{int(time.time() * 1000)}'
        start_time = time.time()
        self._start_times[operation_id] = start_time

        # Log the start of AI planning
        with LoggingContext(
            operation_id=operation_id,
            operation='ai_planning_start',
        ):
            logger.debug(f'AI planning started [id={operation_id}]')

        try:
            # Call the original begin_task method
            plan = await self.original_planner.begin_task(context, state)

            # Calculate duration
            duration_ms = (time.time() - start_time) * 1000

            # Extract AI response content from the plan
            ai_response_content = self._extract_ai_response_from_plan(plan)

            # Log the AI response if we have content
            if ai_response_content:
                # Get session information from state if available
                session_id = self._get_session_id_from_state(state)

                # Log the AI response
                await ai_response_logger.log_ai_response(
                    context=context,
                    response_content=ai_response_content,
                    response_type='ai_plan_response',
                    session_id=session_id,
                    duration_ms=duration_ms,
                    metadata={
                        'operation_id': operation_id,
                        'plan_type': type(plan).__name__,
                        'has_commands': bool(getattr(plan, 'commands', None)),
                    },
                )

                # Log success with structured context
                with LoggingContext(
                    operation_id=operation_id,
                    operation='ai_planning_complete',
                    duration_ms=duration_ms,
                    response_length=len(ai_response_content),
                ):
                    logger.info(
                        f'AI planning completed [id={operation_id}] in {duration_ms:.3f}ms: '
                        f'{ai_response_content[:100]}{"..." if len(ai_response_content) > 100 else ""}'
                    )
            else:
                # Log completion without response content
                with LoggingContext(
                    operation_id=operation_id,
                    operation='ai_planning_complete',
                    duration_ms=duration_ms,
                ):
                    logger.debug(
                        f'AI planning completed [id={operation_id}] in {duration_ms:.3f}ms (no response content)'
                    )

            # Clean up tracking
            self._start_times.pop(operation_id, None)

            return plan

        except Exception as e:
            # Calculate duration
            duration_ms = (time.time() - start_time) * 1000

            # Log the error
            await ai_response_logger.log_ai_error(
                context=context,
                error_message=str(e),
                error_type='ai_planning_error',
                session_id=self._get_session_id_from_state(state),
                duration_ms=duration_ms,
            )

            # Log error with structured context
            with LoggingContext(
                operation_id=operation_id,
                operation='ai_planning_error',
                duration_ms=duration_ms,
            ):
                logger.error(f'AI planning failed [id={operation_id}] after {duration_ms:.3f}ms: {e}')

            # Clean up tracking
            self._start_times.pop(operation_id, None)

            # Re-raise the exception
            raise

    def _extract_ai_response_from_plan(self, plan: Any) -> Optional[str]:
        """
        Extract AI response content from a plan.

        Args:
            plan: The plan generated by the AI

        Returns:
            The AI response content if available
        """
        try:
            # Try to extract response from various plan attributes
            if hasattr(plan, 'response') and plan.response:
                return str(plan.response)

            if hasattr(plan, 'message') and plan.message:
                return str(plan.message)

            if hasattr(plan, 'content') and plan.content:
                return str(plan.content)

            # Check for commands that might contain responses
            commands = getattr(plan, 'commands', None)
            if commands:
                responses = []
                for command in commands:
                    # Check response attribute
                    response = getattr(command, 'response', None)
                    if response and response is not None and str(response).strip():
                        responses.append(str(response))
                    else:
                        # Check message attribute
                        message = getattr(command, 'message', None)
                        if message and message is not None and str(message).strip():
                            responses.append(str(message))

                if responses:
                    return '\n'.join(responses)

            return None

        except Exception as e:
            logger.debug(f'Failed to extract AI response from plan: {e}')
            return None

    def _get_session_id_from_state(self, state: TurnState) -> Optional[str]:
        """
        Extract session ID from turn state.

        Args:
            state: The turn state

        Returns:
            The session ID if available
        """
        try:
            # Try to get session ID from various possible locations
            if hasattr(state, 'conversation') and hasattr(state.conversation, 'get'):
                return state.conversation.get('session_id')  # type: ignore

            if hasattr(state, 'user') and hasattr(state.user, 'get'):
                return state.user.get('session_id')  # type: ignore

            if hasattr(state, 'get'):
                return state.get('session_id')  # type: ignore

            return None

        except (AttributeError, TypeError):
            return None


def create_audit_planner(original_planner: ActionPlanner) -> AuditActionPlanner:
    """
    Create an audit-enabled ActionPlanner wrapper.

    Args:
        original_planner: The original ActionPlanner to wrap

    Returns:
        AuditActionPlanner wrapper that captures AI responses
    """
    return AuditActionPlanner(original_planner)
