[tool.poetry]
name = "ProjectManager"
version = "0.0.0"
description = "A AI-powered project management tool for Microsoft Teams."
authors = ["Microsoft <<EMAIL>>"]
readme = "README.md"
packages = [{ include = "src" }]

[tool.poetry.dependencies]
python = ">=3.9,<3.11"
python-dotenv = "^1.0.1"
requests-oauthlib = "^2.0.0"
pendulum = "^3.0.0"
icecream = "^2.1.4"
atlassian-python-api = "^4.0.3"
loguru = "^0.7.3"
jira = "^3.8.0"
teams-ai = "^1.8.0"
apscheduler = "^3.11.0"
pymssql = "^2.3.4"
pyodbc = "^5.2.0"
aioodbc = "^0.5.0"
SQLAlchemy = "^2.0.21"
azure-core = "1.31.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pylint = "^2.17.4"
pytest-cov = "^4.1.0"
pytest-asyncio = "^0.21.1"
black = "^24.3.0"
isort = "^5.12.0"
mypy = "^1.5.0"
jupyter = "^1.1.1"

[tool.poetry.scripts]
lint = "scripts:lint"
fmt = "scripts:fmt"
test = "scripts:test"
clean = "scripts:clean"
ci = "scripts:ci"
start = "scripts:start"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
target-version = ['py38']

[tool.isort]
profile = "black"

[tool.pytest.ini_options]
addopts = "--cov-report html:coverage --cov=src"

[tool.mypy]
python_version = "3.8"
ignore_missing_imports = true
show_error_codes = true
no_implicit_optional = true
warn_unused_ignores = true
