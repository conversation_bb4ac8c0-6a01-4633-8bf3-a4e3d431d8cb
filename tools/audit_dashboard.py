#!/usr/bin/env python3
"""
Simple audit dashboard for viewing audit data.

This script provides basic reporting and visualization of audit data
from the comprehensive audit mechanism.
"""

import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional

import pytz
from sqlalchemy import text

# Add src to path for imports
sys.path.insert(0, 'src')

from src.utils.sql.db_connection import DatabaseConnectionManager


class AuditDashboard:
    """
    Simple dashboard for viewing audit data.
    """
    
    def __init__(self):
        """Initialize the dashboard."""
        self.db_manager = DatabaseConnectionManager()
        self.sydney_tz = pytz.timezone('Australia/Sydney')
    
    def get_user_activity_summary(self, days: int = 7) -> List[Dict]:
        """
        Get user activity summary for the last N days.
        
        Args:
            days: Number of days to look back
            
        Returns:
            List of user activity summaries
        """
        try:
            with self.db_manager.session_scope() as session:
                result = session.execute(
                    text("""
                        SELECT 
                            user_id,
                            user_name,
                            COUNT(CASE WHEN event_type = 'user_message' THEN 1 END) as message_count,
                            COUNT(CASE WHEN event_type = 'action_execution' THEN 1 END) as action_count,
                            COUNT(CASE WHEN event_type = 'action_execution' AND event_status = 'failure' THEN 1 END) as error_count,
                            MIN(created_at) as first_activity,
                            MAX(created_at) as last_activity
                        FROM model.AuditLog 
                        WHERE created_at >= NOW() - INTERVAL :days DAY
                          AND user_id IS NOT NULL
                        GROUP BY user_id, user_name
                        ORDER BY last_activity DESC
                    """),
                    {"days": days}
                )
                
                return [dict(row._mapping) for row in result]
        except Exception as e:
            print(f"Error getting user activity: {e}")
            return []
    
    def get_action_performance(self, days: int = 7) -> List[Dict]:
        """
        Get action performance metrics for the last N days.
        
        Args:
            days: Number of days to look back
            
        Returns:
            List of action performance metrics
        """
        try:
            with self.db_manager.session_scope() as session:
                result = session.execute(
                    text("""
                        SELECT 
                            action_name,
                            COUNT(*) as total_executions,
                            COUNT(CASE WHEN event_status = 'success' THEN 1 END) as success_count,
                            COUNT(CASE WHEN event_status = 'failure' THEN 1 END) as failure_count,
                            ROUND(AVG(duration_ms), 2) as avg_duration_ms,
                            ROUND(MIN(duration_ms), 2) as min_duration_ms,
                            ROUND(MAX(duration_ms), 2) as max_duration_ms,
                            ROUND(COUNT(CASE WHEN event_status = 'failure' THEN 1 END) * 100.0 / COUNT(*), 2) as error_rate_percent
                        FROM model.AuditLog 
                        WHERE event_type = 'action_execution'
                          AND created_at >= NOW() - INTERVAL :days DAY
                          AND action_name IS NOT NULL
                        GROUP BY action_name
                        ORDER BY total_executions DESC
                    """),
                    {"days": days}
                )
                
                return [dict(row._mapping) for row in result]
        except Exception as e:
            print(f"Error getting action performance: {e}")
            return []
    
    def get_error_summary(self, days: int = 7) -> List[Dict]:
        """
        Get error summary for the last N days.
        
        Args:
            days: Number of days to look back
            
        Returns:
            List of error summaries
        """
        try:
            with self.db_manager.session_scope() as session:
                result = session.execute(
                    text("""
                        SELECT 
                            action_name,
                            error_message,
                            COUNT(*) as occurrence_count,
                            MAX(created_at) as last_occurrence,
                            GROUP_CONCAT(DISTINCT user_id) as affected_users
                        FROM model.AuditLog 
                        WHERE event_type = 'error'
                          AND created_at >= NOW() - INTERVAL :days DAY
                        GROUP BY action_name, error_message
                        ORDER BY occurrence_count DESC, last_occurrence DESC
                        LIMIT 20
                    """),
                    {"days": days}
                )
                
                return [dict(row._mapping) for row in result]
        except Exception as e:
            print(f"Error getting error summary: {e}")
            return []
    
    def get_daily_activity(self, days: int = 30) -> List[Dict]:
        """
        Get daily activity metrics for the last N days.
        
        Args:
            days: Number of days to look back
            
        Returns:
            List of daily activity metrics
        """
        try:
            with self.db_manager.session_scope() as session:
                result = session.execute(
                    text("""
                        SELECT 
                            DATE(created_at) as activity_date,
                            COUNT(DISTINCT user_id) as unique_users,
                            COUNT(CASE WHEN event_type = 'user_message' THEN 1 END) as total_messages,
                            COUNT(CASE WHEN event_type = 'action_execution' THEN 1 END) as total_actions,
                            COUNT(CASE WHEN event_type = 'action_execution' AND event_status = 'failure' THEN 1 END) as total_errors
                        FROM model.AuditLog 
                        WHERE created_at >= NOW() - INTERVAL :days DAY
                        GROUP BY DATE(created_at)
                        ORDER BY activity_date DESC
                    """),
                    {"days": days}
                )
                
                return [dict(row._mapping) for row in result]
        except Exception as e:
            print(f"Error getting daily activity: {e}")
            return []
    
    def get_session_metrics(self, days: int = 7) -> List[Dict]:
        """
        Get session metrics for the last N days.
        
        Args:
            days: Number of days to look back
            
        Returns:
            List of session metrics
        """
        try:
            with self.db_manager.session_scope() as session:
                result = session.execute(
                    text("""
                        SELECT 
                            user_id,
                            user_name,
                            COUNT(*) as session_count,
                            ROUND(AVG(message_count), 1) as avg_messages_per_session,
                            ROUND(AVG(action_count), 1) as avg_actions_per_session,
                            ROUND(AVG(session_duration_minutes), 1) as avg_session_duration_minutes,
                            MAX(last_activity_at) as last_session
                        FROM model.UserSession 
                        WHERE created_at >= NOW() - INTERVAL :days DAY
                        GROUP BY user_id, user_name
                        ORDER BY session_count DESC
                    """),
                    {"days": days}
                )
                
                return [dict(row._mapping) for row in result]
        except Exception as e:
            print(f"Error getting session metrics: {e}")
            return []
    
    def print_dashboard(self, days: int = 7):
        """
        Print a comprehensive dashboard to console.
        
        Args:
            days: Number of days to look back
        """
        print("=" * 80)
        print(f"🔍 AUDIT DASHBOARD - Last {days} Days")
        print("=" * 80)
        
        # User Activity Summary
        print("\n📊 USER ACTIVITY SUMMARY")
        print("-" * 50)
        user_activity = self.get_user_activity_summary(days)
        if user_activity:
            print(f"{'User':<20} {'Messages':<10} {'Actions':<10} {'Errors':<8} {'Last Activity':<20}")
            print("-" * 70)
            for user in user_activity[:10]:  # Top 10 users
                user_name = (user['user_name'] or user['user_id'] or 'Unknown')[:19]
                last_activity = user['last_activity'].strftime('%Y-%m-%d %H:%M') if user['last_activity'] else 'N/A'
                print(f"{user_name:<20} {user['message_count']:<10} {user['action_count']:<10} {user['error_count']:<8} {last_activity:<20}")
        else:
            print("No user activity data available")
        
        # Action Performance
        print("\n⚡ ACTION PERFORMANCE")
        print("-" * 50)
        action_perf = self.get_action_performance(days)
        if action_perf:
            print(f"{'Action':<25} {'Total':<8} {'Success':<8} {'Errors':<8} {'Avg Time':<10} {'Error %':<8}")
            print("-" * 75)
            for action in action_perf[:10]:  # Top 10 actions
                action_name = (action['action_name'] or 'Unknown')[:24]
                avg_time = f"{action['avg_duration_ms']:.1f}ms" if action['avg_duration_ms'] else 'N/A'
                error_rate = f"{action['error_rate_percent']:.1f}%" if action['error_rate_percent'] else '0%'
                print(f"{action_name:<25} {action['total_executions']:<8} {action['success_count']:<8} {action['failure_count']:<8} {avg_time:<10} {error_rate:<8}")
        else:
            print("No action performance data available")
        
        # Error Summary
        print("\n🚨 ERROR SUMMARY")
        print("-" * 50)
        errors = self.get_error_summary(days)
        if errors:
            print(f"{'Action':<20} {'Error':<30} {'Count':<8} {'Last Seen':<20}")
            print("-" * 80)
            for error in errors[:10]:  # Top 10 errors
                action_name = (error['action_name'] or 'System')[:19]
                error_msg = (error['error_message'] or 'Unknown error')[:29]
                last_seen = error['last_occurrence'].strftime('%Y-%m-%d %H:%M') if error['last_occurrence'] else 'N/A'
                print(f"{action_name:<20} {error_msg:<30} {error['occurrence_count']:<8} {last_seen:<20}")
        else:
            print("No error data available")
        
        # Daily Activity Trend
        print("\n📈 DAILY ACTIVITY TREND")
        print("-" * 50)
        daily_activity = self.get_daily_activity(min(days, 14))  # Last 14 days max
        if daily_activity:
            print(f"{'Date':<12} {'Users':<8} {'Messages':<10} {'Actions':<10} {'Errors':<8}")
            print("-" * 50)
            for day in daily_activity:
                date_str = day['activity_date'].strftime('%Y-%m-%d') if day['activity_date'] else 'N/A'
                print(f"{date_str:<12} {day['unique_users']:<8} {day['total_messages']:<10} {day['total_actions']:<10} {day['total_errors']:<8}")
        else:
            print("No daily activity data available")
        
        # Session Metrics
        print("\n🔄 SESSION METRICS")
        print("-" * 50)
        session_metrics = self.get_session_metrics(days)
        if session_metrics:
            print(f"{'User':<20} {'Sessions':<10} {'Avg Msgs':<10} {'Avg Actions':<12} {'Avg Duration':<12}")
            print("-" * 70)
            for session in session_metrics[:10]:  # Top 10 users by sessions
                user_name = (session['user_name'] or session['user_id'] or 'Unknown')[:19]
                avg_duration = f"{session['avg_session_duration_minutes']:.1f}m" if session['avg_session_duration_minutes'] else 'N/A'
                print(f"{user_name:<20} {session['session_count']:<10} {session['avg_messages_per_session']:<10} {session['avg_actions_per_session']:<12} {avg_duration:<12}")
        else:
            print("No session metrics data available")
        
        print("\n" + "=" * 80)
        print(f"Dashboard generated at: {datetime.now(self.sydney_tz).strftime('%Y-%m-%d %H:%M:%S %Z')}")
        print("=" * 80)


def main():
    """Main function to run the dashboard."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Audit Dashboard')
    parser.add_argument('--days', type=int, default=7, help='Number of days to look back (default: 7)')
    parser.add_argument('--format', choices=['console', 'json'], default='console', help='Output format')
    
    args = parser.parse_args()
    
    dashboard = AuditDashboard()
    
    if args.format == 'console':
        dashboard.print_dashboard(args.days)
    elif args.format == 'json':
        import json
        data = {
            'user_activity': dashboard.get_user_activity_summary(args.days),
            'action_performance': dashboard.get_action_performance(args.days),
            'error_summary': dashboard.get_error_summary(args.days),
            'daily_activity': dashboard.get_daily_activity(args.days),
            'session_metrics': dashboard.get_session_metrics(args.days),
        }
        print(json.dumps(data, default=str, indent=2))


if __name__ == "__main__":
    main()
