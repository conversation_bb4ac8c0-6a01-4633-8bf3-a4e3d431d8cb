-- ============================================================================
-- Development SQL Script for FinanceDB Bot Framework
-- ============================================================================
-- Purpose: Development queries and maintenance operations for the bot framework
-- Database: FinanceDB
-- Schema: model
-- Created: For development and debugging purposes
-- ============================================================================

-- ============================================================================
-- SECTION 1: TABLE EXISTENCE CHECK
-- ============================================================================
-- Check which tables exist in the database before running queries
-- Check if audit tables exist

-- Check if AuditLog table exists
IF EXISTS (
    SELECT 1
    FROM INFORMATION_SCHEMA.TABLES
    WHERE TABLE_SCHEMA = 'model'
    AND TABLE_NAME = 'AuditLog'
)
BEGIN
    PRINT 'AuditLog table exists in model schema'
END
ELSE
BEGIN
    PRINT 'AuditLog table does NOT exist in model schema - run audit migration first'
END

-- Check if UserSession table exists
IF EXISTS (
    SELECT 1
    FROM INFORMATION_SCHEMA.TABLES
    WHERE TABLE_SCHEMA = 'model'
    AND TABLE_NAME = 'UserSession'
)
BEGIN
    PRINT 'UserSession table exists in model schema'
END
ELSE
BEGIN
    PRINT 'UserSession table does NOT exist in model schema - run audit migration first'
END

-- Check if PMATokens table exists
IF EXISTS (
    SELECT 1
    FROM INFORMATION_SCHEMA.TABLES
    WHERE TABLE_SCHEMA = 'model'
    AND TABLE_NAME = 'PMATokens'
)
BEGIN
    PRINT 'PMATokens table exists in model schema'
END
ELSE
BEGIN
    PRINT 'PMATokens table does NOT exist in model schema'
END

-- Summary of all tables in model schema
SELECT
    TABLE_NAME
    ,TABLE_SCHEMA
    ,'EXISTS' AS STATUS
FROM
    INFORMATION_SCHEMA.TABLES
WHERE
    TABLE_SCHEMA = 'model'
    AND TABLE_NAME IN ('PMATokens', 'AuditLog', 'UserSession')
ORDER BY
    TABLE_NAME;


-- ============================================================================
-- SECTION 2: DATA INSPECTION QUERIES
-- ============================================================================
-- These queries are used to inspect the current state of tables during development

-- Query 1: Inspect PMA Tokens
-- Purpose: View authentication tokens stored in the system
-- Usage: Check token status, expiration, and associated cloud instances
SELECT
    TOP (1000)
    user_id
    ,user_name
    ,auth_token
    ,refresh_token
    ,cloud_id
    ,expire_at
    ,created_at
    ,updated_at
FROM
    [FinanceDB].[model].[PMATokens]
ORDER BY
    created_at DESC;

-- Query 2: Inspect Audit Log
-- Purpose: View system audit trail for debugging and monitoring
-- Usage: Track user interactions, bot actions, and system events
-- NOTE: This table may not exist yet - run audit migration first if needed

SELECT
    TOP (1000)
    id
    ,event_id
    ,event_type
    ,event_status
    ,user_id
    ,user_name
    ,session_id
    ,request_id
    ,activity_id
    ,action_name
    ,message_content
    ,error_message
    ,created_at
FROM
    [FinanceDB].[model].[AuditLog]
ORDER BY
    created_at DESC;


-- Query 3: Inspect User Sessions
-- Purpose: View active and historical user sessions
-- Usage: Monitor user activity and session management
-- NOTE: This table may not exist yet - run audit migration first if needed

SELECT
    TOP (1000)
    id
    ,session_id
    ,user_id
    ,user_name
    ,channel_id
    ,conversation_id
    ,message_count
    ,action_count
    ,error_count
    ,first_activity_at
    ,last_activity_at
    ,session_duration_minutes
    ,created_at
    ,updated_at
FROM
    [FinanceDB].[model].[UserSession]
ORDER BY
    created_at DESC;


-- ============================================================================
-- SECTION 3: DEVELOPMENT MAINTENANCE OPERATIONS
-- ============================================================================
-- WARNING: These operations modify or delete data - use with caution!
-- Uncomment only when needed for development/testing

-- Operation 1: Clear PMA Tokens table
-- Purpose: Remove all authentication tokens (useful for testing)
-- WARNING: This will log out all users and require re-authentication
-- TRUNCATE TABLE [FinanceDB].[model].[PMATokens];

-- Operation 2: Drop PMA Tokens table
-- Purpose: Completely remove the table structure (for schema changes)
-- WARNING: This will permanently delete the table and all data
-- DROP TABLE [FinanceDB].[model].[PMATokens];

-- ============================================================================
-- SECTION 4: USEFUL DEVELOPMENT QUERIES
-- ============================================================================

-- Query: Count records in each table
-- Purpose: Quick overview of data volume in each table
/*
SELECT 'PMATokens' AS table_name, COUNT(*) AS record_count
FROM [FinanceDB].[model].[PMATokens]
UNION ALL
SELECT 'AuditLog' AS table_name, COUNT(*) AS record_count
FROM [FinanceDB].[model].[AuditLog]
UNION ALL
SELECT 'UserSession' AS table_name, COUNT(*) AS record_count
FROM [FinanceDB].[model].[UserSession];
*/

-- Query: Find expired tokens
-- Purpose: Identify tokens that need refresh
/*
SELECT
    user_id,
    user_name,
    cloud_id,
    expire_at,
    DATEDIFF(minute, GETUTCDATE(), expire_at) AS minutes_until_expiry
FROM
    [FinanceDB].[model].[PMATokens]
WHERE
    expire_at < GETUTCDATE()
ORDER BY
    expire_at DESC;
*/

-- Query: Recent audit events by type
-- Purpose: Monitor specific types of system events
/*
SELECT
    event_type,
    COUNT(*) AS event_count,
    MAX(created_at) AS last_occurrence
FROM
    [FinanceDB].[model].[AuditLog]
WHERE
    created_at >= DATEADD(day, -7, GETUTCDATE())
GROUP BY
    event_type
ORDER BY
    event_count DESC;
*/

-- ============================================================================
-- SECTION 5: AUDIT TABLE CREATION COMMANDS
-- ============================================================================
-- Use these commands to create audit tables if they don't exist
-- NOTE: It's recommended to use the Python migration script instead

-- Create AuditLog table (if needed)
/*
-- Run this Python command instead:
-- python -m src.utils.sql.audit_migration
*/

-- ============================================================================
-- SECTION 6: HELPFUL DEVELOPMENT TIPS
-- ============================================================================

-- To create audit tables, run:
-- python -m src.utils.sql.audit_migration

-- To check current database connection:
-- SELECT @@SERVERNAME AS ServerName, DB_NAME() AS DatabaseName;

-- To check current user:
-- SELECT SYSTEM_USER AS CurrentUser, USER_NAME() AS DatabaseUser;

-- To see all tables in the model schema:
-- SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = 'model';

-- To see column information for a specific table:
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
-- FROM INFORMATION_SCHEMA.COLUMNS
-- WHERE TABLE_SCHEMA = 'model' AND TABLE_NAME = 'PMATokens'
-- ORDER BY ORDINAL_POSITION;